# Context
File name: 2025-04-13_3_friend-groups.md
Created at: 2025-04-13_00:00:00
Created by: contre
Main branch: main
Task Branch: task/friend-groups_2025-04-13_1
Yolo Mode: Ask

# Task Description
Implement a 'Friend Groups' feature with role-based access control (<PERSON><PERSON>, Collaborator, View-Only Guest). <PERSON><PERSON> create groups, manage data, invite collaborators (who can also manage data), and share read-only links for non-users.

# Project Overview
Amateur soccer match statistics application. Core entities include Users, Matches, and Player Stats. Currently, all registered users can view and add data globally across the entire application. There is no existing 'groups' functionality or fine-grained access control. Data is not segregated. The goal is to partition data by 'Friend Group' and introduce access control based on roles within each group.

⚠️ WARNING: NEVER MODIFY THIS SECTION ⚠️
# Execution Protocol:

## 1. Create feature branch
1. Create a new task branch from [MAIN_BRANCH]:
  ```
  git checkout -b task/[TASK_IDENTIFIER]_[TASK_DATE_AND_NUMBER]
  ```
2. Add the branch name to the [TASK_FILE] under "Task Branch."
3. Verify the branch is active:
  ```
  git branch --show-current
  ```
4. Update "Current execution step" in [TASK_FILE] to next step

## 2. Create the task file
1. Execute command to generate [TASK_FILE_NAME]:
   ```
   [TASK_FILE_NAME]="$(date +%Y-%m-%d)_$(($(ls -1q .tasks | grep -c $(date +%Y-%m-%d)) + 1))"
   ```
2. Create [TASK_FILE] with strict naming:
   ```
   mkdir -p .tasks && touch ".tasks/${TASK_FILE_NAME}_[TASK_IDENTIFIER].md"
   ```
3. Verify file creation:
   ```
   ls -la ".tasks/${TASK_FILE_NAME}_[TASK_IDENTIFIER].md"
   ```
4. Copy ENTIRE Task File Template into new file
5. Insert Execution Protocol EXACTLY, in verbatim, by:
-   - Copying text between "-- [START OF EXECUTION PROTOCOL]" and "-- [END OF EXECUTION PROTOCOL]"
-   - Adding "⚠️ WARNING: NEVER MODIFY THIS SECTION ⚠️" both as header and a footer
+   a. Find the protocol content between [START OF EXECUTION PROTOCOL] and [END OF EXECUTION PROTOCOL] markers above
+   b. In the task file:
+      1. Replace "[FULL EXECUTION PROTOCOL COPY]" with the ENTIRE protocol content from step 5a
+      2. Keep the warning header and footer: "⚠️ WARNING: NEVER MODIFY THIS SECTION ⚠️"
6. Systematically populate ALL placeholders:
   a. Run commands for dynamic values:
      ```
      [DATETIME]="$(date +'%Y-%m-%d_%H:%M:%S')"
      [USER_NAME]="$(whoami)"
      [TASK_BRANCH]="$(git branch --show-current)"
      ```
   b. Fill [PROJECT_OVERVIEW] by recursively analyzing mentioned files:
      ```
      find [PROJECT_ROOT] -type f -exec cat {} + | analyze_dependencies
      ```
7. Cross-verify completion:
   - Check ALL template sections exist
   - Confirm NO existing task files were modified
8. Set the "Current execution step" tp the name and number of the next planned step of the exectution protocol
9. Print full task file contents for verification

<<< HALT IF NOT [YOLO_MODE]: Confirm [TASK_FILE] with user before proceeding >>>

## 3. Analysis
1. Analyze code related to [TASK]:
  - Identify core files/functions
  - Trace code flow
2. Document findings in "Analysis" section
3. Set the "Current execution step" tp the name and number of the next planned step of the exectution protocol

<<< HALT IF NOT [YOLO_MODE]: Wait for analysis confirmation >>>

## 4. Proposed Solution
1. Create plan based on analysis:
  - Research dependencies
  - Add to "Proposed Solution"
2. NO code changes yet
3. Set the "Current execution step" tp the name and number of the next planned step of the exectution protocol

<<< HALT IF NOT [YOLO_MODE]: Get solution approval >>>

## 5. Iterate on the task
1. Review "Task Progress" history
2. Plan next changes
3. Present for approval:
  ```
  [CHANGE PLAN]
  - Files: [CHANGED_FILES]
  - Rationale: [EXPLANATION]
  ```
4. If approved:
  - Implement changes
  - Append to "Task Progress":
    ```
    [DATETIME]
    - Modified: [list of files and code changes]
    - Changes: [the changes made as a summary]
    - Reason: [reason for the changes]
    - Blockers: [list of blockers preventing this update from being successful]
    - Status: [UNCONFIRMED|SUCCESSFUL|UNSUCCESSFUL]
    ```
5. Ask user: "Status: SUCCESSFUL/UNSUCCESSFUL?"
6. If UNSUCCESSFUL: Repeat from 5.1
7. If SUCCESSFUL:
  a. Commit? → `git add [FILES] && git commit -m "[SHORT_MSG]"`
  b. More changes? → Repeat step 5
  c. Continue? → Proceed
8. Set the "Current execution step" tp the name and number of the next planned step of the exectution protocol

## 6. Task Completion
1. Stage changes (exclude task files):
  ```
  git add --all :!.tasks/*
  ```
2. Commit with message:
  ```
  git commit -m "[COMMIT_MESSAGE]"
  ```
3. Set the "Current execution step" tp the name and number of the next planned step of the exectution protocol

<<< HALT IF NOT [YOLO_MODE]: Confirm merge with [MAIN_BRANCH] >>>

## 7. Merge Task Branch
1. Merge explicitly:
  ```
  git checkout [MAIN_BRANCH]
  git merge task/[TASK_IDENTIFIER]_[TASK_DATE_AND_NUMBER]
  ```
2. Verify merge:
  ```
  git diff [MAIN_BRANCH] task/[TASK_IDENTIFIER]_[TASK_DATE_AND_NUMBER]
  ```
3. Set the "Current execution step" tp the name and number of the next planned step of the exectution protocol

## 8. Delete Task Branch
1. Delete if approved:
  ```
  git branch -d task/[TASK_IDENTIFIER]_[TASK_DATE_AND_NUMBER]
  ```
2. Set the "Current execution step" tp the name and number of the next planned step of the exectution protocol

## 9. Final Review
1. Complete "Final Review" after user confirmation
2. Set step to "All done!"

[END OF EXECUTION PROTOCOL]
⚠️ WARNING: NEVER MODIFY THIS SECTION ⚠️

# Analysis

## Key Areas for Implementation

1. **Authentication and Authorization**:
   - `src/lib/auth.ts`: Likely handles user authentication.
   - `src/context/AuthContext.tsx`: Manages user context, essential for role-based access control.

2. **Database Integration**:
   - `supabase/migrations/`: Contains SQL migrations for database schema updates.
   - `supabase/`: Likely includes database interaction logic.

3. **UI Components**:
   - `src/components/`: Contains reusable components for building group management interfaces.
   - `src/components/ui/`: Includes UI elements like buttons, dialogs, and forms.

4. **Pages**:
   - `src/pages/DashboardPage.tsx`: May need updates to display group-specific data.
   - `src/pages/ProfilePage.tsx`: Could include group management options.

## Next Steps
- Trace the code flow in the identified files to understand how to integrate the new functionality.
- Plan database schema changes for "Friend Groups" and roles.

# Proposed Solution

## Database Schema Updates
1. Create a new table `friend_groups`:
   - Columns: `id`, `name`, `created_by`, `created_at`.
   - Purpose: Store group metadata.

2. Create a new table `group_members`:
   - Columns: `id`, `group_id`, `user_id`, `role` (Admin, Collaborator, Guest).
   - Purpose: Manage group membership and roles.

3. Update existing tables (e.g., `matches`, `player_stats`) to include a `group_id` column:
   - Purpose: Partition data by group.

## Backend Logic
1. Add Supabase policies for role-based access control:
   - Admins: Full access to group data.
   - Collaborators: Can manage data but not invite others.
   - Guests: Read-only access.

2. Create API endpoints:
   - `POST /groups`: Create a new group.
   - `POST /groups/:id/invite`: Invite users to a group.
   - `GET /groups/:id`: Fetch group details and members.
   - `PATCH /groups/:id`: Update group details.

## UI Changes
1. Add a "Groups" section to the Dashboard:
   - Display a list of groups the user belongs to.
   - Include options to create a new group or manage existing ones.

2. Create a "Group Management" page:
   - Allow Admins to invite users and assign roles.
   - Display group members and their roles.

3. Update existing pages (e.g., Matches, Players):
   - Filter data by the selected group.

## Next Steps
- Implement database schema changes and Supabase policies.
- Develop backend API endpoints.
- Update the UI to support group management and role-based access control.

# Current execution step: "5. Iterate on the task"

# Task Progress
2025-04-13
- Modified: src/pages/GroupSelectionPage.tsx, src/main.tsx, src/pages/DashboardPage.tsx, src/pages/PlayersPage.tsx, supabase/migrations/20240413000002_create_friend_groups.sql, src/lib/api/groups.ts
- Changes: Implemented the 'Group Selection' page, updated navigation flow, added modal for creating groups, filtered data by selected group, added RLS policies, and implemented API functions for group management.
- Reason: To implement the 'Friend Groups' feature as described in the task file.
- Blockers: None
- Status: SUCCESSFUL

# Task Progress
[Change history with timestamps]

# Final Review:
[Post-completion summary]