# Fixing CORS Issues for Production Deployment

You're experiencing a CORS (Cross-Origin Resource Sharing) error when trying to log in to your application in production, even though it works locally. This guide will help you fix this issue.

## The Problem

Your application works fine locally but fails with CORS errors when deployed to Vercel. This is because you need to add your production domain to the allowed CORS origins in your Supabase project.

## How to Fix It

### 1. Update Supabase CORS Settings for Production

1. Go to the [Supabase Dashboard](https://app.supabase.com/)
2. Select your project (`jbljfsvvskbbyxftqlkg`)
3. Go to Project Settings > API
4. Scroll down to the "CORS" section (this is different from the redirect URLs section)
5. Add your production URLs to the "Additional allowed origins" field:
   - `https://soccer-ui-arena-contre98s-projects.vercel.app`
   - `https://soccer-ui-arena.vercel.app`

![CORS Settings Example](https://supabase.com/docs/img/guides/auth/cors-settings.png)

### 2. Important Notes About CORS Settings

- CORS settings are different from redirect URLs
- You need to add the exact domain without any path or wildcards
- Do not include trailing slashes
- Make sure to include both the protocol (https://) and the full domain

### 3. After Updating CORS Settings

After updating the CORS settings, you should:

1. Wait a few minutes for the changes to propagate
2. Clear your browser cache
3. Try logging in again on your production site

## Still Having Issues?

If you're still experiencing CORS issues after following these steps, try the following:

1. Check the browser console for the exact CORS error message
2. Verify that the URL in the error message matches the URL you added to the allowed origins
3. Make sure there are no typos in the URLs you added
4. Try using a different browser to rule out browser-specific issues

## Additional Resources

- [Supabase Auth Documentation](https://supabase.com/docs/guides/auth)
- [CORS on MDN](https://developer.mozilla.org/en-US/docs/Web/HTTP/CORS)
