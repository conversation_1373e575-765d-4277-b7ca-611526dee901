# Fixing CORS Issues with Supabase Authentication

You're experiencing a CORS (Cross-Origin Resource Sharing) error when trying to log in to your application. This guide will help you fix this issue.

## What is CORS?

CORS is a security feature implemented by browsers that restricts web pages from making requests to a different domain than the one that served the web page. This is a security measure to prevent malicious websites from making unauthorized requests to other websites on behalf of the user.

## The Error

```
Cross-Origin Request Blocked: The Same Origin Policy disallows reading the remote resource at https://ahicrznvbaqvaromqjko.supabase.co/auth/v1/token?grant_type=password. (Reason: CORS request did not succeed). Status code: (null).
```

This error occurs because your browser is blocking the request to the Supabase authentication endpoint due to CORS restrictions.

## How to Fix It

### 1. Update Supabase CORS Settings

1. Go to the [Supabase Dashboard](https://app.supabase.com/)
2. Select your project (`jbljfsvvskbbyxftqlkg`)
3. Go to Project Settings > API
4. Scroll down to the "CORS" section
5. Add your application's URL to the "Additional allowed origins" field:
   - For local development: `http://localhost:5173` (based on your Vite config)
   - If you're using a different port, adjust accordingly
   - If you're deploying to a production environment, add that URL as well (e.g., `https://your-app.vercel.app`)

![Supabase CORS Settings](https://supabase.com/docs/img/guides/auth/cors-settings.png)

### 2. Verify Your Environment Variables

Make sure your environment variables are correctly set in your `.env` file:

```
VITE_SUPABASE_URL=https://jbljfsvvskbbyxftqlkg.supabase.co
VITE_SUPABASE_ANON_KEY=your-anon-key
```

### 3. Restart Your Development Server

After making these changes, restart your development server:

```bash
npm run dev
```

### 4. Clear Browser Cache and Cookies

Sometimes, browsers cache CORS errors. Clear your browser cache and cookies for your development domain.

### 5. Test Authentication Again

Try logging in again to see if the CORS issue is resolved.

## Still Having Issues?

If you're still experiencing CORS issues after following these steps, try the following:

1. Check if your Supabase project is in the correct region
2. Ensure your Supabase project is active and not in maintenance mode
3. Try using a different browser to rule out browser-specific issues
4. Check if there are any network issues or firewalls blocking the requests

## Additional Resources

- [Supabase Auth Documentation](https://supabase.com/docs/guides/auth)
- [CORS on MDN](https://developer.mozilla.org/en-US/docs/Web/HTTP/CORS)
