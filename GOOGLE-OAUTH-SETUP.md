# Setting Up Google OAuth for Fulbito Stats

This guide will walk you through setting up Google OAuth for your Fulbito Stats application.

## Step 1: Create OAuth Credentials in Google Cloud Console

1. Go to the [Google Cloud Console](https://console.cloud.google.com/)
2. Create a new project or select an existing one
3. Navigate to "APIs & Services" > "Credentials"
4. Click "Configure Consent Screen"
   - Select "External" user type (unless you have a Google Workspace organization)
   - Fill in the required information:
     - App name: "Fulbito Stats"
     - User support email: Your email
     - Developer contact information: Your email
   - Click "Save and Continue"
   - Add scopes: Select ".../auth/userinfo.email" and ".../auth/userinfo.profile"
   - Click "Save and Continue"
   - Add test users if needed (for development)
   - Click "Save and Continue"
5. Go back to "Credentials" and click "Create Credentials" > "OAuth client ID"
   - Application type: "Web application"
   - Name: "Fulbito Stats Web Client"
   - Authorized JavaScript origins:
     - Add your local development URL: `http://localhost:3000`
     - Add your production URL: `https://fulbitostats.ar` (or your actual domain)
   - Authorized redirect URIs:
     - Add your Supabase redirect URL: `https://jbljfsvvskbbyxftqlkg.supabase.co/auth/v1/callback`
     - Add your local redirect URL: `http://localhost:3000/dashboard`
     - Add your production redirect URL: `https://fulbitostats.ar/dashboard`
   - Click "Create"
6. Note down the Client ID and Client Secret that are generated

## Step 2: Configure Google OAuth in Supabase

1. Go to your [Supabase Dashboard](https://app.supabase.com/)
2. Select your project
3. Navigate to "Authentication" > "Providers"
4. Find "Google" in the list and click on it
5. Enable the provider by toggling the switch
6. Enter the Client ID and Client Secret from Google Cloud Console
7. Save the changes

## Step 3: Update Your Application's Redirect URLs

Make sure your application's redirect URLs match what you've configured in Google Cloud Console:

1. In your Supabase project settings, go to "Authentication" > "URL Configuration"
2. Set the Site URL to your production URL (e.g., `https://fulbitostats.ar`)
3. Add additional redirect URLs if needed:
   - `http://localhost:3000/dashboard` (for development)
   - `https://fulbitostats.ar/dashboard` (for production)

## Step 4: Test Your Google Sign-In

1. Run your application locally or deploy it
2. Try signing in with Google
3. You should be redirected to Google's authentication page and then back to your application

## Troubleshooting

If you encounter the "Error 401: invalid_client" error:

1. Double-check that your Client ID and Client Secret are correctly entered in Supabase
2. Verify that your redirect URIs are correctly configured in both Google Cloud Console and Supabase
3. Make sure your application is using the correct Supabase URL and API key
4. Check that your Google Cloud project has the OAuth consent screen properly configured
5. Ensure the Google OAuth API is enabled in your Google Cloud project

For more detailed information, refer to:
- [Supabase Auth Documentation](https://supabase.com/docs/guides/auth)
- [Google OAuth Documentation](https://developers.google.com/identity/protocols/oauth2)
