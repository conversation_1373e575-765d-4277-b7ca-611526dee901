# Fulbito Stats

![Fulbito Stats](/public/assets/logo.svg)

## Overview

Fulbito Stats is a comprehensive web application designed to help soccer teams and groups track player statistics, match results, team chemistry, and generate balanced teams. The application provides a user-friendly interface for managing all aspects of recreational soccer games, with a focus on group-based organization and detailed player analytics.

### What Makes This Project Special

- **Group-Based Organization**: Manage multiple soccer groups with separate statistics and players
- **Chemistry Analysis**: Unique feature that analyzes which players perform best together as duos, trios, and quads
- **Smart Team Generator**: Create balanced teams based on player ratings and chemistry
- **Shareable Stats**: Generate view-only links to share with friends without requiring login
- **Comprehensive Player Ratings**: Track skills, effort, and stamina for each player
- **Multi-Admin Support**: Different permission levels for group administrators
- **Progressive Web App**: Install and use as a native-like app on mobile devices

## Features

### Core Features

- **User Authentication**: Secure login and signup system with Supabase Auth
- **Group Management**: Create and manage multiple soccer groups with different admin roles
- **Dashboard**: Overview of key statistics, recent matches, and best chemistry combinations
- **Player Management**: Track player skills, effort, and stamina ratings
- **Match Tracking**: Record match results, scores, and goalscorers
- **Leaderboard**: View player performance statistics and rankings with customizable filters
- **Team Chemistry Analysis**: Analyze which players perform best together in different combinations
- **Team Generator**: Create balanced teams based on player ratings with warnings for imbalanced teams
- **Shareable Stats**: Generate view-only links to share with friends without requiring login
- **Dark/Light Mode**: Toggle between dark and light themes with consistent styling
- **Feedback System**: Integrated feedback button linking to a Google Form
- **Support Options**: Integration with Cafecito for donations
- **Multilingual Support**: Toggle between Spanish and English interfaces

### User Experience Features

- **Responsive Design**: Optimized for both desktop and mobile devices
- **Offline Support**: Basic functionality available without internet connection
- **Installable App**: Can be installed as a PWA on mobile and desktop devices
- **Accessibility Improvements**: Focus on making the app usable for everyone
- **Performance Optimizations**: Fast loading times and smooth interactions

### Technical Features

- **Real-time Data Updates**: Instant updates with Supabase Realtime
- **Row-Level Security**: Secure data access with Supabase RLS policies
- **Modern UI Components**: Built with shadcn-ui and Tailwind CSS
- **State Management**: React Context API and TanStack Query for efficient data fetching
- **Analytics Integration**: Usage tracking with Vercel Analytics

## Screenshots

*Screenshots of the application will be added here. Consider adding images of:*

- *Dashboard view showing recent matches and chemistry stats*
- *Team generator in action*
- *Leaderboard with player rankings*
- *Mobile view of the application*
- *Dark and light theme comparison*

## Technology Stack

- **Frontend**: React 18, TypeScript
- **Styling**: Tailwind CSS, shadcn-ui components
- **State Management**: React Context API, TanStack Query
- **Backend/Database**: Supabase (PostgreSQL, Auth, Storage, Realtime)
- **Build Tool**: Vite
- **Deployment**: Vercel
- **PWA Support**: Workbox, vite-plugin-pwa
- **Internationalization**: i18next
- **Analytics**: Vercel Analytics

## Getting Started

### Prerequisites

- Node.js (v20 or higher)
- npm or yarn
- Supabase account

### Installation

```bash
# Clone the repository
git clone <repository-url>

# Navigate to the project directory
cd fulbito-stats

# Install dependencies
npm install

# Start the development server
npm run dev
```

### Environment Setup

Create a `.env` file in the root directory with the following variables:

```
VITE_SUPABASE_URL=your_supabase_url
VITE_SUPABASE_ANON_KEY=your_supabase_anon_key
VITE_RESEND_API_KEY=your_resend_api_key (optional, for email functionality)
```

### Supabase Setup

1. Create a new Supabase project
2. Run the SQL migrations found in the `supabase/migrations` directory
3. Set up the Row Level Security policies as described in `docs/rls_complete_rebuild.md`
4. Configure CORS settings for your domain as described in `CORS-SETUP-GUIDE.md`

## Deployment

### Deploying to Vercel

1. Push your code to a GitHub repository
2. Import the repository in Vercel
3. Set the environment variables in the Vercel dashboard
4. Deploy the application

### CORS Configuration for Production

Make sure to add your production domain to the allowed CORS origins in your Supabase project settings. See `CORS-PRODUCTION-GUIDE.md` for detailed instructions.

## Usage

1. **Sign Up/Login**: Create an account or log in
2. **Create or Join a Group**: Set up a new soccer group or join an existing one
3. **Add Players**: Enter player information and ratings
4. **Record Matches**: Track match results and player performances
5. **Generate Teams**: Use the team generator for balanced matchups
6. **Analyze Stats**: View leaderboards and chemistry data
7. **Share Stats**: Generate and share view-only links with friends

## Contributing

Contributions are welcome! Please feel free to submit a Pull Request.

1. Fork the repository
2. Create your feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add some amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## License

This project is proprietary software.

## Acknowledgments

- All the recreational soccer players who inspired this project
- The open-source community for the amazing tools and libraries
- Supabase for providing an excellent backend platform
- Vercel for hosting and deployment services
