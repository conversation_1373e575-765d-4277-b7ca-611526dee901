# Shared Links Feature Setup

This document provides instructions for setting up the shared links feature, which is currently experiencing issues.

## Issues Identified

1. **Missing Database Table**: The `shared_links` table does not exist in the Supabase database.
2. **Focus Management Issues**: There are infinite recursion issues in focus management components.
3. **API Integration**: API calls are failing due to the missing table.

## Solution Steps

### 1. Create the Missing `shared_links` Table

You can create the required table using one of the following methods:

#### Option A: Run the SQL Script Directly in Supabase Dashboard

1. Log in to your Supabase dashboard at https://app.supabase.com
2. Navigate to your project
3. Go to the SQL Editor
4. Copy the contents of `create_shared_links_table.sql` and run it

#### Option B: Use the Node.js Script

1. Make sure your `.env` file contains the correct Supabase URL and service role key
2. Run the script:
   ```bash
   node create_shared_links_table.js
   ```

#### Option C: Deploy and Run the Edge Function

1. Deploy the Edge Function:
   ```bash
   npx supabase functions deploy create-shared-links-table
   ```
2. Invoke the function with proper authentication:
   ```bash
   curl -i --location --request POST 'https://jbljfsvvskbbyxftqlkg.supabase.co/functions/v1/create-shared-links-table' \
     --header "Authorization: Bearer YOUR_AUTH_TOKEN" \
     --header 'Content-Type: application/json'
   ```

### 2. Fix Focus Management Issues

The ShareManager component has been updated to handle the missing table gracefully. Additionally, an ErrorBoundary wrapper has been created to prevent cascading failures.

To use the error boundary:

1. Import the wrapped component instead of the original:
   ```jsx
   import ShareManagerWithErrorBoundary from '@/components/sharing/ShareManagerWithErrorBoundary';
   ```

2. Use it in your components:
   ```jsx
   <ShareManagerWithErrorBoundary groupId={groupId} groupName={groupName} />
   ```

### 3. API Integration Fixes

The ShareManager component has been updated to handle API failures gracefully:

- It checks if the table exists before attempting operations
- It provides user-friendly error messages
- It prevents infinite recursion by properly handling errors

## Verification

After implementing these fixes, you should:

1. Verify the `shared_links` table exists in your Supabase database
2. Confirm the ShareManager component loads without errors
3. Test creating, updating, and deleting shared links

## Troubleshooting

If you continue to experience issues:

1. Check the browser console for specific error messages
2. Verify your Supabase permissions and RLS policies
3. Ensure your authentication is working correctly
4. Check that all required environment variables are set correctly

For persistent issues, consider temporarily disabling the sharing feature until all components are properly set up.
