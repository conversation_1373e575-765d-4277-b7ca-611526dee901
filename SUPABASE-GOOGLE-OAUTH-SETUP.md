# Setting Up Google OAuth in Supabase

This guide will walk you through the process of setting up Google OAuth in your Supabase project to fix the "Error 401: invalid_client" issue.

## Step 1: Create Google OAuth Credentials

1. Go to the [Google Cloud Console](https://console.cloud.google.com/)
2. Create a new project or select an existing one
3. Navigate to "APIs & Services" > "Credentials"
4. Click "Configure Consent Screen"
   - Select "External" user type
   - Fill in the required information:
     - App name: "Fulbito Stats"
     - User support email: Your email
     - Developer contact information: Your email
   - Click "Save and Continue"
   - Add scopes: Select ".../auth/userinfo.email" and ".../auth/userinfo.profile"
   - Click "Save and Continue"
   - Add test users if needed (for development)
   - Click "Save and Continue"
5. Go back to "Credentials" and click "Create Credentials" > "OAuth client ID"
   - Application type: "Web application"
   - Name: "Fulbito Stats Web Client"
   - Authorized JavaScript origins:
     - Add your local development URL: `http://localhost:3000`
     - Add your production URL: `https://fulbitostats.ar` (or your actual domain)
   - Authorized redirect URIs:
     - Add your Supabase redirect URL: `https://jbljfsvvskbbyxftqlkg.supabase.co/auth/v1/callback`
     - Add your local redirect URL: `http://localhost:3000/dashboard`
     - Add your production redirect URL: `https://fulbitostats.ar/dashboard`
   - Click "Create"
6. Note down the Client ID and Client Secret that are generated

## Step 2: Configure Google OAuth in Supabase

1. Go to your [Supabase Dashboard](https://app.supabase.com/)
2. Select your project
3. Navigate to "Authentication" > "Providers"
4. Find "Google" in the list and click on it
5. Enable the provider by toggling the switch
6. Enter the Client ID and Client Secret from Google Cloud Console
7. Save the changes

## Step 3: Update Your Redirect URLs in Supabase

1. In your Supabase project settings, go to "Authentication" > "URL Configuration"
2. Set the Site URL to your production URL (e.g., `https://fulbitostats.ar`)
3. Add additional redirect URLs:
   - `http://localhost:3000/dashboard` (for development)
   - `https://fulbitostats.ar/dashboard` (for production)

## Step 4: Verify Your Environment Variables

Make sure your environment variables are correctly set in your `.env` file:

```
VITE_SUPABASE_URL=https://jbljfsvvskbbyxftqlkg.supabase.co
VITE_SUPABASE_ANON_KEY=your-anon-key
```

## Step 5: Test Your Google Sign-In

1. Run your application locally or deploy it
2. Try signing in with Google
3. You should be redirected to Google's authentication page and then back to your application

## Troubleshooting the "Error 401: invalid_client" Issue

If you're still seeing the "Error 401: invalid_client" error, check the following:

1. **Verify Client ID and Client Secret**: Make sure you've entered the correct Client ID and Client Secret in Supabase.

2. **Check Authorized Domains**: Ensure that the domain you're testing from is listed in the Authorized JavaScript origins in Google Cloud Console.

3. **Verify Redirect URIs**: Make sure all your redirect URIs are correctly configured in both Google Cloud Console and Supabase.

4. **Check API Enabled**: Make sure the "Google Identity" API is enabled in your Google Cloud project.

5. **Verify OAuth Consent Screen**: Make sure your OAuth consent screen is properly configured and published.

6. **Check for Typos**: Double-check for any typos in your Client ID, Client Secret, or redirect URIs.

7. **Clear Browser Cache**: Sometimes, browsers cache OAuth errors. Try clearing your browser cache or using an incognito window.

8. **Check Console Logs**: Look at your browser console logs for more detailed error information.

## Additional Resources

- [Supabase Auth Documentation](https://supabase.com/docs/guides/auth)
- [Google OAuth Documentation](https://developers.google.com/identity/protocols/oauth2)
- [Google Cloud Console](https://console.cloud.google.com/)
