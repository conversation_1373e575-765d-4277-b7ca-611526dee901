-- Add email_invite and status columns to group_members table
ALTER TABLE public.group_members
ADD COLUMN IF NOT EXISTS email_invite TEXT,
ADD COLUMN IF NOT EXISTS status TEXT DEFAULT 'active' CHECK (status IN ('active', 'pending', 'declined'));

-- Update the user_id column to be nullable
ALTER TABLE public.group_members
ALTER COLUMN user_id DROP NOT NULL;

-- Add a constraint to ensure either user_id or email_invite is provided
ALTER TABLE public.group_members
DROP CONSTRAINT IF EXISTS user_id_or_email_invite_required;

ALTER TABLE public.group_members
ADD CONSTRAINT user_id_or_email_invite_required
CHECK (
  (user_id IS NOT NULL AND email_invite IS NULL) OR
  (user_id IS NULL AND email_invite IS NOT NULL)
);
