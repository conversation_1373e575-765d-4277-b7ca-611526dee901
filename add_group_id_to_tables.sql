-- First, check if the tables exist
DO $$
BEGIN
    -- Check if players table exists
    IF EXISTS (SELECT FROM pg_tables WHERE schemaname = 'public' AND tablename = 'players') THEN
        -- Add group_id column to players table if it doesn't exist
        IF NOT EXISTS (SELECT FROM information_schema.columns 
                      WHERE table_schema = 'public' 
                      AND table_name = 'players' 
                      AND column_name = 'group_id') THEN
            ALTER TABLE public.players 
            ADD COLUMN group_id UUID REFERENCES friend_groups(id) ON DELETE CASCADE;
            
            -- Create index for better performance
            CREATE INDEX IF NOT EXISTS players_group_id_idx ON public.players(group_id);
            
            -- Enable Row Level Security
            ALTER TABLE public.players ENABLE ROW LEVEL SECURITY;
            
            -- Create policies
            CREATE POLICY "Users can view players in their groups" ON public.players
            FOR SELECT USING (
              group_id IN (
                SELECT group_id FROM group_members WHERE user_id = auth.uid()
                UNION
                SELECT id FROM friend_groups WHERE created_by = auth.uid()
              )
            );
            
            CREATE POLICY "Users can modify players in their groups" ON public.players
            FOR ALL USING (
              group_id IN (
                SELECT group_id FROM group_members WHERE user_id = auth.uid()
                UNION
                SELECT id FROM friend_groups WHERE created_by = auth.uid()
              )
            );
            
            RAISE NOTICE 'Added group_id column to players table';
        ELSE
            RAISE NOTICE 'group_id column already exists in players table';
        END IF;
    ELSE
        RAISE NOTICE 'players table does not exist';
    END IF;
    
    -- Check if matches table exists
    IF EXISTS (SELECT FROM pg_tables WHERE schemaname = 'public' AND tablename = 'matches') THEN
        -- Add group_id column to matches table if it doesn't exist
        IF NOT EXISTS (SELECT FROM information_schema.columns 
                      WHERE table_schema = 'public' 
                      AND table_name = 'matches' 
                      AND column_name = 'group_id') THEN
            ALTER TABLE public.matches 
            ADD COLUMN group_id UUID REFERENCES friend_groups(id) ON DELETE CASCADE;
            
            -- Create index for better performance
            CREATE INDEX IF NOT EXISTS matches_group_id_idx ON public.matches(group_id);
            
            -- Enable Row Level Security
            ALTER TABLE public.matches ENABLE ROW LEVEL SECURITY;
            
            -- Create policies
            CREATE POLICY "Users can view matches in their groups" ON public.matches
            FOR SELECT USING (
              group_id IN (
                SELECT group_id FROM group_members WHERE user_id = auth.uid()
                UNION
                SELECT id FROM friend_groups WHERE created_by = auth.uid()
              )
            );
            
            CREATE POLICY "Users can modify matches in their groups" ON public.matches
            FOR ALL USING (
              group_id IN (
                SELECT group_id FROM group_members WHERE user_id = auth.uid()
                UNION
                SELECT id FROM friend_groups WHERE created_by = auth.uid()
              )
            );
            
            RAISE NOTICE 'Added group_id column to matches table';
        ELSE
            RAISE NOTICE 'group_id column already exists in matches table';
        END IF;
    ELSE
        RAISE NOTICE 'matches table does not exist';
    END IF;
    
    -- Check if chemistry table exists
    IF EXISTS (SELECT FROM pg_tables WHERE schemaname = 'public' AND tablename = 'chemistry') THEN
        -- Add group_id column to chemistry table if it doesn't exist
        IF NOT EXISTS (SELECT FROM information_schema.columns 
                      WHERE table_schema = 'public' 
                      AND table_name = 'chemistry' 
                      AND column_name = 'group_id') THEN
            ALTER TABLE public.chemistry 
            ADD COLUMN group_id UUID REFERENCES friend_groups(id) ON DELETE CASCADE;
            
            -- Create index for better performance
            CREATE INDEX IF NOT EXISTS chemistry_group_id_idx ON public.chemistry(group_id);
            
            -- Enable Row Level Security
            ALTER TABLE public.chemistry ENABLE ROW LEVEL SECURITY;
            
            -- Create policies
            CREATE POLICY "Users can view chemistry in their groups" ON public.chemistry
            FOR SELECT USING (
              group_id IN (
                SELECT group_id FROM group_members WHERE user_id = auth.uid()
                UNION
                SELECT id FROM friend_groups WHERE created_by = auth.uid()
              )
            );
            
            CREATE POLICY "Users can modify chemistry in their groups" ON public.chemistry
            FOR ALL USING (
              group_id IN (
                SELECT group_id FROM group_members WHERE user_id = auth.uid()
                UNION
                SELECT id FROM friend_groups WHERE created_by = auth.uid()
              )
            );
            
            RAISE NOTICE 'Added group_id column to chemistry table';
        ELSE
            RAISE NOTICE 'group_id column already exists in chemistry table';
        END IF;
    ELSE
        RAISE NOTICE 'chemistry table does not exist';
    END IF;
END
$$;
