import pandas as pd
import json
from datetime import datetime

# Load the Excel file
excel_file = 'docs/fstatsog.xlsx'
print(f"Reading Excel file: {excel_file}")

# Get sheet names
xl = pd.ExcelFile(excel_file)
sheet_names = xl.sheet_names
print(f"Sheet names: {sheet_names}")

# Read each sheet and print basic info
for sheet_name in sheet_names:
    print(f"\n--- Sheet: {sheet_name} ---")
    df = pd.read_excel(excel_file, sheet_name=sheet_name)
    print(f"Shape: {df.shape}")
    print("First few rows:")
    print(df.head())
    print("Column names:")
    print(df.columns.tolist())

# Try to identify players and matches data
players_data = None
matches_data = None

# Check if there's a sheet that looks like player data
for sheet_name in sheet_names:
    df = pd.read_excel(excel_file, sheet_name=sheet_name)
    if 'Name' in df.columns or 'name' in df.columns or 'NOMBRE' in df.columns:
        print(f"\nPossible players sheet found: {sheet_name}")
        players_data = df
        break

# Check if there's a sheet that looks like match data
for sheet_name in sheet_names:
    df = pd.read_excel(excel_file, sheet_name=sheet_name)
    if 'Date' in df.columns or 'date' in df.columns or 'FECHA' in df.columns:
        print(f"\nPossible matches sheet found: {sheet_name}")
        matches_data = df
        break

print("\nAnalysis complete.")
