-- Create friend_groups table
CREATE TABLE IF NOT EXISTS friend_groups (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name TEXT NOT NULL,
    created_by UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    created_at TIMESTAMP DEFAULT now()
);

-- Enable RLS for friend_groups table
ALTER TABLE friend_groups ENABLE ROW LEVEL SECURITY;

-- Policy: Allow group creators to manage their groups
CREATE POLICY "Group Creator Full Access"
ON friend_groups
FOR ALL
USING (auth.uid() = created_by);

-- Create group_members table
CREATE TABLE IF NOT EXISTS group_members (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    group_id UUID REFERENCES friend_groups(id) ON DELETE CASCADE,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    role TEXT CHECK (role IN ('Admin', 'Collaborator', 'Guest')) NOT NULL,
    created_at TIMESTAMP DEFAULT now()
);

-- Enable RLS for group_members table
ALTER TABLE group_members ENABLE ROW LEVEL SECURITY;

-- Add group_id column to players table if it doesn't exist
ALTER TABLE IF EXISTS public.players 
ADD COLUMN IF NOT EXISTS group_id UUID REFERENCES friend_groups(id) ON DELETE CASCADE;

-- Add group_id column to matches table if it doesn't exist
ALTER TABLE IF EXISTS public.matches 
ADD COLUMN IF NOT EXISTS group_id UUID REFERENCES friend_groups(id) ON DELETE CASCADE;

-- Add group_id column to chemistry table if it doesn't exist
ALTER TABLE IF EXISTS public.chemistry 
ADD COLUMN IF NOT EXISTS group_id UUID REFERENCES friend_groups(id) ON DELETE CASCADE;

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS players_group_id_idx ON public.players(group_id);
CREATE INDEX IF NOT EXISTS matches_group_id_idx ON public.matches(group_id);
CREATE INDEX IF NOT EXISTS chemistry_group_id_idx ON public.chemistry(group_id);

-- Enable Row Level Security on these tables
ALTER TABLE public.players ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.matches ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.chemistry ENABLE ROW LEVEL SECURITY;

-- Create match_comments table
CREATE TABLE IF NOT EXISTS public.match_comments (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    match_id BIGINT REFERENCES public.matches(id) ON DELETE CASCADE,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    content TEXT NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now()) NOT NULL,
    group_id UUID REFERENCES friend_groups(id) ON DELETE CASCADE
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS match_comments_match_id_idx ON public.match_comments(match_id);
CREATE INDEX IF NOT EXISTS match_comments_user_id_idx ON public.match_comments(user_id);
CREATE INDEX IF NOT EXISTS match_comments_group_id_idx ON public.match_comments(group_id);

-- Enable RLS for match_comments table
ALTER TABLE public.match_comments ENABLE ROW LEVEL SECURITY;

-- Create user_preferences table
CREATE TABLE IF NOT EXISTS public.user_preferences (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
    theme TEXT DEFAULT 'system' CHECK (theme IN ('light', 'dark', 'system')),
    dashboard_layout JSONB DEFAULT '{}',
    default_group_id UUID REFERENCES friend_groups(id) ON DELETE SET NULL,
    notification_settings JSONB DEFAULT '{"email": true, "browser": true}',
    display_settings JSONB DEFAULT '{"compactView": false, "showAvatars": true, "animationsEnabled": true}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now()) NOT NULL,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now()) NOT NULL,
    UNIQUE(user_id)
);

-- Create index for better performance
CREATE INDEX IF NOT EXISTS user_preferences_user_id_idx ON public.user_preferences(user_id);

-- Enable Row Level Security
ALTER TABLE public.user_preferences ENABLE ROW LEVEL SECURITY;

-- Create policies to restrict access to user's own preferences
CREATE POLICY "Users can view their own preferences" ON public.user_preferences
FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own preferences" ON public.user_preferences
FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own preferences" ON public.user_preferences
FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete their own preferences" ON public.user_preferences
FOR DELETE USING (auth.uid() = user_id);

-- Create function to automatically update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
   NEW.updated_at = NOW();
   RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger to update updated_at timestamp
CREATE TRIGGER update_user_preferences_updated_at
BEFORE UPDATE ON public.user_preferences
FOR EACH ROW
EXECUTE FUNCTION update_updated_at_column();
