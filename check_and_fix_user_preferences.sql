-- Check if user_preferences table exists
DO $$
DECLARE
  table_exists BO<PERSON>EAN;
BEGIN
  SELECT EXISTS (
    SELECT FROM information_schema.tables 
    WHERE table_schema = 'public'
    AND table_name = 'user_preferences'
  ) INTO table_exists;
  
  IF table_exists THEN
    RAISE NOTICE 'user_preferences table exists';
  ELSE
    RAISE NOTICE 'user_preferences table does NOT exist';
  END IF;
END;
$$;

-- Check RLS policies for user_preferences
SELECT 
  schemaname, 
  tablename, 
  policyname, 
  permissive, 
  roles, 
  cmd, 
  qual, 
  with_check
FROM 
  pg_policies 
WHERE 
  tablename = 'user_preferences';

-- Check if the table has the expected columns
SELECT 
  column_name, 
  data_type, 
  is_nullable
FROM 
  information_schema.columns
WHERE 
  table_schema = 'public' 
  AND table_name = 'user_preferences'
ORDER BY 
  ordinal_position;

-- Check if there are any rows in the table
SELECT COUNT(*) FROM public.user_preferences;

-- Fix RLS policies if needed
DO $$
BEGIN
  -- Drop existing policies
  DROP POLICY IF EXISTS "Users can view their own preferences" ON public.user_preferences;
  DROP POLICY IF EXISTS "Users can insert their own preferences" ON public.user_preferences;
  DROP POLICY IF EXISTS "Users can update their own preferences" ON public.user_preferences;
  DROP POLICY IF EXISTS "Users can delete their own preferences" ON public.user_preferences;
  
  -- Recreate policies
  CREATE POLICY "Users can view their own preferences" ON public.user_preferences
    FOR SELECT USING (auth.uid() = user_id);
  
  CREATE POLICY "Users can insert their own preferences" ON public.user_preferences
    FOR INSERT WITH CHECK (auth.uid() = user_id);
  
  CREATE POLICY "Users can update their own preferences" ON public.user_preferences
    FOR UPDATE USING (auth.uid() = user_id);
  
  CREATE POLICY "Users can delete their own preferences" ON public.user_preferences
    FOR DELETE USING (auth.uid() = user_id);
    
  RAISE NOTICE 'RLS policies recreated successfully';
END;
$$;

-- Make sure RLS is enabled
ALTER TABLE public.user_preferences ENABLE ROW LEVEL SECURITY;
