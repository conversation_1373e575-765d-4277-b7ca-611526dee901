-- Check tables and their columns
SELECT 
    table_name,
    column_name,
    data_type,
    is_nullable
FROM 
    information_schema.columns
WHERE 
    table_schema = 'public'
    AND table_name IN ('players', 'matches', 'chemistry', 'friend_groups', 'group_members')
ORDER BY 
    table_name, ordinal_position;

-- Check if RLS is enabled
SELECT
    tablename,
    rowsecurity
FROM
    pg_tables
WHERE
    schemaname = 'public'
    AND tablename IN ('players', 'matches', 'chemistry', 'friend_groups', 'group_members');

-- Check policies
SELECT
    schemaname,
    tablename,
    policyname,
    permissive,
    roles,
    cmd,
    qual
FROM
    pg_policies
WHERE
    schemaname = 'public'
    AND tablename IN ('players', 'matches', 'chemistry', 'friend_groups', 'group_members')
ORDER BY
    tablename, policyname;
