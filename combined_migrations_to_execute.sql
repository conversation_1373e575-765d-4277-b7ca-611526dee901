-- Create a function to check if a table exists
CREATE OR REPLACE FUNCTION public.check_table_exists(table_name text)
RETURNS boolean
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  table_exists boolean;
BEGIN
  SELECT EXISTS (
    SELECT FROM information_schema.tables 
    WHERE table_schema = 'public'
    AND table_name = $1
  ) INTO table_exists;
  
  RETURN table_exists;
END;
$$;

-- <PERSON> execute permission to anon and authenticated roles
GRANT EXECUTE ON FUNCTION public.check_table_exists TO anon;
GRANT EXECUTE ON FUNCTION public.check_table_exists TO authenticated;

-- Create a function to execute SQL statements with admin privileges
-- This function should only be callable by service role or admin users
CREATE OR REPLACE FUNCTION public.exec_sql(sql text)
RETURNS void
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  EXECUTE sql;
END;
$$;

-- Restrict access to this function to only service role
R<PERSON>VOKE ALL ON FUNCTION public.exec_sql FROM PUBLIC;
<PERSON><PERSON><PERSON><PERSON> ALL ON FUNCTION public.exec_sql FROM anon;
<PERSON><PERSON><PERSON><PERSON> ALL ON FUNCTION public.exec_sql FROM authenticated;
