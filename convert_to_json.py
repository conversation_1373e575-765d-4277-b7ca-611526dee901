import pandas as pd
import json
from datetime import datetime, timezone
import re

# Load the Excel file
excel_file = 'docs/fstatsog.xlsx'
print(f"Reading Excel file: {excel_file}")

# Function to extract player names from a team string
def extract_players(team_str):
    if pd.isna(team_str):
        return []
    # Split by comma and clean up whitespace
    players = [p.strip() for p in team_str.split(',')]
    return [p for p in players if p]  # Remove empty strings

# Function to parse date from various formats
def parse_date(date_str):
    if pd.isna(date_str):
        return None
    
    # Try to extract date from formats like "viernes 23 agosto"
    date_pattern = r'(\d+)\s+(\w+)'
    match = re.search(date_pattern, date_str)
    
    if match:
        day = int(match.group(1))
        month_str = match.group(2).lower()
        
        # Map Spanish month names to numbers
        month_map = {
            'enero': 1, 'febrero': 2, 'marzo': 3, 'abril': 4,
            'mayo': 5, 'junio': 6, 'julio': 7, 'agosto': 8,
            'septiembre': 9, 'octubre': 10, 'noviembre': 11, 'diciembre': 12
        }
        
        # Get year from context (use 2024 as default)
        year = 2024
        
        if month_str in month_map:
            month = month_map[month_str]
            # Create ISO format date string
            return f"{year}-{month:02d}-{day:02d}T18:00:00Z"
    
    # If we couldn't parse the date, return a placeholder
    return "2024-01-01T18:00:00Z"

# Read the Data sheet for matches
matches_df = pd.read_excel(excel_file, sheet_name='Data')
print(f"Matches data shape: {matches_df.shape}")

# Read player stats from Calculos sheet
players_df = pd.read_excel(excel_file, sheet_name='Calculos')
print(f"Players data shape: {players_df.shape}")

# Extract unique player names from all sheets
all_players = set()

# Extract from matches
for _, row in matches_df.iterrows():
    team1 = extract_players(row['Equipo 1'])
    team2 = extract_players(row['Equipo 2'])
    all_players.update(team1)
    all_players.update(team2)

# Extract from player stats
if 'Jugadores' in players_df.columns:
    for player in players_df['Jugadores']:
        if not pd.isna(player):
            all_players.add(player.strip())

# Create player objects with default stats
players_json = []
for player_name in sorted(all_players):
    if player_name:  # Skip empty names
        # Try to find player stats in the Calculos sheet
        player_stats = players_df[players_df['Jugadores'] == player_name]
        
        # Default stats
        skills = 75
        effort = 75
        stamina = 75
        
        # If we found stats, use them
        if not player_stats.empty:
            # We could calculate skills based on win rate if available
            win_rate_col = 'Win Rate'
            if win_rate_col in player_stats.columns and not pd.isna(player_stats[win_rate_col].iloc[0]):
                win_rate = player_stats[win_rate_col].iloc[0]
                if isinstance(win_rate, (int, float)):
                    # Scale win rate (0-1) to skills (50-100)
                    skills = int(50 + (win_rate * 50))
        
        players_json.append({
            "name": player_name,
            "skills": skills,
            "effort": effort,
            "stamina": stamina
        })

print(f"Extracted {len(players_json)} unique players")

# Create a map of player names to their index in the players array
player_indices = {player["name"]: i for i, player in enumerate(players_json)}

# Process matches
matches_json = []
for _, row in matches_df.iterrows():
    # Skip rows without valid data
    if pd.isna(row['Fecha']):
        continue
    
    # Extract teams
    team1 = extract_players(row['Equipo 1'])
    team2 = extract_players(row['Equipo 2'])
    
    # Skip matches without players
    if not team1 or not team2:
        continue
    
    # Parse date
    match_date = parse_date(row['Fecha'])
    
    # Determine winner
    winner = None
    if not pd.isna(row['Ganador']):
        winner_players = extract_players(row['Ganador'])
        # Check if winner players are more in team1 or team2
        team1_winners = sum(1 for p in winner_players if p in team1)
        team2_winners = sum(1 for p in winner_players if p in team2)
        
        if team1_winners > team2_winners:
            winner = "A"
        elif team2_winners > team1_winners:
            winner = "B"
        else:
            winner = "Draw"
    
    # Convert player names to indices
    team1_indices = [player_indices.get(p) for p in team1 if p in player_indices]
    team2_indices = [player_indices.get(p) for p in team2 if p in player_indices]
    
    # Remove None values (players not found in the player list)
    team1_indices = [i for i in team1_indices if i is not None]
    team2_indices = [i for i in team2_indices if i is not None]
    
    # Create match object
    match = {
        "match_date": match_date,
        "teama": team1_indices,
        "teamb": team2_indices,
        "scorea": 1 if winner == "A" else 0,
        "scoreb": 1 if winner == "B" else 0,
        "winner": winner if winner else "Draw",
        "goalscorers": [],
        "youtubelink": ""
    }
    
    matches_json.append(match)

print(f"Extracted {len(matches_json)} matches")

# Create the final JSON structure
import_data = {
    "players": players_json,
    "matches": matches_json
}

# Save to file
output_file = 'fulbito_import.json'
with open(output_file, 'w', encoding='utf-8') as f:
    json.dump(import_data, f, indent=2, ensure_ascii=False)

print(f"Data saved to {output_file}")

# Print a sample of the data
print("\nSample of players:")
for player in players_json[:5]:
    print(f"  {player['name']}: Skills={player['skills']}, Effort={player['effort']}, Stamina={player['stamina']}")

print("\nSample of matches:")
for match in matches_json[:3]:
    print(f"  Date: {match['match_date']}")
    print(f"  Team A: {match['teama']}")
    print(f"  Team B: {match['teamb']}")
    print(f"  Score: {match['scorea']} - {match['scoreb']}")
    print(f"  Winner: {match['winner']}")
    print()
