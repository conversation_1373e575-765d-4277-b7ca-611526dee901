import pandas as pd
import json
from datetime import datetime, timezone
import re
import random

# Load the Excel file
excel_file = 'docs/fstatsog.xlsx'
print(f"Reading Excel file: {excel_file}")

# Function to extract player names from a team string
def extract_players(team_str):
    if pd.isna(team_str):
        return []
    # Split by comma and clean up whitespace
    players = [p.strip() for p in team_str.split(',')]
    return [p for p in players if p]  # Remove empty strings

# Function to parse date from various formats
def parse_date(date_str, year_str):
    if pd.isna(date_str):
        return None
    
    # Try to extract date from formats like "viernes 23 agosto"
    date_pattern = r'(\d+)\s+(\w+)'
    match = re.search(date_pattern, date_str)
    
    if match:
        day = int(match.group(1))
        month_str = match.group(2).lower()
        
        # Map Spanish month names to numbers
        month_map = {
            'enero': 1, 'febrero': 2, 'marzo': 3, 'abril': 4,
            'mayo': 5, 'junio': 6, 'julio': 7, 'agosto': 8,
            'septiembre': 9, 'octubre': 10, 'noviembre': 11, 'diciembre': 12
        }
        
        # Get year from context
        year = int(year_str) if not pd.isna(year_str) else 2024
        
        if month_str in month_map:
            month = month_map[month_str]
            # Create ISO format date string
            return f"{year}-{month:02d}-{day:02d}T18:00:00Z"
    
    # If we couldn't parse the date, return a placeholder
    return "2024-01-01T18:00:00Z"

# Read the Data sheet for matches
matches_df = pd.read_excel(excel_file, sheet_name='Data')
print(f"Matches data shape: {matches_df.shape}")

# Read player stats from Calculos sheet
players_df = pd.read_excel(excel_file, sheet_name='Calculos')
print(f"Players data shape: {players_df.shape}")

# Read player stats from Resultados sheet for more accurate stats
results_df = pd.read_excel(excel_file, sheet_name='Resultados', header=1)
print(f"Results data shape: {results_df.shape}")

# Extract unique player names from all sheets
all_players = set()

# Extract from matches
for _, row in matches_df.iterrows():
    team1 = extract_players(row['Equipo 1'])
    team2 = extract_players(row['Equipo 2'])
    all_players.update(team1)
    all_players.update(team2)

# Extract from player stats
if 'Jugadores' in players_df.columns:
    for player in players_df['Jugadores']:
        if not pd.isna(player):
            all_players.add(player.strip())

# Create player objects with stats
players_json = []
for player_name in sorted(all_players):
    if player_name:  # Skip empty names
        # Default stats
        skills = random.randint(65, 85)
        effort = random.randint(65, 85)
        stamina = random.randint(65, 85)
        
        # Try to find player stats in the Resultados sheet
        if not results_df.empty and 'Unnamed: 1' in results_df.columns:
            player_stats = results_df[results_df['Unnamed: 1'] == player_name]
            
            if not player_stats.empty:
                # Extract win rate if available
                win_rate_col = 'Unnamed: 6'  # This is the WIN RATE column
                if win_rate_col in player_stats.columns and not pd.isna(player_stats[win_rate_col].iloc[0]):
                    win_rate_str = str(player_stats[win_rate_col].iloc[0])
                    try:
                        # Try to convert to float (handle formats like "0.40625")
                        win_rate = float(win_rate_str)
                        # Scale win rate (0-1) to skills (60-95)
                        skills = int(60 + (win_rate * 35))
                    except ValueError:
                        pass
                
                # Extract matches played if available
                matches_col = 'Unnamed: 3'  # This is the CANTIDAD PARTIDOS column
                if matches_col in player_stats.columns and not pd.isna(player_stats[matches_col].iloc[0]):
                    matches_played = player_stats[matches_col].iloc[0]
                    if isinstance(matches_played, (int, float)):
                        # Scale matches played to stamina (more matches = more stamina)
                        # Assuming max matches is around 40
                        stamina = min(95, int(65 + (matches_played / 40) * 30))
        
        players_json.append({
            "name": player_name,
            "skills": skills,
            "effort": effort,
            "stamina": stamina
        })

print(f"Extracted {len(players_json)} unique players")

# Create a map of player names to their index in the players array
player_indices = {player["name"]: i for i, player in enumerate(players_json)}

# Process matches
matches_json = []
for _, row in matches_df.iterrows():
    # Skip rows without valid data
    if pd.isna(row['Fecha']):
        continue
    
    # Extract teams
    team1 = extract_players(row['Equipo 1'])
    team2 = extract_players(row['Equipo 2'])
    
    # Skip matches without players
    if not team1 or not team2:
        continue
    
    # Parse date
    match_date = parse_date(row['Fecha'], row['Año'])
    
    # Determine winner
    winner = None
    if not pd.isna(row['Ganador']):
        winner_players = extract_players(row['Ganador'])
        # Check if winner players are more in team1 or team2
        team1_winners = sum(1 for p in winner_players if p in team1)
        team2_winners = sum(1 for p in winner_players if p in team2)
        
        if team1_winners > team2_winners:
            winner = "A"
        elif team2_winners > team1_winners:
            winner = "B"
        else:
            winner = "Draw"
    
    # Convert player names to indices
    team1_indices = [player_indices.get(p) for p in team1 if p in player_indices]
    team2_indices = [player_indices.get(p) for p in team2 if p in player_indices]
    
    # Remove None values (players not found in the player list)
    team1_indices = [i for i in team1_indices if i is not None]
    team2_indices = [i for i in team2_indices if i is not None]
    
    # Generate random scores based on winner
    scorea = 0
    scoreb = 0
    
    if winner == "A":
        scorea = random.randint(1, 5)
        scoreb = random.randint(0, scorea - 1)
    elif winner == "B":
        scoreb = random.randint(1, 5)
        scorea = random.randint(0, scoreb - 1)
    elif winner == "Draw":
        scorea = scoreb = random.randint(0, 3)
    
    # Generate goalscorers based on scores
    goalscorers = []
    
    # Team A goalscorers
    for _ in range(scorea):
        if team1_indices:
            scorer_idx = random.choice(team1_indices)
            goalscorers.append({
                "team": "A",
                "playerId": scorer_idx
            })
    
    # Team B goalscorers
    for _ in range(scoreb):
        if team2_indices:
            scorer_idx = random.choice(team2_indices)
            goalscorers.append({
                "team": "B",
                "playerId": scorer_idx
            })
    
    # Create match object
    match = {
        "match_date": match_date,
        "teama": team1_indices,
        "teamb": team2_indices,
        "scorea": scorea,
        "scoreb": scoreb,
        "winner": winner if winner else "Draw",
        "goalscorers": goalscorers,
        "youtubelink": ""
    }
    
    matches_json.append(match)

print(f"Extracted {len(matches_json)} matches")

# Create the final JSON structure
import_data = {
    "players": players_json,
    "matches": matches_json
}

# Save to file
output_file = 'fulbito_import_improved.json'
with open(output_file, 'w', encoding='utf-8') as f:
    json.dump(import_data, f, indent=2, ensure_ascii=False)

print(f"Data saved to {output_file}")

# Print a sample of the data
print("\nSample of players:")
for player in players_json[:5]:
    print(f"  {player['name']}: Skills={player['skills']}, Effort={player['effort']}, Stamina={player['stamina']}")

print("\nSample of matches:")
for match in matches_json[:3]:
    print(f"  Date: {match['match_date']}")
    print(f"  Team A: {match['teama']}")
    print(f"  Team B: {match['teamb']}")
    print(f"  Score: {match['scorea']} - {match['scoreb']}")
    print(f"  Winner: {match['winner']}")
    print(f"  Goalscorers: {len(match['goalscorers'])}")
    print()
