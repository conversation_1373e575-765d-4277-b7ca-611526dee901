import fs from 'fs';

// Read the file with player names
const data = JSON.parse(fs.readFileSync('converted_fulbito_stats_with_names.json', 'utf8'));

// Create a new data structure
const newData = {
  players: data.players,
  matches: []
};

// Process each match to use player names instead of indices
data.matches.forEach(match => {
  // Get the player names for each team
  const teamaNames = match.teamaNames;
  const teambNames = match.teambNames;
  
  // Create a new match object with player names
  const newMatch = {
    match_date: match.match_date,
    teamaByName: teamaNames,
    teambByName: teambNames,
    scorea: match.scorea,
    scoreb: match.scoreb,
    winner: match.winner,
    goalscorers: match.goalscorers || [],
    youtubelink: match.youtubelink || ""
  };
  
  // Add the match to the new data
  newData.matches.push(newMatch);
});

// Save the new file
fs.writeFileSync('converted_fulbito_stats_name_based.json', JSON.stringify(newData, null, 2));

console.log('Created new file: converted_fulbito_stats_name_based.json');
console.log('This file uses player names instead of indices for team references.');
console.log('You will need to modify the import process to handle this format.');

// Print a sample of the first match for verification
if (newData.matches.length > 0) {
  console.log('\nSample of first match:');
  const firstMatch = newData.matches[0];
  console.log(`Match date: ${firstMatch.match_date}`);
  console.log('Team A:');
  firstMatch.teamaByName.forEach(name => {
    console.log(`  ${name}`);
  });
  console.log('Team B:');
  firstMatch.teambByName.forEach(name => {
    console.log(`  ${name}`);
  });
}
