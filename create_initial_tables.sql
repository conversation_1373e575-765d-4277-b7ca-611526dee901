-- Create players table if it doesn't exist
CREATE TABLE IF NOT EXISTS players (
    id BIGINT GENERATED BY DEFAULT AS IDENTITY PRIMARY KEY,
    name TEXT NOT NULL,
    skills INTEGER NOT NULL,
    effort INTEGER NOT NULL,
    stamina INTEGER NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now()) NOT NULL
);

-- Create matches table if it doesn't exist
CREATE TABLE IF NOT EXISTS matches (
    id BIGINT GENERATED BY DEFAULT AS IDENTITY PRIMARY KEY,
    match_date TIMESTAMP WITH TIME ZONE NOT NULL,
    teama INTEGER[] NOT NULL,
    teamb INTEGER[] NOT NULL,
    scorea INTEGER,
    scoreb INTEGER,
    winner TEXT CHECK (winner IN ('A', 'B', 'Draw')),
    goalscorers <PERSON><PERSON><PERSON><PERSON>,
    youtubelink TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now()) NOT NULL
);

-- Create chemistry table if it doesn't exist
CREATE TABLE IF NOT EXISTS chemistry (
    id BIGINT GENERATED BY DEFAULT AS IDENTITY PRIMARY KEY,
    player1_id BIGINT REFERENCES players(id) NOT NULL,
    player2_id BIGINT REFERENCES players(id) NOT NULL,
    games_together INTEGER DEFAULT 0,
    wins_together INTEGER DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now()) NOT NULL,
    UNIQUE(player1_id, player2_id)
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS chemistry_player1_id_idx ON chemistry(player1_id);
CREATE INDEX IF NOT EXISTS chemistry_player2_id_idx ON chemistry(player2_id);
