-- Create match_comments table
CREATE TABLE IF NOT EXISTS public.match_comments (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    match_id BIGINT REFERENCES public.matches(id) ON DELETE CASCADE,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    content TEXT NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now()) NOT NULL,
    group_id UUID REFERENCES friend_groups(id) ON DELETE CASCADE
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS match_comments_match_id_idx ON public.match_comments(match_id);
CREATE INDEX IF NOT EXISTS match_comments_user_id_idx ON public.match_comments(user_id);
CREATE INDEX IF NOT EXISTS match_comments_group_id_idx ON public.match_comments(group_id);

-- Enable RLS for match_comments table
ALTER TABLE public.match_comments ENABLE ROW LEVEL SECURITY;

-- Create policies to restrict access to data based on group membership
CREATE POLICY "Users can view comments in their groups" ON public.match_comments
FOR SELECT USING (
  group_id IN (
    SELECT group_id FROM group_members WHERE user_id = auth.uid()
    UNION
    SELECT id FROM friend_groups WHERE created_by = auth.uid()
  )
);

CREATE POLICY "Users can insert comments in their groups" ON public.match_comments
FOR INSERT WITH CHECK (
  group_id IN (
    SELECT group_id FROM group_members WHERE user_id = auth.uid()
    UNION
    SELECT id FROM friend_groups WHERE created_by = auth.uid()
  )
);

CREATE POLICY "Users can update their own comments" ON public.match_comments
FOR UPDATE USING (
  auth.uid() = user_id
);

CREATE POLICY "Users can delete their own comments" ON public.match_comments
FOR DELETE USING (
  auth.uid() = user_id
);
