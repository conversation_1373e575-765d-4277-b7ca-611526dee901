// <PERSON><PERSON><PERSON> to create the shared_links table in Supabase
// Run with: node create_shared_links_table.js

import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';
import fs from 'fs';
import path from 'path';

// Load environment variables
dotenv.config();

// Create Supabase client with service role key
const supabaseUrl = process.env.VITE_SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseKey) {
  console.error('Error: VITE_SUPABASE_URL and SUPABASE_SERVICE_ROLE_KEY must be set in .env file');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey);

// SQL to create the shared_links table
const sql = `
-- Check if the shared_links table exists
DO $$
BEGIN
    IF NOT EXISTS (SELECT FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'shared_links') THEN
        -- Create shared_links table for enhanced sharing
        CREATE TABLE public.shared_links (
            id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
            group_id UUID REFERENCES friend_groups(id) ON DELETE CASCADE NOT NULL,
            created_by UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
            name TEXT NOT NULL,
            access_level TEXT DEFAULT 'read' CHECK (access_level IN ('read', 'comment')),
            expires_at TIMESTAMP WITH TIME ZONE,
            is_active BOOLEAN DEFAULT true,
            created_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now()) NOT NULL,
            updated_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now()) NOT NULL
        );

        -- Create index for better performance
        CREATE INDEX shared_links_group_id_idx ON public.shared_links(group_id);
        CREATE INDEX shared_links_created_by_idx ON public.shared_links(created_by);

        -- Enable Row Level Security
        ALTER TABLE public.shared_links ENABLE ROW LEVEL SECURITY;

        -- Create policies to restrict access to shared_links
        CREATE POLICY "Users can view their own shared links" ON public.shared_links
        FOR SELECT USING (
          created_by = auth.uid() OR
          group_id IN (
            SELECT group_id FROM group_members 
            WHERE user_id = auth.uid() AND role = 'Admin'
          )
        );

        CREATE POLICY "Users can create shared links for their groups" ON public.shared_links
        FOR INSERT WITH CHECK (
          group_id IN (
            SELECT group_id FROM group_members 
            WHERE user_id = auth.uid() AND role IN ('Admin', 'Collaborator')
            UNION
            SELECT id FROM friend_groups WHERE created_by = auth.uid()
          )
        );

        CREATE POLICY "Users can update their own shared links" ON public.shared_links
        FOR UPDATE USING (
          created_by = auth.uid() OR
          group_id IN (
            SELECT group_id FROM group_members 
            WHERE user_id = auth.uid() AND role = 'Admin'
          )
        );

        CREATE POLICY "Users can delete their own shared links" ON public.shared_links
        FOR DELETE USING (
          created_by = auth.uid() OR
          group_id IN (
            SELECT group_id FROM group_members 
            WHERE user_id = auth.uid() AND role = 'Admin'
          )
        );

        -- Check if the update_updated_at_column function exists
        IF EXISTS (SELECT FROM pg_proc WHERE proname = 'update_updated_at_column') THEN
            -- Create trigger to update updated_at timestamp
            CREATE TRIGGER update_shared_links_updated_at
            BEFORE UPDATE ON public.shared_links
            FOR EACH ROW
            EXECUTE FUNCTION update_updated_at_column();
        END IF;

        RAISE NOTICE 'Created shared_links table and associated objects';
    ELSE
        RAISE NOTICE 'shared_links table already exists';
    END IF;
END
$$;
`;

// Save SQL to a file for reference
fs.writeFileSync(path.join(process.cwd(), 'create_shared_links_table.sql'), sql);

// Execute the SQL
async function createTable() {
  try {
    // Try to execute the SQL directly
    const { error } = await supabase.rpc('exec_sql', { query: sql });
    
    if (error) {
      console.error('Error executing SQL via RPC:', error);
      console.log('The exec_sql function may not be available. You can run the SQL manually using the SQL file created.');
      return;
    }
    
    console.log('Successfully executed SQL to create shared_links table');
    
    // Verify the table was created
    const { data, error: checkError } = await supabase
      .from('shared_links')
      .select('count', { count: 'exact', head: true });
      
    if (checkError) {
      console.error('Error verifying table creation:', checkError);
      return;
    }
    
    console.log('Verified shared_links table exists');
  } catch (error) {
    console.error('Error creating shared_links table:', error);
  }
}

createTable()
  .then(() => console.log('Done'))
  .catch(err => console.error('Unhandled error:', err));
