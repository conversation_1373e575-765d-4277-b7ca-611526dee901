-- Create friend_groups table if it doesn't exist
CREATE TABLE IF NOT EXISTS friend_groups (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name TEXT NOT NULL,
    created_by UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    created_at TIMESTAMP DEFAULT now()
);

-- Create group_members table if it doesn't exist
CREATE TABLE IF NOT EXISTS group_members (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    group_id UUID REFERENCES friend_groups(id) ON DELETE CASCADE,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    role TEXT CHECK (role IN ('Admin', 'Collaborator', 'Guest')) NOT NULL,
    created_at TIMESTAMP DEFAULT now()
);

-- Create players table if it doesn't exist
CREATE TABLE IF NOT EXISTS players (
    id BIGINT GENERATED BY DEFAULT AS IDENTITY PRIMARY KEY,
    name TEXT NOT NULL,
    skills INTEGER NOT NULL,
    effort INTEGER NOT NULL,
    stamina INTEGER NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now()) NOT NULL
);

-- Create matches table if it doesn't exist
CREATE TABLE IF NOT EXISTS matches (
    id BIGINT GENERATED BY DEFAULT AS IDENTITY PRIMARY KEY,
    match_date TIMESTAMP WITH TIME ZONE NOT NULL,
    teama INTEGER[] NOT NULL,
    teamb INTEGER[] NOT NULL,
    scorea INTEGER,
    scoreb INTEGER,
    winner TEXT CHECK (winner IN ('A', 'B', 'Draw')),
    goalscorers JSONB,
    youtubelink TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now()) NOT NULL
);

-- Create chemistry table if it doesn't exist
CREATE TABLE IF NOT EXISTS chemistry (
    id BIGINT GENERATED BY DEFAULT AS IDENTITY PRIMARY KEY,
    player1_id BIGINT REFERENCES players(id) NOT NULL,
    player2_id BIGINT REFERENCES players(id) NOT NULL,
    games_together INTEGER DEFAULT 0,
    wins_together INTEGER DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now()) NOT NULL,
    UNIQUE(player1_id, player2_id)
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS chemistry_player1_id_idx ON chemistry(player1_id);
CREATE INDEX IF NOT EXISTS chemistry_player2_id_idx ON chemistry(player2_id);
