<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
  <title>Fulbito Stats - Dashboard Preview</title>
  <link rel="preconnect" href="https://fonts.googleapis.com">
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
  <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
  <link rel="modulepreload" href="https://cdn.jsdelivr.net/npm/feather-icons/dist/feather.min.js">
  <style>
    :root {
      --primary: #35db71;
      --primary-dark: #1a8f45;
      --primary-light: #a7f3c1;
      --secondary: #4a7aff;
      --background: #f8f9fa;
      --card: #ffffff;
      --text: #333333;
      --text-muted: #6c757d;
      --border: #e2e8f0;
      --success: #28a745;
      --warning: #ffc107;
      --danger: #dc3545;
      --shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    }

    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }

    body {
      font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
      background-color: var(--background);
      color: var(--text);
      line-height: 1.5;
      -webkit-font-smoothing: antialiased;
      -moz-osx-font-smoothing: grayscale;
    }

    .container {
      max-width: 100%;
      margin: 0 auto;
      padding: 0;
      overflow-x: hidden;
    }

    .app-header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      background-color: var(--primary);
      color: white;
      padding: 12px 16px;
      position: sticky;
      top: 0;
      z-index: 100;
      box-shadow: var(--shadow);
    }

    .logo {
      font-weight: 700;
      font-size: 1.25rem;
      display: flex;
      align-items: center;
      gap: 8px;
    }

    .group-selector {
      display: flex;
      align-items: center;
      background: rgba(255, 255, 255, 0.2);
      border-radius: 8px;
      padding: 6px 12px;
      font-size: 0.875rem;
      cursor: pointer;
    }

    .tab-bar {
      display: flex;
      background-color: var(--card);
      border-bottom: 1px solid var(--border);
      position: sticky;
      top: 56px;
      z-index: 90;
    }

    .tab {
      flex: 1;
      text-align: center;
      padding: 12px 4px;
      font-size: 0.75rem;
      color: var(--text-muted);
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: 4px;
      cursor: pointer;
    }

    .tab.active {
      color: var(--primary);
      border-bottom: 2px solid var(--primary);
      font-weight: 500;
    }

    .tab-icon {
      width: 18px;
      height: 18px;
    }

    .tab-content {
      padding: 16px;
      display: none;
    }

    .tab-content.active {
      display: block;
    }

    .stats-carousel {
      display: flex;
      overflow-x: auto;
      scroll-snap-type: x mandatory;
      gap: 12px;
      padding: 4px 0 16px 0;
      margin: 0 -16px;
      padding-left: 16px;
      -webkit-overflow-scrolling: touch;
    }

    .stat-card {
      flex: 0 0 85%;
      scroll-snap-align: start;
      background-color: var(--card);
      border-radius: 12px;
      padding: 16px;
      box-shadow: var(--shadow);
      position: relative;
    }

    .stat-card-icon {
      position: absolute;
      top: 16px;
      right: 16px;
      color: var(--primary-light);
    }

    .stat-value {
      font-size: 2rem;
      font-weight: 700;
      margin-top: 8px;
    }

    .stat-label {
      color: var(--text-muted);
      font-size: 0.875rem;
    }

    .section-title {
      font-size: 1.125rem;
      font-weight: 600;
      margin: 24px 0 16px 0;
      display: flex;
      align-items: center;
      justify-content: space-between;
    }

    .view-all {
      font-size: 0.75rem;
      color: var(--secondary);
      text-decoration: none;
      display: flex;
      align-items: center;
      gap: 4px;
    }

    .quick-actions {
      display: grid;
      grid-template-columns: repeat(2, 1fr);
      gap: 12px;
      margin-bottom: 24px;
    }

    .action-button {
      background-color: var(--card);
      border-radius: 12px;
      padding: 16px;
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: 8px;
      box-shadow: var(--shadow);
      cursor: pointer;
    }

    .action-icon {
      color: var(--primary);
      background-color: var(--primary-light);
      width: 40px;
      height: 40px;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .action-label {
      font-size: 0.875rem;
      font-weight: 500;
    }

    .card {
      background-color: var(--card);
      border-radius: 12px;
      padding: 16px;
      margin-bottom: 16px;
      box-shadow: var(--shadow);
    }

    .card-title {
      font-size: 1rem;
      font-weight: 600;
      margin-bottom: 12px;
      display: flex;
      align-items: center;
      gap: 8px;
    }

    .activity-list {
      display: flex;
      flex-direction: column;
      gap: 12px;
    }

    .activity-item {
      display: flex;
      align-items: center;
      gap: 12px;
      padding-bottom: 12px;
      border-bottom: 1px solid var(--border);
    }

    .activity-item:last-child {
      border-bottom: none;
      padding-bottom: 0;
    }

    .activity-icon {
      background-color: var(--primary-light);
      color: var(--primary);
      width: 36px;
      height: 36px;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      flex-shrink: 0;
    }

    .activity-content {
      flex: 1;
    }

    .activity-title {
      font-weight: 500;
      font-size: 0.875rem;
    }

    .activity-subtitle {
      color: var(--text-muted);
      font-size: 0.75rem;
    }

    .activity-meta {
      font-size: 0.75rem;
      color: var(--text-muted);
      text-align: right;
      white-space: nowrap;
    }

    .chart-container {
      height: 180px;
      margin-top: 12px;
      position: relative;
    }

    .chart-placeholder {
      width: 100%;
      height: 100%;
      background-color: #f0f0f0;
      border-radius: 8px;
      display: flex;
      align-items: center;
      justify-content: center;
      color: var(--text-muted);
      font-size: 0.75rem;
    }

    /* Chemistry Tab Styles */
    .filter-bar {
      display: flex;
      align-items: center;
      gap: 8px;
      margin-bottom: 16px;
      background-color: var(--card);
      padding: 12px;
      border-radius: 12px;
      box-shadow: var(--shadow);
    }

    .filter-input {
      flex: 1;
      border: 1px solid var(--border);
      border-radius: 8px;
      padding: 8px 12px;
      font-size: 0.875rem;
      background-color: var(--background);
    }

    .filter-button {
      background-color: var(--primary);
      color: white;
      border: none;
      border-radius: 8px;
      padding: 8px 12px;
      font-size: 0.875rem;
      cursor: pointer;
      display: flex;
      align-items: center;
      gap: 4px;
    }

    .chip-container {
      display: flex;
      gap: 8px;
      overflow-x: auto;
      padding-bottom: 12px;
      -webkit-overflow-scrolling: touch;
    }

    .chip {
      background-color: var(--background);
      border: 1px solid var(--border);
      border-radius: 16px;
      padding: 4px 12px;
      font-size: 0.75rem;
      white-space: nowrap;
      cursor: pointer;
    }

    .chip.active {
      background-color: var(--primary-light);
      border-color: var(--primary);
      color: var(--primary-dark);
    }

    .chemistry-card {
      display: flex;
      align-items: center;
      gap: 12px;
      margin-bottom: 12px;
    }

    .player-combo {
      flex: 1;
    }

    .player-names {
      font-weight: 500;
      font-size: 0.875rem;
      display: flex;
      align-items: center;
      gap: 4px;
    }

    .player-avatar {
      width: 24px;
      height: 24px;
      border-radius: 50%;
      background-color: var(--primary-light);
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 0.75rem;
      color: var(--primary-dark);
      font-weight: 600;
    }

    .player-stats {
      display: flex;
      gap: 8px;
      font-size: 0.75rem;
      color: var(--text-muted);
      margin-top: 4px;
    }

    .win-rate {
      font-weight: 600;
      font-size: 1rem;
      color: var(--success);
    }

    /* Leaderboard Tab Styles */
    .leaderboard-row {
      display: flex;
      align-items: center;
      padding: 12px 0;
      border-bottom: 1px solid var(--border);
    }

    .rank {
      width: 24px;
      height: 24px;
      border-radius: 50%;
      background-color: var(--background);
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 0.75rem;
      font-weight: 600;
      margin-right: 12px;
    }

    .rank.top-1 {
      background-color: gold;
      color: black;
    }

    .rank.top-2 {
      background-color: silver;
      color: black;
    }

    .rank.top-3 {
      background-color: #cd7f32;
      color: white;
    }

    .player-info {
      display: flex;
      align-items: center;
      flex: 1;
    }

    .player-name {
      font-weight: 500;
      font-size: 0.875rem;
      margin-left: 8px;
    }

    .player-rating {
      font-weight: 600;
      font-size: 1rem;
    }

    .trend {
      display: flex;
      align-items: center;
      font-size: 0.75rem;
      color: var(--success);
    }

    .trend.down {
      color: var(--danger);
    }

    /* Team Generator Tab Styles */
    .player-selection {
      max-height: 300px;
      overflow-y: auto;
      border: 1px solid var(--border);
      border-radius: 12px;
      margin-bottom: 16px;
    }

    .player-checkbox {
      display: flex;
      align-items: center;
      padding: 12px;
      border-bottom: 1px solid var(--border);
    }

    .player-checkbox:last-child {
      border-bottom: none;
    }

    .checkbox {
      margin-right: 12px;
    }

    .player-badge {
      background-color: var(--primary-light);
      color: var(--primary-dark);
      border-radius: 4px;
      padding: 2px 6px;
      font-size: 0.75rem;
      margin-left: 8px;
    }

    .controls {
      display: flex;
      flex-direction: column;
      gap: 12px;
      margin-bottom: 16px;
    }

    .segmented-control {
      display: flex;
      border: 1px solid var(--border);
      border-radius: 8px;
      overflow: hidden;
    }

    .segment {
      flex: 1;
      text-align: center;
      padding: 8px;
      font-size: 0.875rem;
      background-color: var(--background);
      cursor: pointer;
    }

    .segment.active {
      background-color: var(--primary);
      color: white;
    }

    .toggle-row {
      display: flex;
      align-items: center;
      justify-content: space-between;
    }

    .toggle-label {
      font-size: 0.875rem;
    }

    .toggle {
      position: relative;
      width: 40px;
      height: 20px;
      border-radius: 10px;
      background-color: var(--text-muted);
    }

    .toggle.active {
      background-color: var(--primary);
    }

    .toggle::after {
      content: '';
      position: absolute;
      width: 16px;
      height: 16px;
      border-radius: 50%;
      background-color: white;
      top: 2px;
      left: 2px;
      transition: transform 0.2s;
    }

    .toggle.active::after {
      transform: translateX(20px);
    }

    .generate-button {
      background-color: var(--primary);
      color: white;
      border: none;
      border-radius: 8px;
      padding: 12px;
      font-size: 1rem;
      font-weight: 500;
      width: 100%;
      cursor: pointer;
    }

    .team-card {
      margin-bottom: 12px;
    }

    .team-header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 8px;
    }

    .team-name {
      font-weight: 600;
    }

    .team-rating {
      font-size: 0.875rem;
      color: var(--text-muted);
    }

    .team-player {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 8px 0;
      border-bottom: 1px solid var(--border);
    }

    .team-player:last-child {
      border-bottom: none;
    }

    .balance-indicator {
      display: flex;
      align-items: center;
      justify-content: center;
      margin: 16px 0;
      font-size: 0.875rem;
      color: var(--success);
    }

    .pagination-dots {
      display: flex;
      justify-content: center;
      gap: 4px;
      margin-top: 8px;
    }

    .dot {
      width: 8px;
      height: 8px;
      border-radius: 50%;
      background-color: var(--border);
    }

    .dot.active {
      background-color: var(--primary);
    }

    /* Utility classes */
    .mt-4 {
      margin-top: 16px;
    }

    .mb-4 {
      margin-bottom: 16px;
    }

    .text-success {
      color: var(--success);
    }

    .text-warning {
      color: var(--warning);
    }

    .text-danger {
      color: var(--danger);
    }

    .flex {
      display: flex;
    }

    .items-center {
      align-items: center;
    }

    .justify-between {
      justify-content: space-between;
    }

    .gap-2 {
      gap: 8px;
    }
  </style>
</head>
<body>
  <div class="container">
    <!-- App Header -->
    <header class="app-header">
      <div class="logo">
        <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
          <circle cx="12" cy="12" r="10"></circle>
          <polygon points="10 8 16 12 10 16 10 8"></polygon>
        </svg>
        Fulbito
      </div>
      <div class="group-selector">
        Friday Night FC
        <svg width="16" height="1