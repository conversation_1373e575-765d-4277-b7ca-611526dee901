# Fixing the 404 Error with auth.users Table

This document explains how to fix the 404 error that occurs when trying to query user data from Supabase.

## The Problem

The error occurs because the application is trying to directly query the `auth.users` table through the REST API, but this table is not accessible this way. The error message is:

```
Endpoint: GET /rest/v1/users
Project ID: jbljfsvvskbbyxftqlkg
Error code: 42P01
Error message: 'relation "public.users" does not exist'
```

## The Solution

We've created a SQL function that can be used to safely query user details by ID. This function runs with elevated privileges and returns only the necessary user information in a JSON format.

## How to Apply the Fix

### Step 1: Apply the SQL Function to Your Supabase Project

You can apply the SQL function in one of two ways:

#### Option 1: Using the Supabase SQL Editor

1. Log in to your Supabase dashboard
2. Go to the SQL Editor
3. Copy the contents of `supabase/migrations/20240815000000_create_get_user_details_function.sql`
4. Paste it into the SQL Editor and run it

#### Option 2: Using the Provided Script

1. Make sure you have the `SUPABASE_SERVICE_ROLE_KEY` in your environment variables (this is different from the anon key)
2. Run the script:

```bash
node scripts/apply_user_details_function.js
```

### Step 2: The Code Changes

The code has already been updated to use the new function instead of directly querying the `auth.users` table. The change is in `src/lib/api/groups.ts`:

```javascript
// Before:
const { data: userData, error: userError } = await supabase
  .from('auth.users')
  .select('email, raw_user_meta_data->display_name')
  .eq('id', member.user_id)
  .maybeSingle();

// After:
const { data: userData, error: userError } = await supabase
  .rpc('get_user_details_by_id', { user_id_to_find: member.user_id });

if (!userError && userData) {
  member.user = {
    email: userData.email,
    display_name: userData.display_name
  };
}
```

## Verifying the Fix

After applying the SQL function and deploying the code changes, the 404 error should be resolved. You should now be able to see user details when viewing group members.

## Additional Information

- The SQL function is designed to be secure and only returns the email and display name of users.
- The function uses the `SECURITY DEFINER` attribute, which means it runs with the privileges of the function creator (typically the database owner).
- Only authenticated users can execute this function.
- The function returns a JSON object instead of a table to avoid type mismatch issues.
