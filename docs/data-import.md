# Data Import and Export Features

These features allow users to import data when creating a new group and export data from existing groups, making it easy to back up, share, or transfer data between groups.

## How to Use Import

1. Navigate to the Group Selection page
2. Click "Create Group"
3. Enter a name for your new group
4. Select the "Import Data" tab
5. Upload a JSON file containing players and matches
6. Review the preview of the data to be imported
7. Click "Confirm Import" to create the group with the imported data

## JSON Format

The import file must be a valid JSON file with the following structure:

```json
{
  "players": [
    {
      "name": "Player Name",
      "skills": 85,
      "effort": 90,
      "stamina": 75
    },
    ...
  ],
  "matches": [
    {
      "match_date": "2023-06-15T18:30:00Z",
      "teama": [0, 2, 4],
      "teamb": [1, 3],
      "scorea": 3,
      "scoreb": 2,
      "winner": "A",
      "goalscorers": [
        { "team": "A", "playerId": 0 },
        { "team": "B", "playerId": 1 }
      ],
      "youtubelink": ""
    },
    ...
  ]
}
```

### Players

Each player object must include:
- `name`: String (2-50 characters)
- `skills`: Number (0-100)
- `effort`: Number (0-100)
- `stamina`: Number (0-100)

### Matches

Each match object must include:
- `match_date`: ISO 8601 date string (format: `YYYY-MM-DDTHH:MM:SSZ`)
- `teama`: Array of player indices (0-10 players, corresponding to the index in the players array)
- `teamb`: Array of player indices (0-10 players, corresponding to the index in the players array)

Optional match fields:
- `scorea`: Number (0-99) or null
- `scoreb`: Number (0-99) or null
- `winner`: "A", "B", "Draw", or null
- `goalscorers`: Array of objects with `team` ("A" or "B") and `playerId` (number, index in the players array)
- `youtubelink`: String (valid URL) or empty string

## Important Note About Player Indices

In the import file, player references in teams and goalscorers use **0-based indices** into the players array:

- The first player in the `players` array has index 0
- The second player has index 1
- And so on...

For example, if your players array is:
```json
"players": [
  { "name": "John", ... },  // index 0
  { "name": "Sarah", ... }, // index 1
  { "name": "Mike", ... }   // index 2
]
```

Then in your matches, you would reference them as:
```json
"teama": [0, 2],  // John and Mike
"teamb": [1],     // Sarah
"goalscorers": [
  { "team": "A", "playerId": 0 }  // John scored
]
```

## Implementation Details

The import process works as follows:

1. The JSON file is validated against a schema to ensure it contains the required data
2. A preview of the data is shown to the user for confirmation
3. When confirmed, the data is sent to the server in a single transaction:
   - A new group is created
   - The user is added as an Admin
   - Players are imported
   - Matches are imported

If any part of the import fails, the entire transaction is rolled back to ensure data consistency.

## Export Feature

The export feature allows you to download all players and matches from an existing group as a JSON file.

### How to Use Export

1. Navigate to the Group Selection page
2. For the group you want to export, either:
   - Click the "Export Data" button below the group card
   - Or click the menu (three dots) and select "Export Data" from the dropdown
3. The data will be downloaded as a JSON file named `[group_name]_export_[date].json`

### Export Format

The exported JSON file follows the same format as the import file, making it easy to import the data into a new group. The export process handles all the necessary conversions:

- Player IDs are converted to indices (0-based)
- Team player references are updated to use these indices
- Goalscorer player references are also updated
- Date formats are standardized to ISO 8601 format with 'Z' timezone indicator

**Important**: The order of players in the export file is preserved from the original import order. This is critical for maintaining the correct player references in matches. If you manually edit the export file, be careful not to change the order of players, as this would cause player references in matches to point to the wrong players.

This ensures that the exported file can be imported directly into a new group without any manual adjustments.

## Error Handling

Common errors that may occur during import:
- Invalid JSON format
- Missing required fields
- Values outside of allowed ranges
- Too many players or matches
- Server errors during import

In all cases, a descriptive error message will be shown to help resolve the issue.
