# Resend Email Setup Guide

This guide explains how to set up Resend for sending emails in both development and production environments.

## Current Setup

The application is currently configured to use Resend's API for sending invitation emails. In development mode, all emails are redirected to the developer's email address (`<EMAIL>`) to comply with Resend's free tier limitations.

## Development Mode

In development mode:
- All emails are redirected to the developer's email address
- The original recipient's email is included in the email content for testing
- This allows testing the invitation flow without a verified domain

## Setting Up for Production

To send emails to actual recipients in production, follow these steps:

### 1. Verify a Domain with Resend

1. Create an account on [Resend](https://resend.com) if you haven't already
2. Go to the [Domains section](https://resend.com/domains) in your Resend dashboard
3. Click "Add Domain" and follow the instructions to verify your domain
4. Add the required DNS records to your domain's DNS settings
5. Wait for the domain to be verified (this can take up to 24 hours)

### 2. Update Environment Variables

Once your domain is verified, update the following environment variables in your Supabase project:

```bash
supabase secrets set SENDER_EMAIL=<EMAIL> SENDER_NAME="Soccer Stats" RESEND_MODE=production
```

Replace `<EMAIL>` with an email address using your verified domain.

### 3. Deploy the Updated Function

```bash
supabase functions deploy send-invitation-email
```

## Troubleshooting

### Common Issues

1. **403 Forbidden Error**: This usually means you're trying to send to recipients other than your own email without a verified domain.

2. **Email Not Received**: Check your spam folder. Also, verify that the Resend API key is correct and has sufficient permissions.

3. **Invalid Sender Domain**: Make sure the sender email address uses a domain that you've verified in Resend.

### Testing the Email Function

You can test the email function locally using:

```bash
curl -i --location --request POST 'http://127.0.0.1:54321/functions/v1/send-invitation-email' \
  --header 'Authorization: Bearer YOUR_ANON_KEY' \
  --header 'Content-Type: application/json' \
  --data '{"email":"<EMAIL>","groupId":"123","groupName":"Test Group","invitedBy":"<EMAIL>","role":"Guest"}'
```

## Alternative Email Providers

If Resend doesn't meet your needs, consider these alternatives:

1. **SendGrid**: Offers a free tier with 100 emails per day
2. **Mailgun**: Provides a free tier with 5,000 emails per month for 3 months
3. **Amazon SES**: Very cost-effective for high volume (approximately $0.10 per 1,000 emails)

Each provider has its own API and setup process, but the general integration approach would be similar.
