# Complete RLS Policy Rebuild

This document explains the complete rebuild of Row Level Security (RLS) policies for the Soccer Stats application.

## Why a Complete Rebuild?

After encountering persistent recursion issues with the existing RLS policies, we've taken a clean-slate approach by:

1. Dropping ALL existing policies
2. Temporarily disabling RLS
3. Creating new, simplified policies with a clear hierarchy
4. Using SECURITY DEFINER functions to break circular dependencies
5. Re-enabling RLS

## Key Improvements

The new RLS policy design includes several key improvements:

### 1. Helper Functions

We've created four helper functions that run with elevated privileges:

- `user_member_groups(user_uuid)`: Returns groups where a user is a member (any role)
- `user_admin_groups(user_uuid)`: Returns groups where a user is an admin
- `user_editor_groups(user_uuid)`: Returns groups where a user is an admin or collaborator
- `user_created_groups(user_uuid)`: Returns groups created by a user

These functions break circular dependencies by running with `SECURITY DEFINER` privileges.

### 2. Simplified Policy Structure

The new policies follow a clear, consistent pattern:

- Each policy has a specific purpose and descriptive name
- Policies are separated by operation (SELECT, INSERT, UPDATE, DELETE)
- Complex conditions are replaced with function calls
- Circular references are eliminated

### 3. Clear Access Hierarchy

The access control follows a clear hierarchy:

- **Group Creators**: Full access to their groups and all members
- **Admins**: Full access to group data and members
- **Collaborators**: Can edit data but not manage members
- **Guests**: Read-only access to group data

## How to Apply the Rebuild

### Option 1: Via Supabase SQL Editor (Recommended)

1. Log in to your Supabase dashboard
2. Navigate to the SQL Editor
3. Copy the contents of `supabase/migrations/20240901000003_complete_rls_rebuild.sql`
4. Paste into the SQL Editor and run the query

### Option 2: Via JavaScript Script

```bash
node scripts/apply_complete_rls_rebuild.js
```

## Verification

After applying the rebuild, verify that:

1. You can access the group selection page without errors
2. You can view and manage group members
3. The dashboard loads correctly with data from the selected group
4. Players, matches, and chemistry data are properly filtered by group

## Troubleshooting

If you encounter any issues after the rebuild:

1. Check the browser console for specific error messages
2. Verify that all policies were successfully created:

```sql
SELECT schemaname, tablename, policyname, cmd 
FROM pg_policies 
WHERE schemaname = 'public' 
ORDER BY tablename, cmd;
```

3. Test specific queries directly in the SQL Editor to isolate any remaining issues

## Maintenance

When adding new tables or features that require RLS:

1. Follow the same pattern of using helper functions
2. Create separate policies for different operations
3. Avoid complex nested queries in policy definitions
4. Test thoroughly with different user roles
