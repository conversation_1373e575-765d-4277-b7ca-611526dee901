# Fixing Supabase RLS Policy Recursion Issues

This document explains how to fix the infinite recursion errors in Supabase Row Level Security (RLS) policies for the group management system.

## Problem Description

The application is experiencing infinite recursion errors in the RLS policies for the `friend_groups` and `group_members` tables. These errors occur when:

1. Fetching groups in GroupContext.tsx
2. Querying member roles
3. Accessing data in DashboardPage.tsx

The errors manifest as 500 responses from the following endpoints:
- `/rest/v1/group_members`
- `/rest/v1/players`

## Root Cause

The root cause of the issue is circular dependencies in the RLS policies:

1. Policies on the `friend_groups` table reference the `group_members` table
2. Policies on the `group_members` table reference the `friend_groups` table
3. Some policies use complex nested queries with EXISTS and UNION operations

When Supabase evaluates these policies, it can enter an infinite recursion loop trying to resolve the circular dependencies.

## Solution

The solution involves:

1. Dropping the problematic policies that cause recursion
2. Creating new, simplified policies that avoid circular dependencies
3. Separating the policies for different operations (SELECT, INSERT, UPDATE, DELETE)
4. Using direct ID comparisons where possible instead of nested queries

## How to Apply the Fix

### Option 1: Apply via Migration (Recommended)

1. The migration file is located at `supabase/migrations/20240901000001_fix_recursive_rls_policies.sql`
2. Apply this migration to your Supabase project using the Supabase CLI:

```bash
supabase migration up
```

### Option 2: Apply via JavaScript Script

1. Make sure you have the required environment variables set in your `.env` file:
   - `VITE_SUPABASE_URL` - Your Supabase project URL
   - `SUPABASE_SERVICE_ROLE_KEY` - Your Supabase service role key (with admin privileges)

2. Run the script:

```bash
node scripts/apply_rls_policy_fix.js
```

### Option 3: Apply Manually via SQL Editor

1. Log in to your Supabase dashboard
2. Navigate to the SQL Editor
3. Copy the contents of `supabase/migrations/20240901000001_fix_recursive_rls_policies.sql`
4. Paste into the SQL Editor and run the query

## Verification

After applying the fix, verify that:

1. You can access the group selection page without errors
2. You can view and manage group members
3. The dashboard loads correctly with data from the selected group
4. Players, matches, and chemistry data are properly filtered by group

## Troubleshooting

If you continue to experience issues:

1. Check the browser console for any errors
2. Verify that all policies were successfully applied by querying the `pg_policies` table:

```sql
SELECT schemaname, tablename, policyname, permissive, roles, cmd, qual
FROM pg_policies
WHERE schemaname = 'public' AND (tablename = 'friend_groups' OR tablename = 'group_members');
```

3. If needed, you can temporarily disable RLS for testing purposes (remember to re-enable it afterward):

```sql
ALTER TABLE friend_groups DISABLE ROW LEVEL SECURITY;
ALTER TABLE group_members DISABLE ROW LEVEL SECURITY;
```
