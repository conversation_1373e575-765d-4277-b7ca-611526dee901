import fs from 'fs';

// Read the original file
const originalData = JSON.parse(fs.readFileSync('converted_fulbito_stats_fixed_ids.json', 'utf8'));

// Create a new data structure with the same players
const newData = {
  players: originalData.players,
  matches: []
};

// Create a map of player indices to names for easy lookup
const playerIndexToName = {};
originalData.players.forEach((player, index) => {
  playerIndexToName[index] = player.name;
});

// Process each match
originalData.matches.forEach(match => {
  // Create a new match object with explicit player references
  const newMatch = {
    ...match,
    // Add player names for reference (this won't affect import but helps for debugging)
    teamaNames: match.teama.map(index => playerIndexToName[index] || `Unknown player at index ${index}`),
    teambNames: match.teamb.map(index => playerIndexToName[index] || `Unknown player at index ${index}`)
  };

  // Add the match to the new data
  newData.matches.push(newMatch);
});

// Save the new file
fs.writeFileSync('converted_fulbito_stats_with_names.json', JSON.stringify(newData, null, 2));

console.log('Created new file: converted_fulbito_stats_with_names.json');
console.log('This file includes player names for each match for easier debugging.');
console.log('You can import this file using the same process as before.');

// Print a sample of the first match for verification
if (newData.matches.length > 0) {
  console.log('\nSample of first match:');
  const firstMatch = newData.matches[0];
  console.log(`Match date: ${firstMatch.match_date}`);
  console.log('Team A:');
  firstMatch.teama.forEach((index, i) => {
    console.log(`  ${index}: ${firstMatch.teamaNames[i]}`);
  });
  console.log('Team B:');
  firstMatch.teamb.forEach((index, i) => {
    console.log(`  ${index}: ${firstMatch.teambNames[i]}`);
  });
}
