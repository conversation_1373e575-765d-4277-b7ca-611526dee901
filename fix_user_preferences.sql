-- Drop the existing function first
DROP FUNCTION IF EXISTS public.check_table_exists(text);

-- Fix the check_table_exists function to avoid ambiguous column reference
CREATE FUNCTION public.check_table_exists(p_table_name text)
RETURNS boolean
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  table_exists boolean;
BEGIN
  SELECT EXISTS (
    SELECT 1 FROM information_schema.tables
    WHERE table_schema = 'public'
    AND table_name = p_table_name
  ) INTO table_exists;

  RETURN table_exists;
END;
$$;

-- Grant execute permission to anon and authenticated roles
GRANT EXECUTE ON FUNCTION public.check_table_exists TO anon;
GRANT EXECUTE ON FUNCTION public.check_table_exists TO authenticated;

-- Create a function to execute SQL statements with admin privileges
-- This function should only be callable by service role or admin users
CREATE OR REPLACE FUNCTION public.exec_sql(sql text)
RETURNS void
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  EXECUTE sql;
END;
$$;

-- Restrict access to this function to only service role
R<PERSON>VOKE ALL ON FUNCTION public.exec_sql FROM PUBLIC;
R<PERSON>VOKE ALL ON FUNCTION public.exec_sql FROM anon;
REVOKE ALL ON FUNCTION public.exec_sql FROM authenticated;

-- Verify the user_preferences table exists
DO $$
BEGIN
  IF NOT EXISTS (
    SELECT 1 FROM information_schema.tables
    WHERE table_schema = 'public'
    AND table_name = 'user_preferences'
  ) THEN
    RAISE NOTICE 'user_preferences table does not exist!';
  ELSE
    RAISE NOTICE 'user_preferences table exists.';
  END IF;
END;
$$;
