-- First, check if the user_preferences table exists
DO $$
DECLARE
  table_exists B<PERSON><PERSON><PERSON>N;
BEGIN
  SELECT EXISTS (
    SELECT FROM information_schema.tables 
    WHERE table_schema = 'public'
    AND table_name = 'user_preferences'
  ) INTO table_exists;
  
  IF table_exists THEN
    RAISE NOTICE 'user_preferences table exists';
  ELSE
    RAISE NOTICE 'user_preferences table does NOT exist';
  END IF;
END;
$$;

-- Check current RLS status
SELECT relname, relrowsecurity 
FROM pg_class 
WHERE relname = 'user_preferences';

-- Make sure RLS is enabled
ALTER TABLE public.user_preferences ENABLE ROW LEVEL SECURITY;

-- Drop existing policies
DROP POLICY IF EXISTS "Users can view their own preferences" ON public.user_preferences;
DROP POLICY IF EXISTS "Users can insert their own preferences" ON public.user_preferences;
DROP POLICY IF EXISTS "Users can update their own preferences" ON public.user_preferences;
DROP POLICY IF EXISTS "Users can delete their own preferences" ON public.user_preferences;

-- Create more permissive policies for testing
-- This allows any authenticated user to view any preferences (for debugging)
CREATE POLICY "Any authenticated user can view preferences" ON public.user_preferences
  FOR SELECT USING (auth.role() = 'authenticated');

-- These policies still restrict insert/update/delete to the user's own records
CREATE POLICY "Users can insert their own preferences" ON public.user_preferences
  FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own preferences" ON public.user_preferences
  FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete their own preferences" ON public.user_preferences
  FOR DELETE USING (auth.uid() = user_id);

-- Check the policies after changes
SELECT 
  schemaname, 
  tablename, 
  policyname, 
  permissive, 
  roles, 
  cmd, 
  qual, 
  with_check
FROM 
  pg_policies 
WHERE 
  tablename = 'user_preferences';
