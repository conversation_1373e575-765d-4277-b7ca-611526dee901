-- Comprehensive script to fix RLS policies for user_preferences table

-- 1. Check if the user_preferences table exists
DO $$
DECLARE
  table_exists BO<PERSON>EAN;
BEGIN
  SELECT EXISTS (
    SELECT FROM information_schema.tables 
    WHERE table_schema = 'public'
    AND table_name = 'user_preferences'
  ) INTO table_exists;
  
  IF table_exists THEN
    RAISE NOTICE 'user_preferences table exists';
  ELSE
    RAISE NOTICE 'user_preferences table does NOT exist';
    
    -- Create the table if it doesn't exist
    CREATE TABLE public.user_preferences (
      id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
      user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
      theme TEXT NOT NULL DEFAULT 'system',
      default_group_id UUID REFERENCES public.friend_groups(id) ON DELETE SET NULL,
      notification_settings JSONB NOT NULL DEFAULT '{"email": true, "browser": true}'::jsonb,
      display_settings JSONB NOT NULL DEFAULT '{"compactView": false, "showAvatars": true, "animationsEnabled": true}'::jsonb,
      created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
      updated_at TIMESTAMPTZ NOT NULL DEFAULT now(),
      UNIQUE(user_id)
    );
    
    RAISE NOTICE 'Created user_preferences table';
  END IF;
END;
$$;

-- 2. Check current RLS status
SELECT relname, relrowsecurity 
FROM pg_class 
WHERE relname = 'user_preferences';

-- 3. Check existing RLS policies
SELECT 
  schemaname, 
  tablename, 
  policyname, 
  permissive, 
  roles, 
  cmd, 
  qual, 
  with_check
FROM 
  pg_policies 
WHERE 
  tablename = 'user_preferences';

-- 4. Check table structure
SELECT 
  column_name, 
  data_type, 
  is_nullable
FROM 
  information_schema.columns
WHERE 
  table_schema = 'public' 
  AND table_name = 'user_preferences'
ORDER BY 
  ordinal_position;

-- 5. Check if there are any rows in the table
SELECT COUNT(*) FROM public.user_preferences;

-- 6. Enable RLS on the table
ALTER TABLE public.user_preferences ENABLE ROW LEVEL SECURITY;

-- 7. Drop existing policies
DROP POLICY IF EXISTS "Users can view their own preferences" ON public.user_preferences;
DROP POLICY IF EXISTS "Users can insert their own preferences" ON public.user_preferences;
DROP POLICY IF EXISTS "Users can update their own preferences" ON public.user_preferences;
DROP POLICY IF EXISTS "Users can delete their own preferences" ON public.user_preferences;
DROP POLICY IF EXISTS "Any authenticated user can view preferences" ON public.user_preferences;
DROP POLICY IF EXISTS "Allow all operations for authenticated users" ON public.user_preferences;

-- 8. Create proper RLS policies
-- Policy for SELECT: Users can only view their own preferences
CREATE POLICY "Users can view their own preferences" ON public.user_preferences
  FOR SELECT USING (auth.uid() = user_id);

-- Policy for INSERT: Users can only insert their own preferences
CREATE POLICY "Users can insert their own preferences" ON public.user_preferences
  FOR INSERT WITH CHECK (auth.uid() = user_id);

-- Policy for UPDATE: Users can only update their own preferences
CREATE POLICY "Users can update their own preferences" ON public.user_preferences
  FOR UPDATE USING (auth.uid() = user_id);

-- Policy for DELETE: Users can only delete their own preferences
CREATE POLICY "Users can delete their own preferences" ON public.user_preferences
  FOR DELETE USING (auth.uid() = user_id);

-- 9. Verify the policies were created correctly
SELECT 
  schemaname, 
  tablename, 
  policyname, 
  permissive, 
  roles, 
  cmd, 
  qual, 
  with_check
FROM 
  pg_policies 
WHERE 
  tablename = 'user_preferences';

-- 10. Create a trigger to update the updated_at timestamp
CREATE OR REPLACE FUNCTION public.update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = now();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

DROP TRIGGER IF EXISTS update_user_preferences_updated_at ON public.user_preferences;

CREATE TRIGGER update_user_preferences_updated_at
BEFORE UPDATE ON public.user_preferences
FOR EACH ROW
EXECUTE FUNCTION public.update_updated_at_column();

-- 11. Create a function to get user preferences (bypasses RLS)
CREATE OR REPLACE FUNCTION public.get_user_preferences(p_user_id UUID)
RETURNS SETOF public.user_preferences
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  RETURN QUERY
  SELECT * FROM public.user_preferences
  WHERE user_id = p_user_id;
END;
$$;

-- Grant execute permission to authenticated users
GRANT EXECUTE ON FUNCTION public.get_user_preferences TO authenticated;

-- 12. Create a function to upsert user preferences (bypasses RLS)
CREATE OR REPLACE FUNCTION public.upsert_user_preferences(
  p_user_id UUID,
  p_theme TEXT DEFAULT 'system',
  p_default_group_id UUID DEFAULT NULL,
  p_notification_settings JSONB DEFAULT '{"email": true, "browser": true}'::jsonb,
  p_display_settings JSONB DEFAULT '{"compactView": false, "showAvatars": true, "animationsEnabled": true}'::jsonb
)
RETURNS public.user_preferences
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  v_result public.user_preferences;
BEGIN
  -- Try to update first
  UPDATE public.user_preferences
  SET 
    theme = p_theme,
    default_group_id = p_default_group_id,
    notification_settings = p_notification_settings,
    display_settings = p_display_settings,
    updated_at = now()
  WHERE user_id = p_user_id
  RETURNING * INTO v_result;
  
  -- If no rows were updated, insert a new record
  IF v_result IS NULL THEN
    INSERT INTO public.user_preferences (
      user_id,
      theme,
      default_group_id,
      notification_settings,
      display_settings
    )
    VALUES (
      p_user_id,
      p_theme,
      p_default_group_id,
      p_notification_settings,
      p_display_settings
    )
    RETURNING * INTO v_result;
  END IF;
  
  RETURN v_result;
END;
$$;

-- Grant execute permission to authenticated users
GRANT EXECUTE ON FUNCTION public.upsert_user_preferences TO authenticated;

-- 13. Create a function to reset user preferences (bypasses RLS)
CREATE OR REPLACE FUNCTION public.reset_user_preferences(p_user_id UUID)
RETURNS public.user_preferences
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  v_result public.user_preferences;
BEGIN
  -- Delete existing preferences
  DELETE FROM public.user_preferences
  WHERE user_id = p_user_id;
  
  -- Insert default preferences
  INSERT INTO public.user_preferences (
    user_id,
    theme,
    default_group_id,
    notification_settings,
    display_settings
  )
  VALUES (
    p_user_id,
    'system',
    NULL,
    '{"email": true, "browser": true}'::jsonb,
    '{"compactView": false, "showAvatars": true, "animationsEnabled": true}'::jsonb
  )
  RETURNING * INTO v_result;
  
  RETURN v_result;
END;
$$;

-- Grant execute permission to authenticated users
GRANT EXECUTE ON FUNCTION public.reset_user_preferences TO authenticated;
