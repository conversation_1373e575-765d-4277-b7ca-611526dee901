<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no" />
    <title>Fulbito Stats</title>
    <meta name="description" content="Track player performance and generate balanced teams" />
    <meta name="author" content="Fulbito Stats" />

    <!-- PWA support -->
    <meta name="theme-color" content="#35db71" />
    <meta name="apple-mobile-web-app-capable" content="yes" />
    <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent" />
    <meta name="apple-mobile-web-app-title" content="Fulbito Stats" />

    <!-- Icons -->
    <link rel="icon" href="/favicon.svg" type="image/svg+xml" />
    <link rel="alternate icon" href="/favicon.ico" type="image/x-icon" />
    <link rel="apple-touch-icon" href="/icons/icon-192x192.png" />
    <link rel="apple-touch-icon" sizes="152x152" href="/icons/icon-152x152.png" />
    <link rel="apple-touch-icon" sizes="180x180" href="/icons/icon-180x180.png" />
    <link rel="apple-touch-icon" sizes="167x167" href="/icons/icon-167x167.png" />

    <!-- Manifest -->
    <link rel="manifest" href="/manifest.json" />

    <!-- Social media -->
    <meta property="og:title" content="Fulbito Stats" />
    <meta property="og:description" content="Track player performance and generate balanced teams" />
    <meta property="og:type" content="website" />
    <meta property="og:image" content="/assets/logo.svg" />
    <meta name="twitter:card" content="summary_large_image" />
    <meta name="twitter:image" content="/assets/logo.svg" />
  </head>

  <body>
    <div id="root"></div>
    <script type="module" src="/src/main.tsx"></script>
  </body>
</html>
