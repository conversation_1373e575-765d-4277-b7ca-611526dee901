-- This script will migrate existing data to use group_id
-- It will create a default group for each user and assign all their data to that group

-- First, let's create a function to migrate data for a specific user
CREATE OR REPLACE FUNCTION migrate_user_data_to_group(user_id UUID)
RETURNS UUID AS $$
DECLARE
    default_group_id UUID;
    player_ids INTEGER[];
BEGIN
    -- Check if the user already has a group
    SELECT id INTO default_group_id
    FROM friend_groups
    WHERE created_by = user_id
    LIMIT 1;
    
    -- If not, create a default group for the user
    IF default_group_id IS NULL THEN
        INSERT INTO friend_groups (name, created_by)
        VALUES ('Default Group', user_id)
        RETURNING id INTO default_group_id;
        
        -- Add the user as an Admin in their own group
        INSERT INTO group_members (group_id, user_id, role)
        VALUES (default_group_id, user_id, 'Admin');
    END IF;
    
    -- Update players table
    UPDATE players
    SET group_id = default_group_id
    WHERE group_id IS NULL;
    
    -- Get all player IDs
    SELECT array_agg(id) INTO player_ids
    FROM players
    WHERE group_id = default_group_id;
    
    -- Update matches table
    UPDATE matches
    SET group_id = default_group_id
    WHERE group_id IS NULL
    AND (
        EXISTS (SELECT 1 FROM unnest(teama) AS player_id WHERE player_id = ANY(player_ids))
        OR EXISTS (SELECT 1 FROM unnest(teamb) AS player_id WHERE player_id = ANY(player_ids))
    );
    
    -- Update chemistry table
    UPDATE chemistry
    SET group_id = default_group_id
    WHERE group_id IS NULL
    AND (player1_id = ANY(player_ids) OR player2_id = ANY(player_ids));
    
    RETURN default_group_id;
END;
$$ LANGUAGE plpgsql;

-- Now, let's run this function for all users
DO $$
DECLARE
    user_record RECORD;
BEGIN
    FOR user_record IN SELECT id FROM auth.users
    LOOP
        PERFORM migrate_user_data_to_group(user_record.id);
    END LOOP;
END;
$$;
