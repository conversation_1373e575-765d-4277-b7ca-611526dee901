{"name": "fulbito-stats", "private": true, "version": "1.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "build:dev": "vite build --mode development", "lint": "eslint .", "preview": "vite preview", "test": "vitest run", "test:watch": "vitest", "test:coverage": "vitest run --coverage", "test:ui": "vitest --ui", "transform-csv": "node scripts/transform-csv-to-json.js"}, "dependencies": {"@hookform/resolvers": "^3.9.0", "@radix-ui/react-accordion": "1.1.2", "@radix-ui/react-alert-dialog": "1.0.5", "@radix-ui/react-aspect-ratio": "1.0.3", "@radix-ui/react-avatar": "1.0.4", "@radix-ui/react-checkbox": "1.0.4", "@radix-ui/react-collapsible": "1.0.3", "@radix-ui/react-context-menu": "2.1.5", "@radix-ui/react-dialog": "1.0.5", "@radix-ui/react-dropdown-menu": "2.0.6", "@radix-ui/react-hover-card": "1.0.7", "@radix-ui/react-icons": "1.3.0", "@radix-ui/react-label": "2.0.2", "@radix-ui/react-navigation-menu": "1.1.4", "@radix-ui/react-popover": "1.0.7", "@radix-ui/react-progress": "1.0.3", "@radix-ui/react-radio-group": "1.1.3", "@radix-ui/react-scroll-area": "1.0.5", "@radix-ui/react-select": "1.2.2", "@radix-ui/react-separator": "1.0.3", "@radix-ui/react-slider": "1.1.2", "@radix-ui/react-slot": "1.0.2", "@radix-ui/react-switch": "1.0.3", "@radix-ui/react-tabs": "1.0.4", "@radix-ui/react-toast": "1.1.5", "@radix-ui/react-toggle": "1.0.3", "@radix-ui/react-toggle-group": "1.0.4", "@radix-ui/react-tooltip": "1.0.7", "@supabase/ssr": "^0.6.1", "@supabase/supabase-js": "^2.49.4", "@tanstack/react-query": "^5.56.2", "@tanstack/react-query-devtools": "^5.73.3", "@vercel/analytics": "^1.5.0", "caniuse-lite": "", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.0.0", "date-fns": "^3.6.0", "dotenv": "^16.5.0", "i18next": "^25.0.1", "i18next-browser-languagedetector": "^8.0.5", "lucide-react": "^0.462.0", "next-themes": "^0.3.0", "react": "^18.3.1", "react-day-picker": "^8.10.1", "react-dnd": "^16.0.1", "react-dnd-html5-backend": "^16.0.1", "react-dom": "^18.3.1", "react-hook-form": "^7.53.0", "react-i18next": "^15.5.1", "react-icons": "^5.5.0", "react-resizable-panels": "^2.1.3", "react-router-dom": "^6.26.2", "recharts": "^2.12.7", "rollup-plugin-visualizer": "^5.14.0", "sonner": "^1.5.0", "tailwind-merge": "^2.5.2", "tailwindcss-animate": "^1.0.7", "uuid": "^11.1.0", "vaul": "^0.9.3", "zod": "^3.23.8"}, "devDependencies": {"@eslint/js": "^9.9.0", "@tailwindcss/typography": "^0.5.15", "@testing-library/jest-dom": "^6.1.5", "@testing-library/react": "^14.1.2", "@types/node": "^20.10.5", "@types/react": "^18.2.45", "@types/react-dom": "^18.2.18", "@types/uuid": "^10.0.0", "@vitejs/plugin-react-swc": "^3.5.0", "@vitest/coverage-v8": "^3.1.2", "autoprefixer": "^10.4.16", "csv-parse": "^5.6.0", "eslint": "^8.56.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.5", "jsdom": "^23.0.1", "postcss": "^8.4.32", "tailwindcss": "^3.4.0", "terser": "^5.39.0", "typescript": "^5.3.3", "typescript-eslint": "^8.0.1", "vite": "^6.3.2", "vite-plugin-pwa": "^1.0.0", "vitest": "^3.1.2", "workbox-window": "^7.3.0"}, "overrides": {"@radix-ui/react-focus-scope": "1.0.3", "@radix-ui/react-use-callback-ref": "1.0.1", "@radix-ui/react-primitive": "1.0.3", "@radix-ui/react-compose-refs": "1.0.1", "@radix-ui/primitive": "1.0.1", "@radix-ui/react-context": "1.0.1", "@radix-ui/react-id": "1.0.1", "@radix-ui/react-use-controllable-state": "1.0.1", "@radix-ui/react-use-layout-effect": "1.0.1", "@radix-ui/react-dismissable-layer": "1.0.4", "@radix-ui/react-portal": "1.0.3", "@radix-ui/react-presence": "1.0.1", "@radix-ui/react-direction": "1.0.1", "@radix-ui/react-collection": "1.0.3"}}