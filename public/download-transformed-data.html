<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Fulbito Stats - Download Transformed Data</title>
  <style>
    body {
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
      max-width: 800px;
      margin: 0 auto;
      padding: 20px;
      line-height: 1.6;
    }
    h1 {
      color: #35db71;
      margin-bottom: 20px;
    }
    .card {
      border: 1px solid #e0e0e0;
      border-radius: 8px;
      padding: 20px;
      margin-bottom: 20px;
      box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }
    .button {
      display: inline-block;
      background-color: #35db71;
      color: white;
      padding: 10px 20px;
      border-radius: 4px;
      text-decoration: none;
      font-weight: bold;
      margin-top: 20px;
    }
    .button:hover {
      background-color: #2bb85e;
    }
    pre {
      background-color: #f5f5f5;
      padding: 15px;
      border-radius: 4px;
      overflow: auto;
      max-height: 300px;
    }
  </style>
</head>
<body>
  <h1>Transformed Fulbito Match Data</h1>

  <div class="card">
    <h2>Data Summary</h2>
    <p>This file contains transformed Fulbito match data from the CSV file. It has been converted to the JSON format required by the import feature.</p>
    <p><strong>Players:</strong> <span id="playerCount">Loading...</span></p>
    <p><strong>Matches:</strong> <span id="matchCount">Loading...</span></p>

    <a href="/transformed-data.json" download="fulbito-matches-import.json" class="button">Download JSON File</a>
  </div>

  <div class="card">
    <h2>Sample Data Preview</h2>
    <pre id="dataSample">Loading...</pre>
  </div>

  <script>
    // Fetch and display the data
    fetch('/transformed-data.json')
      .then(response => response.json())
      .then(data => {
        // Update counts
        document.getElementById('playerCount').textContent = data.players.length;
        document.getElementById('matchCount').textContent = data.matches.length;

        // Create a sample with first 3 players and first 2 matches
        const sample = {
          players: data.players.slice(0, 3),
          matches: data.matches.slice(0, 2)
        };

        // Display the sample
        document.getElementById('dataSample').textContent = JSON.stringify(sample, null, 2);
      })
      .catch(error => {
        console.error('Error loading data:', error);
        document.getElementById('dataSample').textContent = 'Error loading data: ' + error.message;
      });
  </script>
</body>
</html>
