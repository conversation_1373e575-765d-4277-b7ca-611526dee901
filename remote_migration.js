import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

const supabaseUrl = process.env.VITE_SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_SERVICE_ROLE_KEY || process.env.VITE_SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseKey) {
  console.error('Missing Supabase environment variables');
  process.exit(1);
}

console.log('Connecting to Supabase at:', supabaseUrl);
const supabase = createClient(supabaseUrl, supabaseKey);

async function createUserPreferencesTable() {
  console.log('Creating user_preferences table...');
  
  // Check if the table already exists
  const { error: checkError } = await supabase
    .from('user_preferences')
    .select('count(*)', { count: 'exact', head: true });
  
  if (!checkError) {
    console.log('user_preferences table already exists');
    return;
  }
  
  if (checkError.code !== '42P01') {
    console.error('Unexpected error checking table:', checkError);
    return;
  }
  
  // Create the table using SQL
  const { error } = await supabase.rpc('exec_sql', {
    sql: `
      -- Create user_preferences table
      CREATE TABLE IF NOT EXISTS public.user_preferences (
          id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
          user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
          theme TEXT DEFAULT 'system' CHECK (theme IN ('light', 'dark', 'system')),
          dashboard_layout JSONB DEFAULT '{}',
          default_group_id UUID REFERENCES friend_groups(id) ON DELETE SET NULL,
          notification_settings JSONB DEFAULT '{"email": true, "browser": true}',
          display_settings JSONB DEFAULT '{"compactView": false, "showAvatars": true, "animationsEnabled": true}',
          created_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now()) NOT NULL,
          updated_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now()) NOT NULL,
          UNIQUE(user_id)
      );

      -- Create index for better performance
      CREATE INDEX IF NOT EXISTS user_preferences_user_id_idx ON public.user_preferences(user_id);

      -- Enable Row Level Security
      ALTER TABLE public.user_preferences ENABLE ROW LEVEL SECURITY;

      -- Create policies to restrict access to user's own preferences
      CREATE POLICY "Users can view their own preferences" ON public.user_preferences
      FOR SELECT USING (auth.uid() = user_id);

      CREATE POLICY "Users can insert their own preferences" ON public.user_preferences
      FOR INSERT WITH CHECK (auth.uid() = user_id);

      CREATE POLICY "Users can update their own preferences" ON public.user_preferences
      FOR UPDATE USING (auth.uid() = user_id);

      CREATE POLICY "Users can delete their own preferences" ON public.user_preferences
      FOR DELETE USING (auth.uid() = user_id);

      -- Create function to automatically update updated_at timestamp
      CREATE OR REPLACE FUNCTION update_updated_at_column()
      RETURNS TRIGGER AS $$
      BEGIN
         NEW.updated_at = NOW();
         RETURN NEW;
      END;
      $$ LANGUAGE plpgsql;

      -- Create trigger to update updated_at timestamp
      CREATE TRIGGER update_user_preferences_updated_at
      BEFORE UPDATE ON public.user_preferences
      FOR EACH ROW
      EXECUTE FUNCTION update_updated_at_column();
    `
  });

  if (error) {
    console.error('Error creating user_preferences table:', error);
  } else {
    console.log('user_preferences table created successfully');
  }
}

async function main() {
  try {
    await createUserPreferencesTable();
    console.log('Migration completed');
  } catch (error) {
    console.error('Migration failed:', error);
  }
}

main();
