-- First, check if the user_preferences table exists
DO $$
DECLARE
  table_exists B<PERSON><PERSON><PERSON><PERSON>;
BEGIN
  SELECT EXISTS (
    SELECT FROM information_schema.tables 
    WHERE table_schema = 'public'
    AND table_name = 'user_preferences'
  ) INTO table_exists;
  
  IF table_exists THEN
    RAISE NOTICE 'user_preferences table exists';
  ELSE
    RAISE NOTICE 'user_preferences table does NOT exist';
  END IF;
END;
$$;

-- Check current RLS status
SELECT relname, relrowsecurity 
FROM pg_class 
WHERE relname = 'user_preferences';

-- Temporarily disable RLS for the table
ALTER TABLE public.user_preferences DISABLE ROW LEVEL SECURITY;

-- Drop all existing policies
DROP POLICY IF EXISTS "Users can view their own preferences" ON public.user_preferences;
DROP POLICY IF EXISTS "Users can insert their own preferences" ON public.user_preferences;
DROP POLICY IF EXISTS "Users can update their own preferences" ON public.user_preferences;
DROP POLICY IF EXISTS "Users can delete their own preferences" ON public.user_preferences;
DROP POLICY IF EXISTS "Any authenticated user can view preferences" ON public.user_preferences;

-- Create a very permissive policy for testing
CREATE POLICY "Allow all operations for authenticated users" ON public.user_preferences
  FOR ALL USING (auth.role() = 'authenticated');

-- Re-enable RLS
ALTER TABLE public.user_preferences ENABLE ROW LEVEL SECURITY;

-- Check the policies after changes
SELECT 
  schemaname, 
  tablename, 
  policyname, 
  permissive, 
  roles, 
  cmd, 
  qual, 
  with_check
FROM 
  pg_policies 
WHERE 
  tablename = 'user_preferences';
