// <PERSON>ript to apply additional fixes for recursive RLS policy issues
// Run with: node scripts/apply_additional_fix.js

import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';
import fs from 'fs';
import path from 'path';

// Load environment variables
dotenv.config();

const supabaseUrl = process.env.VITE_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('Missing Supabase environment variables. Make sure VITE_SUPABASE_URL and SUPABASE_SERVICE_ROLE_KEY are set.');
  process.exit(1);
}

// Create Supabase client with service role key for admin privileges
const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function applyAdditionalFix() {
  try {
    console.log('Applying additional RLS policy fixes to resolve remaining recursion issues...');
    
    // Read the SQL file
    const sqlFilePath = path.join(process.cwd(), 'supabase/migrations/20240901000002_fix_remaining_recursive_issues.sql');
    const sql = fs.readFileSync(sqlFilePath, 'utf8');
    
    // Execute the SQL
    const { error } = await supabase.rpc('exec_sql', { sql });

    if (error) {
      console.error('Error applying additional RLS policy fixes:', error);
      return;
    }

    console.log('Successfully applied additional RLS policy fixes!');
    console.log('The remaining recursion issues should now be resolved.');
    
    // Verify the policies
    console.log('Verifying policies...');
    
    const { data: policies, error: policiesError } = await supabase.rpc('exec_sql', { 
      sql: `
        SELECT schemaname, tablename, policyname, cmd 
        FROM pg_policies 
        WHERE schemaname = 'public' 
        AND (tablename = 'friend_groups' OR tablename = 'group_members')
        ORDER BY tablename, cmd;
      `
    });
    
    if (policiesError) {
      console.error('Error verifying policies:', policiesError);
    } else {
      console.log('Current policies:');
      console.log(policies);
    }
    
  } catch (error) {
    console.error('Error:', error);
  }
}

applyAdditionalFix();
