// Script to completely rebuild all RLS policies from scratch
// Run with: node scripts/apply_complete_rls_rebuild.js

import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';
import fs from 'fs';
import path from 'path';

// Load environment variables
dotenv.config();

const supabaseUrl = process.env.VITE_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('Missing Supabase environment variables. Make sure VITE_SUPABASE_URL and SUPABASE_SERVICE_ROLE_KEY are set.');
  process.exit(1);
}

// Create Supabase client with service role key for admin privileges
const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function applyCompleteRlsRebuild() {
  try {
    console.log('Starting complete RLS policy rebuild...');
    
    // Read the SQL file
    const sqlFilePath = path.join(process.cwd(), 'supabase/migrations/20240901000003_complete_rls_rebuild.sql');
    const sql = fs.readFileSync(sqlFilePath, 'utf8');
    
    // Execute the SQL
    const { error } = await supabase.rpc('exec_sql', { sql });

    if (error) {
      console.error('Error applying complete RLS rebuild:', error);
      return;
    }

    console.log('Successfully rebuilt all RLS policies!');
    
    // Verify the policies
    console.log('Verifying new policies...');
    
    const { data: policies, error: policiesError } = await supabase.rpc('exec_sql', { 
      sql: `
        SELECT schemaname, tablename, policyname, cmd 
        FROM pg_policies 
        WHERE schemaname = 'public' 
        ORDER BY tablename, cmd;
      `
    });
    
    if (policiesError) {
      console.error('Error verifying policies:', policiesError);
    } else {
      console.log('New policies created:');
      console.log(policies);
    }
    
    console.log('RLS policy rebuild complete. The application should now work without recursion errors.');
    
  } catch (error) {
    console.error('Error:', error);
  }
}

applyCompleteRlsRebuild();
