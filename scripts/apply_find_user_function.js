// This script applies the find_user_id_by_email function to your Supabase project
// Run with: node scripts/apply_find_user_function.js

import { createClient } from '@supabase/supabase-js';
import fs from 'fs';
import path from 'path';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

const supabaseUrl = process.env.VITE_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('Missing Supabase environment variables. Make sure VITE_SUPABASE_URL and SUPABASE_SERVICE_ROLE_KEY are set.');
  process.exit(1);
}

// Create Supabase client with service role key for admin privileges
const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function applyFindUserFunction() {
  try {
    console.log('Applying find_user_id_by_email function to Supabase...');

    // Read the SQL file
    const sqlFilePath = path.join(process.cwd(), 'supabase/migrations/20240820000000_create_find_user_by_email_function.sql');
    const sql = fs.readFileSync(sqlFilePath, 'utf8');

    // Execute the SQL using RPC
    const { error } = await supabase.rpc('exec_sql', { sql });

    if (error) {
      console.error('Error applying function:', error);
      return;
    }

    console.log('Successfully applied find_user_id_by_email function!');

    // Test the function with a known email
    console.log('Testing the function...');
    const testEmail = '<EMAIL>'; // Replace with a known email in your system
    
    const { data, error: testError } = await supabase
      .rpc('find_user_id_by_email', { email_to_find: testEmail });

    if (testError) {
      console.error('Error testing function:', testError);
    } else {
      console.log('Function test result:', data);
      if (data) {
        console.log('Function is working correctly!');
        console.log('User ID for', testEmail, ':', data);
      } else {
        console.log('No user found with email:', testEmail);
        console.log('This is expected if the test email does not exist in your system.');
      }
    }

  } catch (error) {
    console.error('Error in applyFindUserFunction:', error);
  }
}

// Run the function
applyFindUserFunction();
