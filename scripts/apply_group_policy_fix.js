// <PERSON><PERSON><PERSON> to apply the group policy fix to your Supabase project
// Run with: node scripts/apply_group_policy_fix.js

import { createClient } from '@supabase/supabase-js';
import fs from 'fs';
import path from 'path';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

const supabaseUrl = process.env.VITE_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('Missing Supabase environment variables. Make sure VITE_SUPABASE_URL and SUPABASE_SERVICE_ROLE_KEY are set.');
  process.exit(1);
}

// Create Supabase client with service role key for admin privileges
const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function applyGroupPolicyFix() {
  try {
    console.log('Applying group policy fix to Supabase...');

    // SQL to add the policy
    const sql = `
      -- Add a policy to allow all group members to view their groups
      DO $$
      BEGIN
          -- Check if the policy already exists
          IF NOT EXISTS (
              SELECT 1 FROM pg_policies 
              WHERE schemaname = 'public' 
              AND tablename = 'friend_groups' 
              AND policyname = 'Group Members Can View Groups'
          ) THEN
              CREATE POLICY "Group Members Can View Groups"
              ON friend_groups
              FOR SELECT
              USING (
                EXISTS (
                  SELECT 1 FROM group_members
                  WHERE group_id = friend_groups.id
                  AND user_id = auth.uid()
                )
              );
              
              RAISE NOTICE 'Created policy "Group Members Can View Groups" on friend_groups table';
          ELSE
              RAISE NOTICE 'Policy "Group Members Can View Groups" already exists on friend_groups table';
          END IF;
      END
      $$;
    `;

    // Execute the SQL directly
    const { error } = await supabase.rpc('exec_sql', { sql });

    if (error) {
      console.error('Error applying policy:', error);
      return;
    }

    console.log('Successfully applied group policy fix!');
    console.log('Collaborators and guests should now be able to see their groups.');
  } catch (error) {
    console.error('Error:', error);
  }
}

applyGroupPolicyFix();
