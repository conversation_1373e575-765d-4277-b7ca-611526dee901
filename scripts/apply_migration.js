// Script to apply the check_table_exists function to the remote Supabase instance
import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';
import fs from 'fs';
import path from 'path';

// Load environment variables
dotenv.config();

const supabaseUrl = process.env.VITE_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('Missing Supabase environment variables');
  process.exit(1);
}

// Create Supabase client with service role key for admin access
const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function applyMigration() {
  try {
    console.log('Applying fixed check_table_exists function...');

    // Read the fixed migration SQL
    const migrationPath = path.join(process.cwd(), 'fix_user_preferences.sql');
    const migrationSql = fs.readFileSync(migrationPath, 'utf8');

    // Split the SQL into individual statements
    const statements = migrationSql.split(';').filter(stmt => stmt.trim().length > 0);

    console.log(`Executing ${statements.length} SQL statements...`);

    // Execute each statement separately
    for (let i = 0; i < statements.length; i++) {
      const stmt = statements[i].trim() + ';';
      console.log(`Executing statement ${i + 1}/${statements.length}...`);

      try {
        // Use Postgres REST API to execute the statement
        const { error } = await supabase.rpc('exec_sql', { sql: stmt });

        if (error) {
          console.error(`Error executing statement ${i + 1}:`, error);
          // Continue with the next statement
        }
      } catch (stmtError) {
        console.error(`Exception executing statement ${i + 1}:`, stmtError);
        // Continue with the next statement
      }
    }

    console.log('Migration applied successfully');

    // Verify the function exists by checking if user_preferences table exists
    const { data, error: verifyError } = await supabase.from('user_preferences').select('count(*)', { count: 'exact', head: true });

    if (verifyError) {
      console.error('Error verifying user_preferences table:', verifyError);
    } else {
      console.log('user_preferences table exists and is accessible');
    }

    console.log('Migration completed successfully');
  } catch (error) {
    console.error('Error in applyMigration:', error);
  }
}

// Run the migration
applyMigration();
