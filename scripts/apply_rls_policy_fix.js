// <PERSON>ript to fix recursive RLS policy issues
// Run with: node scripts/apply_rls_policy_fix.js

import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';
import fs from 'fs';
import path from 'path';

// Load environment variables
dotenv.config();

const supabaseUrl = process.env.VITE_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('Missing Supabase environment variables. Make sure VITE_SUPABASE_URL and SUPABASE_SERVICE_ROLE_KEY are set.');
  process.exit(1);
}

// Create Supabase client with service role key for admin privileges
const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function applyRlsPolicyFix() {
  try {
    console.log('Applying RLS policy fixes to resolve recursion issues...');
    
    // Read the SQL file
    const sqlFilePath = path.join(process.cwd(), 'supabase/migrations/20240901000001_fix_recursive_rls_policies.sql');
    const sql = fs.readFileSync(sqlFilePath, 'utf8');
    
    // Execute the SQL
    const { error } = await supabase.rpc('exec_sql', { sql });

    if (error) {
      console.error('Error applying RLS policy fixes:', error);
      return;
    }

    console.log('Successfully applied RLS policy fixes!');
    console.log('The recursion issues should now be resolved.');
    console.log('You can now access your groups and group members without infinite recursion errors.');
    
  } catch (error) {
    console.error('Error:', error);
  }
}

applyRlsPolicyFix();
