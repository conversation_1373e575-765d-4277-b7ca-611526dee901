// This script applies the get_user_details_by_id function to your Supabase project
// Run with: node scripts/apply_user_details_function.js

import { createClient } from '@supabase/supabase-js';
import fs from 'fs';
import path from 'path';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

const supabaseUrl = process.env.VITE_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('Missing Supabase environment variables. Make sure VITE_SUPABASE_URL and SUPABASE_SERVICE_ROLE_KEY are set.');
  process.exit(1);
}

// Create Supabase client with service role key for admin privileges
const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function applyUserDetailsFunction() {
  try {
    console.log('Applying get_user_details_by_id function to Supabase...');

    // Read the SQL file
    const sqlFilePath = path.join(process.cwd(), 'supabase/migrations/20240815000000_create_get_user_details_function.sql');
    const sql = fs.readFileSync(sqlFilePath, 'utf8');

    // Execute the SQL using RPC
    const { error } = await supabase.rpc('exec_sql', { sql });

    if (error) {
      console.error('Error applying function:', error);
      return;
    }

    console.log('Successfully applied get_user_details_by_id function!');

    // Test the function
    console.log('Testing the function...');
    const { data: session } = await supabase.auth.getSession();

    if (session?.session?.user) {
      const userId = session.session.user.id;
      const { data, error: testError } = await supabase
        .rpc('get_user_details_by_id', { user_id_to_find: userId });

      if (testError) {
        console.error('Error testing function:', testError);
      } else {
        console.log('Function test result:', data);
        if (data && data.email) {
          console.log('Function is working correctly!');
          console.log('Email:', data.email);
          console.log('Display name:', data.display_name || '(not set)');
        } else {
          console.warn('Function returned data but in unexpected format:', data);
        }
      }
    } else {
      console.log('No authenticated user to test with.');
    }

  } catch (error) {
    console.error('Error in applyUserDetailsFunction:', error);
  }
}

// Run the function
applyUserDetailsFunction();
