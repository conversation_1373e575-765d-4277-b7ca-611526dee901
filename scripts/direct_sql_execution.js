// Script to directly execute SQL statements on the remote Supabase instance
import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';
import fs from 'fs';
import path from 'path';

// Load environment variables
dotenv.config();

const supabaseUrl = process.env.VITE_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('Missing Supabase environment variables');
  process.exit(1);
}

// Create Supabase client with service role key for admin access
const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function executeSQL() {
  try {
    console.log('Checking if user_preferences table exists...');
    
    // Check if the user_preferences table exists
    const { data, error } = await supabase
      .from('user_preferences')
      .select('count(*)', { count: 'exact', head: true });
    
    if (error) {
      console.error('Error checking user_preferences table:', error);
      console.log('This confirms there is an issue with the table access.');
    } else {
      console.log('user_preferences table exists and is accessible!');
      console.log('Count result:', data);
    }
    
    // Check RLS policies
    console.log('\nChecking RLS policies...');
    const { data: policies, error: policiesError } = await supabase
      .from('pg_policies')
      .select('*')
      .eq('tablename', 'user_preferences');
    
    if (policiesError) {
      console.error('Error checking RLS policies:', policiesError);
    } else {
      console.log('RLS policies for user_preferences:', policies);
    }
    
    // Check auth status
    console.log('\nChecking authentication status...');
    const { data: authData, error: authError } = await supabase.auth.getUser();
    
    if (authError) {
      console.error('Authentication error:', authError);
    } else {
      console.log('Authenticated as:', authData.user.id);
    }
    
    console.log('\nDiagnostic checks completed.');
  } catch (error) {
    console.error('Error in executeSQL:', error);
  }
}

// Run the SQL execution
executeSQL();
