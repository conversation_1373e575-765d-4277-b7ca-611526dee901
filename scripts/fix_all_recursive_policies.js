// Script to fix all recursive policy issues
// Run with: node scripts/fix_all_recursive_policies.js

import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

const supabaseUrl = process.env.VITE_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('Missing Supabase environment variables. Make sure VITE_SUPABASE_URL and SUPABASE_SERVICE_ROLE_KEY are set.');
  process.exit(1);
}

// Create Supabase client with service role key for admin privileges
const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function fixAllRecursivePolicies() {
  try {
    console.log('Fixing all recursive policy issues...');

    // Drop all potentially problematic policies
    const dropPoliciesSql = `
      -- Drop friend_groups policies
      DROP POLICY IF EXISTS "Group Creator Full Access" ON friend_groups;
      DROP POLICY IF EXISTS "Group Admin Full Access" ON friend_groups;
      DROP POLICY IF EXISTS "Group Members Can View Groups" ON friend_groups;
      DROP POLICY IF EXISTS "Members View Groups" ON friend_groups;

      -- Drop group_members policies
      DROP POLICY IF EXISTS "View Own Memberships" ON group_members;
      DROP POLICY IF EXISTS "Admins Manage Memberships" ON group_members;
    `;

    // Execute the drop SQL
    const { error: dropError } = await supabase.rpc('exec_sql', { sql: dropPoliciesSql });

    if (dropError) {
      console.error('Error dropping policies:', dropError);
      return;
    }

    console.log('Successfully dropped potentially problematic policies.');

    // Now create new non-recursive policies
    const createPoliciesSql = `
      -- Create simplified policies for friend_groups

      -- 1. Allow creators to manage their groups
      CREATE POLICY "Creators Manage Groups"
      ON friend_groups
      FOR ALL
      USING (created_by = auth.uid());

      -- 2. Allow members to view groups they belong to
      CREATE POLICY "Members View Groups"
      ON friend_groups
      FOR SELECT
      USING (
        id IN (
          SELECT group_id FROM group_members
          WHERE user_id = auth.uid()
        )
      );

      -- Create simplified policies for group_members

      -- 1. Allow users to view their own memberships
      CREATE POLICY "View Own Memberships"
      ON group_members
      FOR SELECT
      USING (user_id = auth.uid());

      -- 2. Allow group creators to manage all memberships in their groups
      CREATE POLICY "Creators Manage All Memberships"
      ON group_members
      FOR ALL
      USING (
        group_id IN (
          SELECT id FROM friend_groups
          WHERE created_by = auth.uid()
        )
      );

      -- 3. Allow admins to manage memberships in groups where they are admins
      CREATE POLICY "Admins Manage Group Memberships"
      ON group_members
      FOR ALL
      USING (
        group_id IN (
          SELECT group_id FROM group_members
          WHERE user_id = auth.uid()
          AND role = 'Admin'
        )
      );
    `;

    // Execute the create SQL
    const { error: createError } = await supabase.rpc('exec_sql', { sql: createPoliciesSql });

    if (createError) {
      console.error('Error creating new policies:', createError);
      return;
    }

    console.log('Successfully created new non-recursive policies!');
    console.log('All users should now be able to access their groups without recursion issues.');
  } catch (error) {
    console.error('Error:', error);
  }
}

fixAllRecursivePolicies();
