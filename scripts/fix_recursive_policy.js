// <PERSON>ript to fix the recursive policy issue
// Run with: node scripts/fix_recursive_policy.js

import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

const supabaseUrl = process.env.VITE_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('Missing Supabase environment variables. Make sure VITE_SUPABASE_URL and SUPABASE_SERVICE_ROLE_KEY are set.');
  process.exit(1);
}

// Create Supabase client with service role key for admin privileges
const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function fixRecursivePolicy() {
  try {
    console.log('Fixing recursive policy issue...');

    // First, drop the problematic policy
    const dropSql = `
      DROP POLICY IF EXISTS "Group Members Can View Groups" ON friend_groups;
    `;

    // Execute the drop SQL
    const { error: dropError } = await supabase.rpc('exec_sql', { sql: dropSql });

    if (dropError) {
      console.error('Error dropping policy:', dropError);
      return;
    }

    console.log('Successfully dropped the problematic policy.');

    // Now create a better policy that avoids recursion
    const createSql = `
      -- Create a new policy for group members to view groups
      CREATE POLICY "Members View Groups" 
      ON friend_groups
      FOR SELECT
      USING (
        id IN (
          SELECT group_id FROM group_members 
          WHERE user_id = auth.uid()
        )
      );
    `;

    // Execute the create SQL
    const { error: createError } = await supabase.rpc('exec_sql', { sql: createSql });

    if (createError) {
      console.error('Error creating new policy:', createError);
      return;
    }

    console.log('Successfully created new non-recursive policy!');
    console.log('Collaborators and guests should now be able to see their groups without recursion issues.');
  } catch (error) {
    console.error('Error:', error);
  }
}

fixRecursivePolicy();
