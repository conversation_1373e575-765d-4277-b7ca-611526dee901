import fs from 'fs';
import path from 'path';
import { parse } from 'csv-parse/sync';
import { fileURLToPath } from 'url';

// Get the directory name in ESM
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Function to parse Spanish date format
function parseSpanishDate(dateStr, year) {
  const monthMap = {
    'enero': '01',
    'febrero': '02',
    'marzo': '03',
    'abril': '04',
    'mayo': '05',
    'junio': '06',
    'julio': '07',
    'agosto': '08',
    'septiembre': '09',
    'octubre': '10',
    'noviembre': '11',
    'diciembre': '12'
  };

  // Extract day and month
  const parts = dateStr.split(' ');
  const day = parts[1].padStart(2, '0');
  const month = monthMap[parts[2]];

  // Create ISO date string
  return `${year}-${month}-${day}T12:00:00Z`;
}

// Function to extract unique player names
function extractPlayers(records) {
  const playerSet = new Set();

  records.forEach(record => {
    // Extract players from team 1
    const team1 = record['Equipo 1'].split(',').map(p => p.trim());
    // Extract players from team 2
    const team2 = record['Equipo 2'].split(',').map(p => p.trim());

    // Add all players to the set
    [...team1, ...team2].forEach(player => {
      // Skip "empate" entries
      if (player !== 'empate') {
        playerSet.add(player);
      }
    });
  });

  // Convert set to array and sort alphabetically
  return Array.from(playerSet).sort();
}

// Function to create player objects
function createPlayerObjects(playerNames) {
  return playerNames.map(name => {
    // Generate random skill values between 70-95
    const skills = Math.floor(Math.random() * 26) + 70;
    const effort = Math.floor(Math.random() * 26) + 70;
    const stamina = Math.floor(Math.random() * 26) + 70;

    return {
      name,
      skills,
      effort,
      stamina
    };
  });
}

// Function to create match objects
function createMatchObjects(records, playerMap) {
  return records.map(record => {
    // Parse date
    const matchDate = parseSpanishDate(record['Fecha'], record['Año']);

    // Extract team players
    const teamA = record['Equipo 1'].split(',').map(p => p.trim());
    const teamB = record['Equipo 2'].split(',').map(p => p.trim());

    // Map player names to indices
    const teamAIndices = teamA.map(name => playerMap.get(name));
    const teamBIndices = teamB.map(name => playerMap.get(name));

    // Determine winner
    let winner = null;
    if (record['Ganador'].includes('empate')) {
      winner = 'Draw';
    } else {
      const winningTeam = record['Ganador'].split(',').map(p => p.trim());
      // Check if winning team matches team A or team B
      const isTeamAWinner = teamA.every(player => winningTeam.includes(player));
      winner = isTeamAWinner ? 'A' : 'B';
    }

    // Create match object
    return {
      match_date: matchDate,
      teama: teamAIndices,
      teamb: teamBIndices,
      scorea: winner === 'A' ? 3 : winner === 'Draw' ? 2 : 1,
      scoreb: winner === 'B' ? 3 : winner === 'Draw' ? 2 : 1,
      winner,
      goalscorers: [],
      youtubelink: ""
    };
  });
}

// Main function to transform CSV to JSON
function transformCsvToJson(inputPath, outputPath) {
  try {
    // Read and parse CSV file
    const csvData = fs.readFileSync(inputPath, 'utf8');
    const records = parse(csvData, {
      columns: true,
      skip_empty_lines: true
    });

    // Extract unique players
    const playerNames = extractPlayers(records);

    // Create player objects
    const players = createPlayerObjects(playerNames);

    // Create player name to index map
    const playerMap = new Map();
    playerNames.forEach((name, index) => {
      playerMap.set(name, index);
    });

    // Create match objects
    const matches = createMatchObjects(records, playerMap);

    // Create final JSON structure
    const jsonOutput = {
      players,
      matches
    };

    // Write to output file
    fs.writeFileSync(outputPath, JSON.stringify(jsonOutput, null, 2));

    console.log(`Transformation complete. Output saved to ${outputPath}`);
    console.log(`Transformed ${players.length} players and ${matches.length} matches.`);

    return jsonOutput;
  } catch (error) {
    console.error('Error transforming CSV to JSON:', error);
    throw error;
  }
}

// Execute the transformation
const inputPath = path.join(__dirname, '../docs/transform data test.csv');
const outputPath = path.join(__dirname, '../public/transformed-data.json');

transformCsvToJson(inputPath, outputPath);
