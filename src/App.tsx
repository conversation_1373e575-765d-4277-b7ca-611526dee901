import React, { Suspense, lazy, useEffect } from "react";
import { Toaster } from "@/components/ui/toaster";
import { TooltipProvider } from "@/components/ui/tooltip";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
// Import devtools only in development
const ReactQueryDevtools = process.env.NODE_ENV === 'production'
  ? () => null // Return null component in production
  : React.lazy(() => import('@tanstack/react-query-devtools').then(module => ({ default: module.ReactQueryDevtools })));
import { BrowserRouter, Routes, Route } from "react-router-dom";
import { Analytics } from "@vercel/analytics/react";
import ProtectedRoute from "@/components/auth/ProtectedRoute";
import { AuthProvider } from "@/context/AuthContext";
import { GroupProvider } from '@/lib/context/GroupContext';
import { UIProvider } from '@/context/UIContext';
import { OfflineProvider } from '@/context/OfflineContext';
import { UserPreferencesProvider } from '@/context/UserPreferencesContext';
import { ThemeProvider, setupSystemThemeListener } from '@/context/ThemeContext';
import { SharedLinkProvider } from '@/context/SharedLinkContext';
import { LanguageProvider } from '@/context/LanguageContext';
import FeedbackNotificationInitializer from '@/components/feedback/FeedbackNotificationInitializer';
import { AppInstallPrompt } from '@/components/ui/app-install-prompt';

// Loading fallback
import { Loader2 } from "lucide-react";

// Lazy-loaded components
const UnifiedLayout = lazy(() => import("@/components/layout/UnifiedLayout"));
const LandingPage = lazy(() => import("@/pages/LandingPage"));
const LoginPage = lazy(() => import("@/pages/LoginPage"));
const SignUpPage = lazy(() => import("@/pages/SignUpPage"));
const ResetPasswordPage = lazy(() => import("@/pages/ResetPasswordPage"));
const DashboardPage = lazy(() => import("@/pages/DashboardPage"));
const PlayersPage = lazy(() => import("@/pages/PlayersPage"));
const MatchesPage = lazy(() => import("@/pages/MatchesPage"));
const LeaderboardPage = lazy(() => import("@/pages/LeaderboardPage"));
const TeamGeneratorPage = lazy(() => import("@/pages/TeamGeneratorPage"));
const ChemistryPage = lazy(() => import("@/pages/ChemistryPage"));
const ProfilePage = lazy(() => import("@/pages/ProfilePage"));
const NotFound = lazy(() => import("@/pages/NotFound"));
const GroupSelectionPage = lazy(() => import("@/pages/GroupSelectionPage"));
// Name-based import is now integrated into the standard import

// Shared (view-only) pages
const SharedEntryPage = lazy(() => import("@/pages/shared/SharedEntryPage"));
const SharedDashboardPage = lazy(() => import("@/pages/shared/SharedDashboardPage"));
const SharedPlayersPage = lazy(() => import("@/pages/shared/SharedPlayersPage"));
const SharedMatchesPage = lazy(() => import("@/pages/shared/SharedMatchesPage"));
const SharedLeaderboardPage = lazy(() => import("@/pages/shared/SharedLeaderboardPage"));
const SharedTeamGeneratorPage = lazy(() => import("@/pages/shared/SharedTeamGeneratorPage"));
const SharedChemistryPage = lazy(() => import("@/pages/shared/SharedChemistryPage"));

// Sharing components
const ShareLinkViewer = lazy(() => import("@/components/sharing/ShareLinkViewer"));

// Create a client
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      staleTime: 5 * 60 * 1000, // 5 minutes
      retry: 1,
      refetchOnWindowFocus: false,
      refetchOnReconnect: true,
      gcTime: 24 * 60 * 60 * 1000, // 24 hours (renamed from cacheTime in v5)
    },
  },
});

// Ensure the queryClient is created only once
const getQueryClient = (() => {
  return queryClient;
})();

// Loading fallback component
const PageLoader = () => {
  // We can't use useTranslation here because it would create a circular dependency
  // Instead, we'll use a simple loading indicator without text
  return (
    <div className="flex items-center justify-center min-h-screen">
      <div className="flex flex-col items-center gap-2">
        <Loader2 className="h-8 w-8 animate-spin text-primary" />
      </div>
    </div>
  );
};

const App = () => {
  // Initialize system theme listener
  useEffect(() => {
    const cleanup = setupSystemThemeListener();
    return cleanup;
  }, []);

  return (
    <QueryClientProvider client={getQueryClient}>
      <BrowserRouter>
        <TooltipProvider>
          <AuthProvider>
            <GroupProvider>
              <OfflineProvider>
                <UserPreferencesProvider>
                  <ThemeProvider>
                    <LanguageProvider>
                      <SharedLinkProvider>
                        <UIProvider>
                          <Suspense fallback={<PageLoader />}>
                          <Routes>
                          <Route path="/" element={<LandingPage />} />
                          <Route path="/login" element={<LoginPage />} />
                          <Route path="/signup" element={<SignUpPage />} />
                          <Route path="/reset-password" element={<ResetPasswordPage />} />
                          <Route path="/group-selection" element={
                            <ProtectedRoute>
                              <GroupSelectionPage />
                            </ProtectedRoute>
                          } />
                          {/* Name-based import is now integrated into the standard import */}

                          {/* Protected Routes */}
                          <Route path="/dashboard" element={
                            <ProtectedRoute>
                              <UnifiedLayout>
                                <DashboardPage />
                              </UnifiedLayout>
                            </ProtectedRoute>
                          } />
                          <Route path="/players" element={
                            <ProtectedRoute>
                              <UnifiedLayout>
                                <PlayersPage />
                              </UnifiedLayout>
                            </ProtectedRoute>
                          } />
                          <Route path="/matches" element={
                            <ProtectedRoute>
                              <UnifiedLayout>
                                <MatchesPage />
                              </UnifiedLayout>
                            </ProtectedRoute>
                          } />
                          {/* Leaderboard, Chemistry, and Team Generator are now integrated into the Dashboard */}
                          <Route path="/profile" element={
                            <ProtectedRoute>
                              <UnifiedLayout>
                                <ProfilePage />
                              </UnifiedLayout>
                            </ProtectedRoute>
                          } />
                          {/* Shared (View-Only) Routes */}
                          <Route path="/shared/:groupId" element={<SharedEntryPage />} />
                          <Route path="/shared/:groupId/dashboard" element={<SharedDashboardPage />} />
                          <Route path="/shared/:groupId/players" element={<SharedPlayersPage />} />
                          <Route path="/shared/:groupId/matches" element={<SharedMatchesPage />} />
                          {/* Leaderboard, Chemistry, and Team Generator are now integrated into the Shared Dashboard */}

                          {/* Enhanced Sharing Routes */}
                          <Route path="/s/:linkId" element={<ShareLinkViewer />} />

                          <Route path="*" element={<NotFound />} />
                        </Routes>
                          </Suspense>
                          <Toaster />
                          <Analytics />
                          <FeedbackNotificationInitializer />
                          <AppInstallPrompt />
                        </UIProvider>
                      </SharedLinkProvider>
                    </LanguageProvider>
                  </ThemeProvider>
                </UserPreferencesProvider>
              </OfflineProvider>
            </GroupProvider>
          </AuthProvider>
        </TooltipProvider>
        {/* Only include devtools in development */}
        {process.env.NODE_ENV !== 'production' && (
          <Suspense fallback={null}>
            <ReactQueryDevtools initialIsOpen={false} />
          </Suspense>
        )}
      </BrowserRouter>
    </QueryClientProvider>
  );
};

export default App;

