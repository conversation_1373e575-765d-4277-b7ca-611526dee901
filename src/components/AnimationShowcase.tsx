import React, { useState } from 'react';
import { <PERSON>, Card<PERSON>ontent, CardH<PERSON>er, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { AnimatedButton } from '@/components/ui/animated-button';
import { Input } from '@/components/ui/input';
import { EnhancedInput } from '@/components/ui/enhanced-input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { TouchFeedback } from '@/components/ui/touch-feedback';
import { StatCard } from '@/components/StatCard';
import { EnhancedCard } from '@/components/ui/enhanced-card';
import { 
  CardSkeleton, 
  StatCardSkeleton, 
  PlayerCardSkeleton,
  TableSkeleton 
} from '@/components/ui/skeletons';
import { useOptimizedAnimation } from '@/utils/animation-utils';
import { 
  Heart, 
  Star, 
  Zap, 
  Trophy, 
  Users, 
  TrendingUp,
  Play,
  Pause,
  RotateCcw
} from 'lucide-react';

const AnimationShowcase: React.FC = () => {
  const [isLoading, setIsLoading] = useState(false);
  const [showSkeletons, setShowSkeletons] = useState(false);
  const [inputValue, setInputValue] = useState('');
  const [selectValue, setSelectValue] = useState('');
  const { getAnimationClass, getStaggerStyle, isReducedMotion } = useOptimizedAnimation();

  const handleLoadingTest = () => {
    setIsLoading(true);
    setTimeout(() => setIsLoading(false), 3000);
  };

  const handleSkeletonTest = () => {
    setShowSkeletons(true);
    setTimeout(() => setShowSkeletons(false), 5000);
  };

  return (
    <div className="container mx-auto p-6 space-y-8">
      <div className="text-center space-y-4">
        <h1 className="text-3xl font-bold gradient-text-primary">
          Animation Showcase
        </h1>
        <p className="text-muted-foreground">
          Testing all animation components and interactions
          {isReducedMotion && (
            <span className="block text-amber-600 font-medium mt-2">
              ⚠️ Reduced motion is enabled - animations are disabled
            </span>
          )}
        </p>
      </div>

      {/* Control Panel */}
      <Card className="animate-fade-in">
        <CardHeader>
          <CardTitle>Animation Controls</CardTitle>
        </CardHeader>
        <CardContent className="flex flex-wrap gap-4">
          <Button onClick={handleLoadingTest} disabled={isLoading}>
            {isLoading ? <Pause className="w-4 h-4" /> : <Play className="w-4 h-4" />}
            {isLoading ? 'Loading...' : 'Test Loading States'}
          </Button>
          <Button onClick={handleSkeletonTest} variant="outline">
            <RotateCcw className="w-4 h-4" />
            Test Skeleton Loading
          </Button>
        </CardContent>
      </Card>

      {/* Card Animations */}
      <section className="space-y-4">
        <h2 className="text-2xl font-semibold">Card Hover Animations</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {[1, 2, 3].map((index) => (
            <EnhancedCard 
              key={index} 
              hoverable 
              className={getAnimationClass('fadeIn')}
              style={getStaggerStyle(index)}
            >
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Heart className="w-5 h-5 text-soccer-primary" />
                  Enhanced Card {index}
                </CardTitle>
              </CardHeader>
              <CardContent>
                <p>Hover over this card to see the animation effect.</p>
              </CardContent>
            </EnhancedCard>
          ))}
        </div>
      </section>

      {/* Stat Cards */}
      <section className="space-y-4">
        <h2 className="text-2xl font-semibold">Stat Card Animations</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          {showSkeletons ? (
            <>
              <StatCardSkeleton />
              <StatCardSkeleton />
              <StatCardSkeleton />
              <StatCardSkeleton />
            </>
          ) : (
            <>
              <StatCard
                title="Total Players"
                value="24"
                icon={<Users className="w-4 h-4" />}
                trend="up"
                trendValue="+2 this week"
              />
              <StatCard
                title="Matches Played"
                value="156"
                icon={<Trophy className="w-4 h-4" />}
                trend="up"
                trendValue="+12 this month"
              />
              <StatCard
                title="Win Rate"
                value="68%"
                icon={<TrendingUp className="w-4 h-4" />}
                trend="up"
                trendValue="+5% improvement"
              />
              <StatCard
                title="Active Streak"
                value="7"
                icon={<Zap className="w-4 h-4" />}
                trend="neutral"
                trendValue="Current streak"
              />
            </>
          )}
        </div>
      </section>

      {/* Button Animations */}
      <section className="space-y-4">
        <h2 className="text-2xl font-semibold">Button Animations</h2>
        <div className="flex flex-wrap gap-4">
          <Button className={getAnimationClass('hoverScale')}>
            Default Button
          </Button>
          <Button variant="outline" className={getAnimationClass('hoverLift')}>
            Outline Button
          </Button>
          <Button variant="secondary" className={getAnimationClass('hoverGlow')}>
            Secondary Button
          </Button>
          <AnimatedButton animation="scale" loading={isLoading}>
            Animated Button
          </AnimatedButton>
          <AnimatedButton animation="glow" variant="outline">
            Glow Animation
          </AnimatedButton>
          <AnimatedButton animation="spring" variant="secondary">
            Spring Animation
          </AnimatedButton>
        </div>
      </section>

      {/* Form Input Animations */}
      <section className="space-y-4">
        <h2 className="text-2xl font-semibold">Form Input Animations</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div className="space-y-4">
            <Input
              placeholder="Standard input with animations"
              value={inputValue}
              onChange={(e) => setInputValue(e.target.value)}
            />
            <EnhancedInput
              label="Enhanced Input"
              placeholder="Enhanced input with animations"
              helperText="This input has enhanced animations"
              startIcon={<Star className="w-4 h-4" />}
            />
            <Select value={selectValue} onValueChange={setSelectValue}>
              <SelectTrigger>
                <SelectValue placeholder="Select an option" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="option1">Option 1</SelectItem>
                <SelectItem value="option2">Option 2</SelectItem>
                <SelectItem value="option3">Option 3</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>
      </section>

      {/* Touch Feedback */}
      <section className="space-y-4">
        <h2 className="text-2xl font-semibold">Touch Feedback (Mobile)</h2>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <TouchFeedback onTap={() => alert('Tapped!')} className="p-4 bg-soccer-primary/10 rounded-lg text-center">
            <div className="space-y-2">
              <Heart className="w-8 h-8 mx-auto text-soccer-primary" />
              <p className="font-medium">Tap Me</p>
              <p className="text-sm text-muted-foreground">Touch feedback with ripple</p>
            </div>
          </TouchFeedback>
          
          <TouchFeedback 
            onLongPress={() => alert('Long pressed!')} 
            className="p-4 bg-secondary/10 rounded-lg text-center"
            longPressDelay={1000}
          >
            <div className="space-y-2">
              <Star className="w-8 h-8 mx-auto text-secondary" />
              <p className="font-medium">Long Press</p>
              <p className="text-sm text-muted-foreground">Hold for 1 second</p>
            </div>
          </TouchFeedback>
          
          <TouchFeedback 
            onTap={() => alert('No ripple!')} 
            ripple={false}
            className="p-4 bg-destructive/10 rounded-lg text-center"
          >
            <div className="space-y-2">
              <Zap className="w-8 h-8 mx-auto text-destructive" />
              <p className="font-medium">No Ripple</p>
              <p className="text-sm text-muted-foreground">Scale animation only</p>
            </div>
          </TouchFeedback>
        </div>
      </section>

      {/* Loading States */}
      <section className="space-y-4">
        <h2 className="text-2xl font-semibold">Loading State Animations</h2>
        {showSkeletons ? (
          <div className="space-y-6">
            <CardSkeleton />
            <PlayerCardSkeleton />
            <TableSkeleton rowCount={3} />
          </div>
        ) : (
          <Card>
            <CardContent className="p-6">
              <p>Click "Test Skeleton Loading" to see loading animations</p>
            </CardContent>
          </Card>
        )}
      </section>
    </div>
  );
};

export default AnimationShowcase;
