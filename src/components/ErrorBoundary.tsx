import React from 'react';
import { Button } from '@/components/ui/button';
import { useToast } from '@/hooks/use-toast';

interface Props {
  children: React.ReactNode;
  fallback?: React.ReactNode;
  maxRetries?: number;
  onError?: (error: Error, info: React.ErrorInfo) => void;
}

interface State {
  hasError: boolean;
  error?: Error;
  errorInfo?: React.ErrorInfo;
  retryCount: number;
}

class ErrorBoundary extends React.Component<Props, State> {
  constructor(props: Props) {
    super(props);
    this.state = { hasError: false, retryCount: 0 };
  }

  static getDerivedStateFromError(error: Error): State {
    return {
      hasError: true,
      error,
    };
  }

  retry = () => {
    const maxRetries = this.props.maxRetries ?? 3;
    if (this.state.retryCount >= maxRetries) {
      console.error('Max retries reached, please refresh the page');
      return;
    }

    this.setState(prev => ({
      hasError: false,
      error: undefined,
      errorInfo: undefined,
      retryCount: prev.retryCount + 1
    }));
  };

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    console.error('Error caught by boundary:', error, errorInfo);
    
    // Report error to monitoring service
    this.reportError(error, errorInfo);
    
    this.setState({ errorInfo });
    this.props.onError?.(error, errorInfo);
  }

  private reportError(error: Error, errorInfo: React.ErrorInfo) {
    // Add your error reporting service here
    // Example: Sentry, LogRocket, etc.
    const errorData = {
      name: error.name,
      message: error.message,
      stack: error.stack,
      componentStack: errorInfo.componentStack,
      timestamp: new Date().toISOString(),
      retryCount: this.state.retryCount
    };

    // For now, just log to console in production
    if (process.env.NODE_ENV === 'production') {
      console.error('Error Report:', errorData);
    }
  }

  render() {
    if (this.state.hasError) {
      const canRetry = (this.props.maxRetries ?? 3) > this.state.retryCount;
      
      return this.props.fallback || (
        <div className="p-6 rounded-lg bg-red-50 border border-red-200">
          <h2 className="text-red-700 font-semibold mb-3">Something went wrong</h2>
          <p className="text-red-600 mb-4">{this.state.error?.message}</p>
          {canRetry ? (
            <Button 
              onClick={this.retry}
              variant="destructive"
              className="hover:bg-red-600"
            >
              Try Again ({this.state.retryCount + 1}/{this.props.maxRetries ?? 3})
            </Button>
          ) : (
            <Button 
              onClick={() => window.location.reload()}
              variant="destructive"
              className="hover:bg-red-600"
            >
              Refresh Page
            </Button>
          )}
          {process.env.NODE_ENV === 'development' && (
            <pre className="mt-4 text-xs text-red-500 overflow-auto">
              {this.state.errorInfo?.componentStack}
            </pre>
          )}
        </div>
      );
    }

    return this.props.children;
  }
}

// Update withErrorBoundary HOC to accept options
export const withErrorBoundary = <P extends object>(
  Component: React.ComponentType<P>,
  options?: {
    fallback?: React.ReactNode;
    maxRetries?: number;
    onError?: Props['onError'];
  }
) => {
  return React.memo((props: P) => (
    <ErrorBoundary {...options}>
      <Component {...props} />
    </ErrorBoundary>
  ));
};

export default ErrorBoundary;
