import { useState, useEffect, useRef } from 'react';
import { useNavigate } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import { useAuth } from '@/context/AuthContext';
import { useGroup } from '@/lib/context/GroupContext';
import { useToast } from '@/hooks/use-toast';
import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { ChevronDown, Users, PlusCircle, UserCog, Edit, Loader2 } from 'lucide-react';
import RoleIndicator from '@/components/groups/RoleIndicator';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogFooter, DialogDescription } from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { updateGroupName, checkUserPermission } from '@/lib/api/groups';

interface Group {
  id: string;
  name: string;
  created_by: string;
  created_at?: string;
}

interface GroupSelectorProps {
  basePath?: string;
}

const GroupSelector = ({
  basePath = ""
}: GroupSelectorProps) => {
  const { t } = useTranslation();
  const [loading, setLoading] = useState(true);
  const { user } = useAuth();
  const { groups, currentGroup, setCurrentGroup, fetchGroups, userRole } = useGroup();
  const { toast } = useToast();
  const navigate = useNavigate();

  // State for editing group name
  const [isEditNameModalOpen, setIsEditNameModalOpen] = useState(false);
  const [newGroupName, setNewGroupName] = useState('');
  const [isUpdatingName, setIsUpdatingName] = useState(false);

  // Use a ref to track initialization state
  const isInitialized = useRef(false);

  // Single effect for initialization
  useEffect(() => {
    // Only run once
    if (isInitialized.current) return;

    const initializeGroups = async () => {
      if (!user?.id) {
        setLoading(false);
        return;
      }

      try {
        // Load groups if needed
        if (groups.length === 0) {
          await fetchGroups();
        }

        // Mark as initialized
        isInitialized.current = true;
      } catch (error: any) {
        console.error('Error initializing groups:', error);
        toast({
          title: t('errors.error'),
          description: error?.message || t('groups.loadError', 'Failed to load groups.'),
          variant: 'destructive',
        });
      } finally {
        setLoading(false);
      }
    };

    initializeGroups();

    // Cleanup function
    return () => {
      // Any cleanup if needed
    };
  }, [user?.id]); // Only depend on user ID

  // Handle group selection
  const handleGroupSelect = (group: Group) => {
    if (!group || (currentGroup && group.id === currentGroup.id)) return;

    try {
      console.log('Selecting group:', group.name);
      setCurrentGroup(group);
      localStorage.setItem('selectedGroupId', group.id);
    } catch (error) {
      console.error('Error selecting group:', error);
      toast({
        title: t('errors.error'),
        description: t('groups.selectError', 'Failed to select group. Please try again.'),
        variant: 'destructive',
      });
    }
  };

  // Navigate to group selection page
  const goToGroupSelection = () => {
    navigate(`${basePath}/group-selection`);
  };

  // Handle opening the edit name modal
  const handleEditNameClick = () => {
    if (!currentGroup) return;
    setNewGroupName(currentGroup.name);
    setIsEditNameModalOpen(true);
  };

  // Handle updating the group name
  const handleUpdateGroupName = async () => {
    if (!currentGroup || !newGroupName.trim() || !user?.id) return;

    setIsUpdatingName(true);
    try {
      // Check if user has admin permission
      const hasPermission = await checkUserPermission(currentGroup.id, user.id, 'Admin');
      if (!hasPermission && currentGroup.created_by !== user.id) {
        throw new Error(t('groups.adminPermissionRequired', 'You need Admin permission to edit the group name'));
      }

      // Update the group name
      const data = await updateGroupName(currentGroup.id, newGroupName);

      if (data && data.length > 0) {
        // Update the local state and context
        const updatedGroup = { ...currentGroup, name: newGroupName };
        setCurrentGroup(updatedGroup);

        // Also update the group in the groups array
        const updatedGroups = groups.map(g =>
          g.id === currentGroup.id ? updatedGroup : g
        );

        // Refresh groups to ensure everything is in sync
        await fetchGroups();

        toast({ title: t('common.success'), description: t('groups.nameUpdateSuccess', 'Group name updated successfully.') });
      }
    } catch (error: any) {
      console.error('Error updating group name:', error);
      toast({
        title: t('errors.error'),
        description: error?.message || t('groups.nameUpdateError', 'Failed to update group name.'),
        variant: 'destructive'
      });
    } finally {
      setIsUpdatingName(false);
      setIsEditNameModalOpen(false);
      setNewGroupName('');
    }
  };

  // Loading state
  if (loading) {
    return (
      <Button
        variant="ghost"
        size="sm"
        disabled
        className="h-8 gap-1 text-xs text-white bg-white/10 dark:bg-gray-800/50"
      >
        <Users className="h-3.5 w-3.5 mr-1" />
        {t('common.loading')}
      </Button>
    );
  }

  // No current group selected
  if (!currentGroup) {
    return (
      <Button
        variant="ghost"
        size="sm"
        onClick={goToGroupSelection}
        className="h-8 gap-1 text-xs text-white bg-white/10 hover:bg-white/20 hover:text-white dark:bg-gray-800/50 dark:hover:bg-gray-700/50"
      >
        <Users className="h-3.5 w-3.5 mr-1" />
        {t('groups.selectGroup')}
      </Button>
    );
  }

  // Normal state with dropdown
  return (
    <>
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button
            variant="ghost"
            size="sm"
            className="h-8 gap-1 text-xs text-white bg-white/10 hover:bg-white/20 hover:text-white dark:bg-gray-800/50 dark:hover:bg-gray-700/50 transition-colors duration-200"
          >
            <Users className="h-3.5 w-3.5 mr-1" />
            {currentGroup.name}
            <ChevronDown className="h-3.5 w-3.5 ml-1" />
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="end" className="w-56" sideOffset={5} collisionPadding={10} forceMount>
          <DropdownMenuLabel className="flex justify-between items-center">
            <span>{t('groups.yourGroups', 'Your Groups')}</span>
            {userRole && currentGroup && <RoleIndicator />}
          </DropdownMenuLabel>
          <DropdownMenuSeparator />
          {groups.map((group) => (
            <DropdownMenuItem
              key={group.id}
              onClick={() => handleGroupSelect(group)}
              className={currentGroup.id === group.id ? "bg-muted" : ""}
            >
              {group.name}
            </DropdownMenuItem>
          ))}
          <DropdownMenuSeparator />
          <DropdownMenuItem onClick={goToGroupSelection} className="text-primary">
            <PlusCircle className="h-4 w-4 mr-2" />
            {t('groups.manageGroups', 'Manage Groups')}
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>

      {/* Edit Group Name Dialog */}
      <Dialog open={isEditNameModalOpen} onOpenChange={setIsEditNameModalOpen}>
        <DialogContent className="sm:max-w-md dark:bg-gray-800 dark:border-gray-700">
          <DialogHeader>
            <DialogTitle className="dark:text-white">
              {t('groups.editGroupName', 'Edit Group Name')}
            </DialogTitle>
            <DialogDescription className="dark:text-gray-300">
              {t('groups.editGroupNameDescription', 'Change the name of your group.')}
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4 py-4">
            <div className="space-y-2">
              <Label htmlFor="edit-group-name" className="dark:text-white">{t('groups.groupName')}</Label>
              <Input
                id="edit-group-name"
                placeholder={t('groups.enterNewGroupName', 'Enter new group name')}
                value={newGroupName}
                onChange={(e) => setNewGroupName(e.target.value)}
                className="dark:bg-gray-700 dark:border-gray-600 dark:text-white dark:placeholder-gray-400"
              />
            </div>
          </div>
          <DialogFooter>
            <Button
              onClick={handleUpdateGroupName}
              disabled={!newGroupName.trim() || isUpdatingName}
              className="bg-soccer-primary hover:bg-soccer-primary/90"
            >
              {isUpdatingName ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  {t('groups.updating', 'Updating...')}
                </>
              ) : (
                t('groups.updateName', 'Update Name')
              )}
            </Button>
            <Button
              variant="outline"
              onClick={() => setIsEditNameModalOpen(false)}
              className="dark:bg-gray-700 dark:border-gray-600 dark:text-white"
            >
              {t('common.cancel')}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  );
};

export default GroupSelector;
