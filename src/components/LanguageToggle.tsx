import React from 'react';
import { useTranslation } from 'react-i18next';
import { Button, ButtonProps } from '@/components/ui/button';
import { cn } from '@/lib/utils';
import { useLanguage } from '@/context/LanguageContext';

interface LanguageToggleProps {
  className?: string;
  variant?: ButtonProps['variant'];
  size?: ButtonProps['size'];
}

const LanguageToggle = ({
  className,
  variant = 'ghost',
  size = 'icon'
}: LanguageToggleProps) => {
  const { language, toggleLanguage } = useLanguage();
  const { t } = useTranslation();

  return (
    <Button
      variant={variant}
      size={size}
      onClick={toggleLanguage}
      className={cn(
        'rounded-full px-3 h-8 text-xs font-semibold flex items-center justify-center transition-all duration-300 hover:scale-110 active:scale-95 min-w-[48px]',
        className
      )}
      aria-label={t('language.toggle', { language: language === 'es' ? 'English' : 'Español' })}
      title={t('language.toggle', { language: language === 'es' ? 'English' : 'Español' })}
    >
      <span className="transition-all duration-300 hover:scale-110">
        {language === 'es' ? 'ESP' : 'ENG'}
      </span>
    </Button>
  );
};

export default LanguageToggle;
