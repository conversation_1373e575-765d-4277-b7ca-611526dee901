import { Link } from "react-router-dom";
import { useTranslation } from "react-i18next";
import appConfig from "@/config/appConfig";

interface LogoComponentProps {
  logoUrl?: string;
  to?: string;
  className?: string;
  width?: string;
  height?: string;
}

const LogoComponent = ({
  logoUrl,
  to = "/dashboard",
  className,
  width = "auto",
  height = "32px"
}: LogoComponentProps) => {
  const { t } = useTranslation();
  // Use the provided logoUrl or fall back to the one in appConfig
  const logoSrc = logoUrl || appConfig.logo.url;

  // If a logo URL is provided, display the image
  if (logoSrc) {
    return (
      <Link to={to} className={`flex items-center ${className || ''}`}>
        <img
          src={logoSrc}
          alt={t('app.name')}
          style={{ width, height }}
          className="transition-transform duration-200 hover:scale-105"
        />
      </Link>
    );
  }

  // Otherwise, display the text version
  return (
    <Link to={to} className={`font-bold text-xl text-white hover:text-white/90 ${className || ''}`}>
      {t('app.name')}
    </Link>
  );
};

export default LogoComponent;
