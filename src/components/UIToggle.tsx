import { <PERSON><PERSON> } from "@/components/ui/button";
import { useUI } from "@/context/UIContext";
import { <PERSON><PERSON>, ArrowLeft } from "lucide-react";
import { useLocation, useNavigate } from "react-router-dom";

const UIToggle = () => {
  const { uiMode, toggleUIMode } = useUI();
  const location = useLocation();
  const navigate = useNavigate();

  const handleToggle = () => {
    toggleUIMode();

    // Redirect to the corresponding route in the other UI mode
    const currentPath = location.pathname;

    if (uiMode === 'current') {
      // Switching to experimental - add /experimental prefix
      if (currentPath.startsWith('/experimental')) {
        return; // Already on experimental route
      }

      // Map current routes to experimental routes
      const experimentalPath = currentPath.replace(/^\//, '/experimental/');
      navigate(experimentalPath);
    } else {
      // Switching to current - remove /experimental prefix
      if (!currentPath.startsWith('/experimental')) {
        return; // Already on current route
      }

      // Map experimental routes to current routes
      const currentRoutePath = currentPath.replace('/experimental/', '/');
      navigate(currentRoutePath);
    }
  };

  return (
    <Button
      onClick={handleToggle}
      variant="outline"
      size="sm"
      className="fixed bottom-6 right-6 z-50 shadow-sm"
    >
      {uiMode === 'current' ? (
        <>
          <Beaker className="h-4 w-4 mr-2" />
          Experimental Mode
        </>
      ) : (
        <>
          <ArrowLeft className="h-4 w-4 mr-2" />
          Standard Mode
        </>
      )}
    </Button>
  );
};

export default UIToggle;
