import ThemeToggle from "@/components/ThemeToggle";
import LanguageToggle from "@/components/LanguageToggle";
import { useTheme } from "@/context/ThemeContext";

const AuthThemeToggle = () => {
  return (
    <div className="absolute top-4 right-4 flex gap-2 z-20">
      <ThemeToggle variant="ghost" className="bg-transparent text-foreground dark:text-white hover:bg-black/10 dark:hover:bg-white/10" />
      <LanguageToggle variant="ghost" className="bg-transparent text-foreground dark:text-white hover:bg-black/10 dark:hover:bg-white/10" />
    </div>
  );
};

export default AuthThemeToggle;
