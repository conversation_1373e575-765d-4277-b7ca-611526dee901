import { useMemo } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Search } from "lucide-react";
import { ChartContainer } from "@/components/ui/chart";
import {
  Cell,
  Tooltip,
  ResponsiveContainer,
  <PERSON>atter,
  Scatter<PERSON>hart,
  XAxis,
  YAxis,
  ZAxis,
} from "recharts";

interface ChemistryPair {
  player1_id: number;
  player2_id: number;
  games_together: number;
  wins_together: number;
}

interface Player {
  id: number;
  name: string;
}

interface ChemistryHeatMapProps {
  chemistryData: ChemistryPair[];
  players: Player[];
  searchTerm?: string;
  onSearchChange?: (value: string) => void;
  maxCellSize?: number;
}

export function ChemistryHeatMap({
  chemistryData,
  players,
  searchTerm = "",
  onSearchChange,
  maxCellSize = 1000
}: ChemistryHeatMapProps) {
  // Filter players based on search term
  const filteredPlayers = useMemo(() => {
    if (!searchTerm) return players;
    const lowerSearch = searchTerm.toLowerCase();
    return players.filter(player => 
      player.name.toLowerCase().includes(lowerSearch)
    );
  }, [players, searchTerm]);

  // Prepare data for the heatmap
  const heatmapData = useMemo(() => {
    const data: { x: number; y: number; z: number; player1: Player; player2: Player; winRate: number; gamesPlayed: number }[] = [];
    
    // Only include chemistry pairs where both players are in the filtered list
    const filteredPlayerIds = new Set(filteredPlayers.map(p => p.id));
    
    chemistryData.forEach(pair => {
      if (filteredPlayerIds.has(pair.player1_id) && filteredPlayerIds.has(pair.player2_id)) {
        const player1 = players.find(p => p.id === pair.player1_id);
        const player2 = players.find(p => p.id === pair.player2_id);
        
        if (player1 && player2) {
          // Calculate win rate
          const winRate = pair.games_together > 0 
            ? Math.round((pair.wins_together / pair.games_together) * 100) 
            : 0;
          
          // Add to data array
          data.push({
            x: filteredPlayers.findIndex(p => p.id === player1.id),
            y: filteredPlayers.findIndex(p => p.id === player2.id),
            z: pair.games_together,
            player1,
            player2,
            winRate,
            gamesPlayed: pair.games_together
          });
        }
      }
    });
    
    return data;
  }, [chemistryData, filteredPlayers, players]);

  // Calculate the maximum number of games played together for scaling
  const maxGames = useMemo(() => {
    if (heatmapData.length === 0) return 1;
    return Math.max(...heatmapData.map(d => d.z));
  }, [heatmapData]);

  // Function to determine cell color based on win rate
  const getCellColor = (winRate: number) => {
    if (winRate >= 80) return "#22c55e"; // Green for high win rate
    if (winRate >= 60) return "#84cc16"; // Light green
    if (winRate >= 50) return "#eab308"; // Yellow
    if (winRate >= 30) return "#f97316"; // Orange
    return "#ef4444"; // Red for low win rate
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>Chemistry Heat Map</CardTitle>
        <CardDescription>
          Visualize player chemistry and win rates
        </CardDescription>
        {onSearchChange && (
          <div className="relative mt-2">
            <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="Search players..."
              value={searchTerm}
              onChange={(e) => onSearchChange(e.target.value)}
              className="pl-8"
            />
          </div>
        )}
      </CardHeader>
      <CardContent>
        <div className="h-[500px] w-full">
          {filteredPlayers.length > 0 ? (
            <ChartContainer
              config={{
                chemistry: {
                  label: "Chemistry",
                  theme: {
                    light: "#35db71",
                    dark: "#35db71",
                  },
                },
              }}
            >
              <ScatterChart
                margin={{ top: 40, right: 40, bottom: 20, left: 40 }}
              >
                <XAxis
                  type="number"
                  dataKey="x"
                  name="Player 1"
                  tick={false}
                  axisLine={false}
                  domain={[-1, filteredPlayers.length]}
                />
                <YAxis
                  type="number"
                  dataKey="y"
                  name="Player 2"
                  tick={false}
                  axisLine={false}
                  domain={[-1, filteredPlayers.length]}
                />
                <ZAxis
                  type="number"
                  dataKey="z"
                  range={[200, maxCellSize]}
                  domain={[0, maxGames]}
                />
                <Tooltip
                  cursor={{ strokeDasharray: '3 3' }}
                  content={({ active, payload }) => {
                    if (active && payload && payload.length) {
                      const data = payload[0].payload;
                      return (
                        <div className="bg-background border rounded-md shadow-md p-3">
                          <p className="font-medium">{data.player1.name} + {data.player2.name}</p>
                          <p>Games together: {data.gamesPlayed}</p>
                          <p>Win rate: {data.winRate}%</p>
                        </div>
                      );
                    }
                    return null;
                  }}
                />
                <Scatter data={heatmapData} shape="square">
                  {heatmapData.map((entry, index) => (
                    <Cell
                      key={`cell-${index}`}
                      fill={getCellColor(entry.winRate)}
                    />
                  ))}
                </Scatter>
                
                {/* Player names on X axis */}
                {filteredPlayers.map((player, index) => (
                  <text
                    key={`x-${player.id}`}
                    x={index * (100 / filteredPlayers.length) + (50 / filteredPlayers.length)}
                    y={20}
                    textAnchor="middle"
                    dominantBaseline="middle"
                    className="text-xs font-medium fill-muted-foreground"
                    transform={`rotate(-45, ${index * (100 / filteredPlayers.length) + (50 / filteredPlayers.length)}, 20)`}
                  >
                    {player.name}
                  </text>
                ))}
                
                {/* Player names on Y axis */}
                {filteredPlayers.map((player, index) => (
                  <text
                    key={`y-${player.id}`}
                    x={10}
                    y={index * (100 / filteredPlayers.length) + (50 / filteredPlayers.length)}
                    textAnchor="end"
                    dominantBaseline="middle"
                    className="text-xs font-medium fill-muted-foreground"
                  >
                    {player.name}
                  </text>
                ))}
              </ScatterChart>
            </ChartContainer>
          ) : (
            <div className="h-full flex items-center justify-center text-muted-foreground">
              No players match your search criteria
            </div>
          )}
        </div>
        
        {/* Legend */}
        <div className="mt-4 flex items-center justify-center gap-4">
          <div className="flex items-center gap-1">
            <div className="w-3 h-3 rounded-sm bg-red-500"></div>
            <span className="text-xs">Low Win Rate</span>
          </div>
          <div className="flex items-center gap-1">
            <div className="w-3 h-3 rounded-sm bg-orange-500"></div>
            <span className="text-xs">Below Average</span>
          </div>
          <div className="flex items-center gap-1">
            <div className="w-3 h-3 rounded-sm bg-yellow-500"></div>
            <span className="text-xs">Average</span>
          </div>
          <div className="flex items-center gap-1">
            <div className="w-3 h-3 rounded-sm bg-lime-500"></div>
            <span className="text-xs">Good</span>
          </div>
          <div className="flex items-center gap-1">
            <div className="w-3 h-3 rounded-sm bg-green-500"></div>
            <span className="text-xs">Excellent</span>
          </div>
        </div>
        
        <div className="mt-2 text-center text-xs text-muted-foreground">
          Larger squares indicate more games played together
        </div>
      </CardContent>
    </Card>
  );
}
