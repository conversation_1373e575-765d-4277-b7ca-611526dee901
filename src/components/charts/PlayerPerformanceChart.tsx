import { useMemo, useRef, useEffect, useState, useCallback } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Tabs, TabsContent, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { ChartContainer } from "@/components/ui/chart";
import { format, parseISO, subMonths } from "date-fns";
import {
  Area,
  AreaChart,
  Bar,
  BarChart,
  CartesianGrid,
  Legend,
  Line,
  LineChart,
  ResponsiveContainer,
  Tooltip,
  XAxis,
  YAxis,
} from "recharts";
import { useMediaQuery } from "@/hooks/use-media-query";

interface Match {
  id: number;
  match_date: string | Date;
  teama: number[];
  teamb: number[];
  scorea: number | null;
  scoreb: number | null;
  winner?: 'A' | 'B' | 'Draw';
}

interface Player {
  id: number;
  name: string;
  skills: number;
  effort: number;
  stamina: number;
  rating?: number;
}

interface PlayerPerformanceChartProps {
  player: Player;
  matches: Match[];
  calculatePlayerRating?: (player: Player) => number;
}

export function PlayerPerformanceChart({
  player,
  matches,
  calculatePlayerRating = (p) => p.rating || Math.round((p.skills + p.effort + p.stamina) / 3)
}: PlayerPerformanceChartProps) {
  // For accessibility - track current focused data point
  const [focusedIndex, setFocusedIndex] = useState<number | null>(null);
  const chartRef = useRef<HTMLDivElement>(null);
  const prefersDarkMode = useMediaQuery("(prefers-color-scheme: dark)");
  const preferReducedMotion = useMediaQuery("(prefers-reduced-motion: reduce)");

  // For keyboard navigation
  const handleKeyDown = useCallback((e: React.KeyboardEvent, data: any[], setIndex: (index: number | null) => void) => {
    if (!data.length) return;

    switch (e.key) {
      case 'ArrowRight':
        setIndex(prev => (prev === null ? 0 : Math.min(prev + 1, data.length - 1)));
        e.preventDefault();
        break;
      case 'ArrowLeft':
        setIndex(prev => (prev === null ? data.length - 1 : Math.max(prev - 1, 0)));
        e.preventDefault();
        break;
      case 'Home':
        setIndex(0);
        e.preventDefault();
        break;
      case 'End':
        setIndex(data.length - 1);
        e.preventDefault();
        break;
      case 'Escape':
        setIndex(null);
        e.preventDefault();
        break;
    }
  }, []);
  // Filter matches where this player participated
  const playerMatches = useMemo(() => {
    return matches
      .filter(match =>
        match.teama.includes(player.id) || match.teamb.includes(player.id)
      )
      .sort((a, b) => {
        const dateA = a.match_date instanceof Date ? a.match_date : new Date(a.match_date);
        const dateB = b.match_date instanceof Date ? b.match_date : new Date(b.match_date);
        return dateA.getTime() - dateB.getTime();
      });
  }, [matches, player.id]);

  // Calculate win rate over time
  const winRateData = useMemo(() => {
    if (playerMatches.length === 0) return [];

    let wins = 0;
    let totalGames = 0;

    return playerMatches.map(match => {
      totalGames++;
      const isTeamA = match.teama.includes(player.id);
      const isWin = (isTeamA && match.winner === 'A') || (!isTeamA && match.winner === 'B');

      if (isWin) wins++;

      const winRate = Math.round((wins / totalGames) * 100);
      const matchDate = match.match_date instanceof Date
        ? match.match_date
        : new Date(match.match_date);

      return {
        date: format(matchDate, 'MMM d'),
        winRate,
        game: totalGames,
      };
    });
  }, [playerMatches, player.id]);

  // Calculate form (last 5 matches)
  const formData = useMemo(() => {
    const recentMatches = [...playerMatches].reverse().slice(0, 5);

    return recentMatches.map(match => {
      const isTeamA = match.teama.includes(player.id);
      const isWin = (isTeamA && match.winner === 'A') || (!isTeamA && match.winner === 'B');
      const isDraw = match.winner === 'Draw';
      const matchDate = match.match_date instanceof Date
        ? match.match_date
        : new Date(match.match_date);

      return {
        date: format(matchDate, 'MMM d'),
        result: isWin ? 3 : isDraw ? 1 : 0,
        resultLabel: isWin ? 'Win' : isDraw ? 'Draw' : 'Loss',
      };
    }).reverse();
  }, [playerMatches, player.id]);

  // Calculate monthly performance
  const monthlyPerformance = useMemo(() => {
    const now = new Date();
    const sixMonthsAgo = subMonths(now, 6);

    // Initialize monthly data
    const monthlyData: Record<string, { month: string, wins: number, draws: number, losses: number, games: number }> = {};

    for (let i = 0; i <= 6; i++) {
      const monthDate = subMonths(now, i);
      const monthKey = format(monthDate, 'MMM yyyy');
      monthlyData[monthKey] = { month: monthKey, wins: 0, draws: 0, losses: 0, games: 0 };
    }

    // Fill in actual data
    playerMatches.forEach(match => {
      const matchDate = match.match_date instanceof Date
        ? match.match_date
        : new Date(match.match_date);

      if (matchDate >= sixMonthsAgo) {
        const monthKey = format(matchDate, 'MMM yyyy');
        if (!monthlyData[monthKey]) {
          monthlyData[monthKey] = { month: monthKey, wins: 0, draws: 0, losses: 0, games: 0 };
        }

        const isTeamA = match.teama.includes(player.id);
        const isWin = (isTeamA && match.winner === 'A') || (!isTeamA && match.winner === 'B');
        const isDraw = match.winner === 'Draw';

        monthlyData[monthKey].games += 1;

        if (isWin) monthlyData[monthKey].wins += 1;
        else if (isDraw) monthlyData[monthKey].draws += 1;
        else monthlyData[monthKey].losses += 1;
      }
    });

    // Convert to array and sort by date
    return Object.values(monthlyData)
      .filter(item => item.games > 0)
      .sort((a, b) => {
        const dateA = new Date(a.month);
        const dateB = new Date(b.month);
        return dateA.getTime() - dateB.getTime();
      });
  }, [playerMatches, player.id]);

  // Announce chart data to screen readers
  const getChartSummary = (data: any[], type: string) => {
    if (!data.length) return "No data available";

    if (type === "winRate") {
      const currentWinRate = data[data.length - 1].winRate;
      return `Win rate chart showing progression over ${data.length} games. Current win rate is ${currentWinRate}%.`;
    } else if (type === "form") {
      const recentResults = data.map(d => d.resultLabel).join(", ");
      return `Recent form chart showing last ${data.length} matches. Results: ${recentResults}.`;
    } else if (type === "monthly") {
      const totalGames = data.reduce((sum, month) => sum + month.games, 0);
      const totalWins = data.reduce((sum, month) => sum + month.wins, 0);
      const winRate = totalGames > 0 ? Math.round((totalWins / totalGames) * 100) : 0;
      return `Monthly performance chart showing data for ${data.length} months. Total games: ${totalGames}, win rate: ${winRate}%.`;
    }
    return "";
  };

  if (playerMatches.length === 0) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Performance Trends</CardTitle>
          <CardDescription>No match data available for this player</CardDescription>
        </CardHeader>
        <CardContent className="h-[300px] flex items-center justify-center text-muted-foreground">
          <div role="alert" aria-live="polite">
            This player hasn't participated in any matches yet.
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>Performance Trends</CardTitle>
        <CardDescription>
          Stats from {playerMatches.length} matches
        </CardDescription>
      </CardHeader>
      <CardContent>
        <Tabs defaultValue="winrate">
          <TabsList className="mb-4" aria-label="Chart type selection">
            <TabsTrigger value="winrate">Win Rate</TabsTrigger>
            <TabsTrigger value="form">Recent Form</TabsTrigger>
            <TabsTrigger value="monthly">Monthly Performance</TabsTrigger>
          </TabsList>

          <TabsContent value="winrate" className="h-[300px]">
            <div
              ref={chartRef}
              tabIndex={0}
              role="figure"
              aria-label="Win rate over time"
              aria-description={getChartSummary(winRateData, "winRate")}
              className="h-full focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2 rounded-md"
              onKeyDown={(e) => handleKeyDown(e, winRateData, setFocusedIndex)}
            >
              <ChartContainer
                config={{
                  winRate: {
                    label: "Win Rate",
                    theme: {
                      light: "#35db71",
                      dark: "#35db71",
                    },
                  },
                }}
              >
                <AreaChart
                  data={winRateData}
                  margin={{ top: 5, right: 20, left: 0, bottom: 5 }}
                  aria-hidden="true"
                >
                  <defs>
                    <linearGradient id="winRateGradient" x1="0" y1="0" x2="0" y2="1">
                      <stop offset="5%" stopColor="#35db71" stopOpacity={0.8} />
                      <stop offset="95%" stopColor="#35db71" stopOpacity={0} />
                    </linearGradient>
                  </defs>
                  <CartesianGrid strokeDasharray="3 3" opacity={0.2} />
                  <XAxis
                    dataKey="date"
                    tick={{ fontSize: 12 }}
                    tickMargin={10}
                    label={{ value: "Match Date", position: "insideBottom", offset: -10, fontSize: 12 }}
                  />
                  <YAxis
                    domain={[0, 100]}
                    tick={{ fontSize: 12 }}
                    tickMargin={10}
                    tickFormatter={(value) => `${value}%`}
                    label={{ value: "Win Rate (%)", angle: -90, position: "insideLeft", style: { textAnchor: "middle" }, fontSize: 12 }}
                  />
                  <Tooltip
                    formatter={(value) => [`${value}%`, 'Win Rate']}
                    labelFormatter={(label, payload) => {
                      if (payload && payload.length > 0) {
                        return `Game ${payload[0].payload.game}: ${label}`;
                      }
                      return label;
                    }}
                    isAnimationActive={!preferReducedMotion}
                  />
                  <Area
                    type="monotone"
                    dataKey="winRate"
                    stroke="#35db71"
                    fillOpacity={1}
                    fill="url(#winRateGradient)"
                    isAnimationActive={!preferReducedMotion}
                  />
                </AreaChart>
              </ChartContainer>
              {/* Screen reader only data table */}
              <div className="sr-only">
                <table>
                  <caption>Win Rate Data</caption>
                  <thead>
                    <tr>
                      <th scope="col">Game</th>
                      <th scope="col">Date</th>
                      <th scope="col">Win Rate</th>
                    </tr>
                  </thead>
                  <tbody>
                    {winRateData.map((item, index) => (
                      <tr key={index}>
                        <td>{item.game}</td>
                        <td>{item.date}</td>
                        <td>{item.winRate}%</td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </div>
          </TabsContent>

          <TabsContent value="form" className="h-[300px]">
            <div
              tabIndex={0}
              role="figure"
              aria-label="Recent form results"
              aria-description={getChartSummary(formData, "form")}
              className="h-full focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2 rounded-md"
              onKeyDown={(e) => handleKeyDown(e, formData, setFocusedIndex)}
            >
              <ChartContainer
                config={{
                  result: {
                    label: "Result",
                    theme: {
                      light: "#35db71",
                      dark: "#35db71",
                    },
                  },
                }}
              >
                <BarChart
                  data={formData}
                  margin={{ top: 5, right: 20, left: 0, bottom: 5 }}
                  aria-hidden="true"
                >
                  <CartesianGrid strokeDasharray="3 3" opacity={0.2} />
                  <XAxis
                    dataKey="date"
                    tick={{ fontSize: 12 }}
                    tickMargin={10}
                    label={{ value: "Match Date", position: "insideBottom", offset: -10, fontSize: 12 }}
                  />
                  <YAxis
                    domain={[0, 3]}
                    ticks={[0, 1, 3]}
                    tickFormatter={(value) => {
                      if (value === 0) return 'Loss';
                      if (value === 1) return 'Draw';
                      if (value === 3) return 'Win';
                      return '';
                    }}
                    tick={{ fontSize: 12 }}
                    tickMargin={10}
                    label={{ value: "Result", angle: -90, position: "insideLeft", style: { textAnchor: "middle" }, fontSize: 12 }}
                  />
                  <Tooltip
                    formatter={(value, name, props) => [props.payload.resultLabel, 'Result']}
                    isAnimationActive={!preferReducedMotion}
                  />
                  <Bar
                    dataKey="result"
                    fill="#35db71"
                    radius={[4, 4, 0, 0]}
                    isAnimationActive={!preferReducedMotion}
                  />
                </BarChart>
              </ChartContainer>
              {/* Screen reader only data table */}
              <div className="sr-only">
                <table>
                  <caption>Recent Form Data</caption>
                  <thead>
                    <tr>
                      <th scope="col">Date</th>
                      <th scope="col">Result</th>
                    </tr>
                  </thead>
                  <tbody>
                    {formData.map((item, index) => (
                      <tr key={index}>
                        <td>{item.date}</td>
                        <td>{item.resultLabel}</td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </div>
          </TabsContent>

          <TabsContent value="monthly" className="h-[300px]">
            <div
              tabIndex={0}
              role="figure"
              aria-label="Monthly performance breakdown"
              aria-description={getChartSummary(monthlyPerformance, "monthly")}
              className="h-full focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2 rounded-md"
              onKeyDown={(e) => handleKeyDown(e, monthlyPerformance, setFocusedIndex)}
            >
              <ChartContainer
                config={{
                  wins: {
                    label: "Wins",
                    theme: {
                      light: "#35db71",
                      dark: "#35db71",
                    },
                  },
                  draws: {
                    label: "Draws",
                    theme: {
                      light: "#f59e0b",
                      dark: "#f59e0b",
                    },
                  },
                  losses: {
                    label: "Losses",
                    theme: {
                      light: "#ef4444",
                      dark: "#ef4444",
                    },
                  },
                }}
              >
                <BarChart
                  data={monthlyPerformance}
                  margin={{ top: 5, right: 20, left: 0, bottom: 5 }}
                  aria-hidden="true"
                >
                  <CartesianGrid strokeDasharray="3 3" opacity={0.2} />
                  <XAxis
                    dataKey="month"
                    tick={{ fontSize: 12 }}
                    tickMargin={10}
                    label={{ value: "Month", position: "insideBottom", offset: -10, fontSize: 12 }}
                  />
                  <YAxis
                    tick={{ fontSize: 12 }}
                    tickMargin={10}
                    label={{ value: "Number of Games", angle: -90, position: "insideLeft", style: { textAnchor: "middle" }, fontSize: 12 }}
                  />
                  <Tooltip isAnimationActive={!preferReducedMotion} />
                  <Legend wrapperStyle={{ paddingTop: 10 }} />
                  <Bar dataKey="wins" stackId="a" fill="#35db71" radius={[4, 4, 0, 0]} isAnimationActive={!preferReducedMotion} />
                  <Bar dataKey="draws" stackId="a" fill="#f59e0b" isAnimationActive={!preferReducedMotion} />
                  <Bar dataKey="losses" stackId="a" fill="#ef4444" isAnimationActive={!preferReducedMotion} />
                </BarChart>
              </ChartContainer>
              {/* Screen reader only data table */}
              <div className="sr-only">
                <table>
                  <caption>Monthly Performance Data</caption>
                  <thead>
                    <tr>
                      <th scope="col">Month</th>
                      <th scope="col">Wins</th>
                      <th scope="col">Draws</th>
                      <th scope="col">Losses</th>
                      <th scope="col">Total Games</th>
                    </tr>
                  </thead>
                  <tbody>
                    {monthlyPerformance.map((item, index) => (
                      <tr key={index}>
                        <td>{item.month}</td>
                        <td>{item.wins}</td>
                        <td>{item.draws}</td>
                        <td>{item.losses}</td>
                        <td>{item.games}</td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </div>
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  );
}
