import { useState, useMemo } from "react";
import { useTranslation } from "react-i18next";
import { Handshake, Users, UsersRound, Search } from "lucide-react";
import { Input } from "@/components/ui/input";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Slider } from "@/components/ui/slider";
import { Badge } from "@/components/ui/badge";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { EnhancedCard } from "@/components/ui/enhanced-card";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";

interface BestChemistry {
  players: number[];
  played: number;
  wins: number;
  winRate: number;
}

interface DashboardChemistryProps {
  players: any[];
  matches: any[];
}

const DashboardChemistry = ({ players, matches }: DashboardChemistryProps) => {
  const { t } = useTranslation();
  const [search, setSearch] = useState("");
  const [selectedPlayer, setSelectedPlayer] = useState<string>("");
  const [minGames, setMinGames] = useState(3);
  const [viewMode, setViewMode] = useState<"duos" | "trios" | "quads">("duos");
  const [currentPage, setCurrentPage] = useState(1);
  const itemsPerPage = 5;

  // Calculate maximum games played for slider
  const maxGamesPlayed = useMemo(() => {
    if (matches.length === 0) return 10;
    const playerGameCounts = new Map<number, number>();

    matches.forEach(match => {
      [...match.teamA, ...match.teamB].forEach(playerId => {
        playerGameCounts.set(playerId, (playerGameCounts.get(playerId) || 0) + 1);
      });
    });

    return Math.max(...Array.from(playerGameCounts.values()), 10);
  }, [matches]);

  // Function to get player name by ID
  const getPlayerName = (id: number): string => {
    const player = players.find((p) => p.id === id);
    return player ? player.name : `Player ${id}`;
  };

  // Function to get combinations of players
  function getCombinations<T>(array: T[], k: number): T[][] {
    const result: T[][] = [];
    function combine(start: number, currentCombination: T[]) {
      if (currentCombination.length === k) {
        result.push([...currentCombination]);
        return;
      }
      for (let i = start; i < array.length; i++) {
        currentCombination.push(array[i]);
        combine(i + 1, currentCombination);
        currentCombination.pop();
      }
    }
    combine(0, []);
    return result;
  }

  // Calculate chemistry data
  const { duos, trios, quads } = useMemo(() => {
    const duosMap = new Map<string, { players: number[]; played: number; wins: number }>();
    const triosMap = new Map<string, { players: number[]; played: number; wins: number }>();
    const quadsMap = new Map<string, { players: number[]; played: number; wins: number }>();

    matches.forEach(match => {
      const teamA = match.teamA || [];
      const teamB = match.teamB || [];

      // Process team A combinations
      if (teamA.length >= 2) {
        const duoCombos = getCombinations(teamA, 2);
        duoCombos.forEach(combo => {
          const key = combo.sort((a, b) => a - b).join('-');
          const existing = duosMap.get(key) || { players: combo, played: 0, wins: 0 };
          existing.played++;
          if (match.winner === 'A') existing.wins++;
          duosMap.set(key, existing);
        });
      }

      if (teamA.length >= 3) {
        const trioCombos = getCombinations(teamA, 3);
        trioCombos.forEach(combo => {
          const key = combo.sort((a, b) => a - b).join('-');
          const existing = triosMap.get(key) || { players: combo, played: 0, wins: 0 };
          existing.played++;
          if (match.winner === 'A') existing.wins++;
          triosMap.set(key, existing);
        });
      }

      if (teamA.length >= 4) {
        const quadCombos = getCombinations(teamA, 4);
        quadCombos.forEach(combo => {
          const key = combo.sort((a, b) => a - b).join('-');
          const existing = quadsMap.get(key) || { players: combo, played: 0, wins: 0 };
          existing.played++;
          if (match.winner === 'A') existing.wins++;
          quadsMap.set(key, existing);
        });
      }

      // Process team B combinations
      if (teamB.length >= 2) {
        const duoCombos = getCombinations(teamB, 2);
        duoCombos.forEach(combo => {
          const key = combo.sort((a, b) => a - b).join('-');
          const existing = duosMap.get(key) || { players: combo, played: 0, wins: 0 };
          existing.played++;
          if (match.winner === 'B') existing.wins++;
          duosMap.set(key, existing);
        });
      }

      if (teamB.length >= 3) {
        const trioCombos = getCombinations(teamB, 3);
        trioCombos.forEach(combo => {
          const key = combo.sort((a, b) => a - b).join('-');
          const existing = triosMap.get(key) || { players: combo, played: 0, wins: 0 };
          existing.played++;
          if (match.winner === 'B') existing.wins++;
          triosMap.set(key, existing);
        });
      }

      if (teamB.length >= 4) {
        const quadCombos = getCombinations(teamB, 4);
        quadCombos.forEach(combo => {
          const key = combo.sort((a, b) => a - b).join('-');
          const existing = quadsMap.get(key) || { players: combo, played: 0, wins: 0 };
          existing.played++;
          if (match.winner === 'B') existing.wins++;
          quadsMap.set(key, existing);
        });
      }
    });

    // Convert to arrays and calculate win rates
    const processChemistryData = (statsMap: Map<string, any>): BestChemistry[] => {
      return Array.from(statsMap.values())
        .map(stat => ({
          ...stat,
          winRate: (stat.played > 0 ? (stat.wins / stat.played) * 100 : 0)
        }))
        .sort((a, b) => b.winRate - a.winRate);
    };

    return {
      duos: processChemistryData(duosMap),
      trios: processChemistryData(triosMap),
      quads: processChemistryData(quadsMap)
    };
  }, [matches]);

  // Filter chemistry data based on search and selected player
  const filteredDuos = useMemo(() => {
    return duos.filter(duo => {
      const playerNames = duo.players.map(id => getPlayerName(id)).join(' ');
      const matchesSearch = playerNames.toLowerCase().includes(search.toLowerCase());
      const matchesPlayer = selectedPlayer ? duo.players.includes(parseInt(selectedPlayer)) : true;
      const matchesMinGames = duo.played >= minGames;
      return matchesSearch && matchesPlayer && matchesMinGames;
    });
  }, [duos, search, selectedPlayer, minGames, getPlayerName]);

  const filteredTrios = useMemo(() => {
    return trios.filter(trio => {
      const playerNames = trio.players.map(id => getPlayerName(id)).join(' ');
      const matchesSearch = playerNames.toLowerCase().includes(search.toLowerCase());
      const matchesPlayer = selectedPlayer ? trio.players.includes(parseInt(selectedPlayer)) : true;
      const matchesMinGames = trio.played >= minGames;
      return matchesSearch && matchesPlayer && matchesMinGames;
    });
  }, [trios, search, selectedPlayer, minGames, getPlayerName]);

  const filteredQuads = useMemo(() => {
    return quads.filter(quad => {
      const playerNames = quad.players.map(id => getPlayerName(id)).join(' ');
      const matchesSearch = playerNames.toLowerCase().includes(search.toLowerCase());
      const matchesPlayer = selectedPlayer ? quad.players.includes(parseInt(selectedPlayer)) : true;
      const matchesMinGames = quad.played >= minGames;
      return matchesSearch && matchesPlayer && matchesMinGames;
    });
  }, [quads, search, selectedPlayer, minGames, getPlayerName]);

  // Get paginated data based on current view mode
  const paginatedData = useMemo(() => {
    let sourceData: BestChemistry[] = [];

    if (viewMode === "duos") sourceData = filteredDuos;
    else if (viewMode === "trios") sourceData = filteredTrios;
    else if (viewMode === "quads") sourceData = filteredQuads;

    const totalItems = sourceData.length;
    const totalPages = Math.max(1, Math.ceil(totalItems / itemsPerPage));
    const validCurrentPage = Math.min(Math.max(1, currentPage), totalPages);
    const start = (validCurrentPage - 1) * itemsPerPage;
    const end = start + itemsPerPage;
    const paginated = sourceData.slice(start, end);

    return {
      data: paginated,
      totalPages,
      totalItems
    };
  }, [filteredDuos, filteredTrios, filteredQuads, viewMode, currentPage, itemsPerPage]);

  // Render chemistry table
  const renderChemistryTable = () => {
    const { data: dataToRender } = paginatedData;

    // Define table headers based on view mode
    let headerData = [];
    if (viewMode === "duos") {
      headerData = [
        { key: "player1", label: t('chemistry.player1'), className: "" },
        { key: "player2", label: t('chemistry.player2'), className: "" },
        { key: "played", label: t('chemistry.played'), className: "text-center" },
        { key: "wins", label: t('chemistry.wins'), className: "text-center" },
        { key: "winRate", label: t('chemistry.winRate'), className: "text-center" }
      ];
    } else if (viewMode === "trios") {
      headerData = [
        { key: "players", label: t('chemistry.players'), className: "" },
        { key: "played", label: t('chemistry.played'), className: "text-center" },
        { key: "wins", label: t('chemistry.wins'), className: "text-center" },
        { key: "winRate", label: t('chemistry.winRate'), className: "text-center" }
      ];
    } else {
      headerData = [
        { key: "players", label: t('chemistry.players'), className: "" },
        { key: "played", label: t('chemistry.played'), className: "text-center" },
        { key: "wins", label: t('chemistry.wins'), className: "text-center" },
        { key: "winRate", label: t('chemistry.winRate'), className: "text-center" }
      ];
    }

    // Render table row based on view mode
    const renderRow = (item: BestChemistry) => {
      if (viewMode === "duos") {
        return (
          <TableRow key={item.players.join('-')}>
            <TableCell>{getPlayerName(item.players[0])}</TableCell>
            <TableCell>{getPlayerName(item.players[1])}</TableCell>
            <TableCell className="text-center">{item.played}</TableCell>
            <TableCell className="text-center">{item.wins}</TableCell>
            <TableCell className="text-center">
              <Badge variant={item.winRate > 50 ? "default" : "outline"} className={item.winRate > 50 ? "bg-green-500" : ""}>
                {item.winRate.toFixed(1)}%
              </Badge>
            </TableCell>
          </TableRow>
        );
      } else {
        return (
          <TableRow key={item.players.join('-')}>
            <TableCell>{item.players.map(id => getPlayerName(id)).join(', ')}</TableCell>
            <TableCell className="text-center">{item.played}</TableCell>
            <TableCell className="text-center">{item.wins}</TableCell>
            <TableCell className="text-center">
              <Badge variant={item.winRate > 50 ? "default" : "outline"} className={item.winRate > 50 ? "bg-green-500" : ""}>
                {item.winRate.toFixed(1)}%
              </Badge>
            </TableCell>
          </TableRow>
        );
      }
    };

    return (
      <div className="rounded-md border mt-4">
        <Table>
          <TableHeader>
            <TableRow>
              {headerData.map(header => (
                <TableHead key={header.key} className={header.className}>
                  {header.label}
                </TableHead>
              ))}
            </TableRow>
          </TableHeader>
          <TableBody>
            {!dataToRender || dataToRender.length === 0 ? (
              <TableRow>
                <TableCell colSpan={headerData.length} className="h-24 text-center">
                  {t('chemistry.noChemistryData', 'No chemistry data available')} ({viewMode})
                </TableCell>
              </TableRow>
            ) : (
              dataToRender.map((item) => renderRow(item))
            )}
          </TableBody>
        </Table>
      </div>
    );
  };

  return (
    <div className="space-y-6">
      {/* Filters Card */}
      <EnhancedCard>
        <div className="p-6">
          <div className="flex items-center gap-3 mb-4">
            <Handshake className="h-5 w-5 text-soccer-primary" />
            <div className="font-semibold text-lg text-foreground">{t('chemistry.title', 'Player Chemistry')}</div>
          </div>
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="relative flex-grow">
              <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder={t('chemistry.searchPlayers')}
                value={search}
                onChange={(e) => setSearch(e.target.value)}
                className="pl-8"
              />
            </div>
            <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center">
              <Select
                value={selectedPlayer}
                onValueChange={setSelectedPlayer}
              >
                <SelectTrigger className="w-[150px]">
                  <SelectValue placeholder={t('chemistry.filterByPlayer')} />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="">{t('chemistry.allPlayers')}</SelectItem>
                  {players.map((player) => (
                    <SelectItem key={player.id} value={player.id.toString()}>
                      {player.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              <div className="flex items-center gap-6 w-full max-w-2xl">
                <div className="text-sm text-muted-foreground whitespace-nowrap min-w-fit">
                  {t('chemistry.minGames', 'Min. Games')}: <span className="font-medium text-foreground">{minGames}</span>
                </div>
                <div className="flex-1 px-4 min-w-[200px] sm:min-w-[300px]">
                  <Slider
                    value={[minGames]}
                    onValueChange={(value) => setMinGames(value[0])}
                    max={maxGamesPlayed}
                    min={0}
                    step={1}
                    className="w-full h-8"
                    aria-label={`Minimum games filter, current value: ${minGames}, maximum: ${maxGamesPlayed}`}
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
      </EnhancedCard>

      {/* Chemistry Analysis Card */}
      <EnhancedCard>
        <div className="p-6">
          <div className="flex items-center justify-between mb-4">
            <div className="flex items-center gap-3">
              <Users className="h-5 w-5 text-soccer-primary" />
              <div className="font-semibold text-lg text-foreground">{t('chemistry.analysisTitle', 'Chemistry Analysis')}</div>
            </div>
            <div className="text-sm text-muted-foreground">
              {t('chemistry.analysisDescription', 'Team performance combinations')}
            </div>
          </div>

        {/* Tabs for View Mode */}
        <Tabs value={viewMode} onValueChange={(value) => setViewMode(value as any)} className="w-full">
          <TabsList className="grid w-full grid-cols-3 md:w-[400px]">
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <TabsTrigger value="duos">
                    <Handshake className="h-4 w-4 md:mr-1" />
                    <span className="hidden md:inline">{t('chemistry.duos', 'Duos')}</span>
                  </TabsTrigger>
                </TooltipTrigger>
                <TooltipContent className="md:hidden">
                  {t('chemistry.duos', 'Duos')}
                </TooltipContent>
              </Tooltip>

              <Tooltip>
                <TooltipTrigger asChild>
                  <TabsTrigger value="trios">
                    <Users className="h-4 w-4 md:mr-1" />
                    <span className="hidden md:inline">{t('chemistry.trios', 'Trios')}</span>
                  </TabsTrigger>
                </TooltipTrigger>
                <TooltipContent className="md:hidden">
                  {t('chemistry.trios', 'Trios')}
                </TooltipContent>
              </Tooltip>

              <Tooltip>
                <TooltipTrigger asChild>
                  <TabsTrigger value="quads">
                    <UsersRound className="h-4 w-4 md:mr-1" />
                    <span className="hidden md:inline">{t('chemistry.quads', 'Quads')}</span>
                  </TabsTrigger>
                </TooltipTrigger>
                <TooltipContent className="md:hidden">
                  {t('chemistry.quads', 'Quads')}
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
          </TabsList>
          {renderChemistryTable()}
        </Tabs>
        </div>
      </EnhancedCard>
    </div>
  );
};

export default DashboardChemistry;
