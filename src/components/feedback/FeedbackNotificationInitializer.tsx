import { useEffect } from 'react';
import { useFeedbackNotification } from '@/lib/services/feedbackService';

/**
 * Component that initializes the feedback notification system.
 * This component doesn't render anything visible but sets up the
 * periodic feedback notification system.
 */
const FeedbackNotificationInitializer = () => {
  // Get the feedback notification hook
  const { showFeedbackNotification } = useFeedbackNotification();

  // For testing purposes, show a notification when the component mounts
  useEffect(() => {
    // Uncomment the line below to test the notification
    // showFeedbackNotification();
  }, []);

  return null; // This component doesn't render anything
};

export default FeedbackNotificationInitializer;
