import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Download, Loader2 } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { fetchGroupDataForExport, generateExportFile } from '@/lib/utils/exportData';

interface ExportGroupDataProps {
  groupId: string;
  groupName: string;
  variant?: 'default' | 'outline' | 'secondary' | 'ghost' | 'link' | 'destructive';
  size?: 'default' | 'sm' | 'lg' | 'icon';
  className?: string;
  onExportComplete?: () => void;
}

const ExportGroupData: React.FC<ExportGroupDataProps> = ({
  groupId,
  groupName,
  variant = 'outline',
  size = 'default',
  className = '',
  onExportComplete
}) => {
  const [isExporting, setIsExporting] = useState(false);
  const { toast } = useToast();

  const handleExport = async () => {
    if (isExporting) return;

    setIsExporting(true);
    try {
      // Fetch the group data
      const data = await fetchGroupDataForExport(groupId);
      
      // Generate and download the export file
      generateExportFile(data, groupName);
      
      // Show success toast
      toast({
        title: 'Export Successful',
        description: `Data for "${groupName}" has been exported successfully.`,
      });
      
      // Call the onExportComplete callback if provided
      if (onExportComplete) {
        onExportComplete();
      }
    } catch (error) {
      console.error('Error exporting group data:', error);
      
      // Show error toast
      toast({
        title: 'Export Failed',
        description: error instanceof Error ? error.message : 'An unknown error occurred.',
        variant: 'destructive',
      });
    } finally {
      setIsExporting(false);
    }
  };

  return (
    <Button
      variant={variant}
      size={size}
      className={className}
      onClick={handleExport}
      disabled={isExporting}
    >
      {isExporting ? (
        <>
          <Loader2 className="mr-2 h-4 w-4 animate-spin" />
          Exporting...
        </>
      ) : (
        <>
          <Download className="mr-2 h-4 w-4" />
          Export Data
        </>
      )}
    </Button>
  );
};

export default ExportGroupData;
