import React from 'react';
import { <PERSON>, CardContent, CardDescription, Card<PERSON>ooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { ImportData, ImportPlayer, ImportMatch } from '@/lib/validators/importSchema';
import { format, parseISO } from 'date-fns';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { Check, AlertCircle } from 'lucide-react';

interface ImportPreviewProps {
  importData: ImportData;
  onConfirm: () => void;
  onCancel: () => void;
  isProcessing: boolean;
}

const ImportPreview: React.FC<ImportPreviewProps> = ({
  importData,
  onConfirm,
  onCancel,
  isProcessing
}) => {
  const { players, matches } = importData;

  // Format date for display
  const formatDate = (dateString: string) => {
    try {
      return format(parseISO(dateString), 'MMM d, yyyy');
    } catch (error) {
      return dateString;
    }
  };

  return (
    <div className="space-y-4 mt-4">
      <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center gap-2">
        <div>
          <h3 className="text-lg font-medium">Import Preview</h3>
          <p className="text-sm text-muted-foreground">
            Review the data before importing
          </p>
        </div>
        <div className="flex items-center gap-2">
          <Badge variant="outline" className="bg-green-50 text-green-700 dark:bg-green-900/20 dark:text-green-400">
            {players.length} Players
          </Badge>
          <Badge variant="outline" className="bg-blue-50 text-blue-700 dark:bg-blue-900/20 dark:text-blue-400">
            {matches.length} Matches
          </Badge>
        </div>
      </div>

      <Separator />

      {/* Players Preview */}
      <div>
        <h4 className="text-sm font-medium mb-2">Players Sample</h4>
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-2">
          {players.slice(0, 3).map((player, index) => (
            <PlayerPreviewCard key={index} player={player} />
          ))}
          {players.length > 3 && (
            <Card className="flex items-center justify-center p-2 border-dashed">
              <p className="text-xs text-muted-foreground">
                +{players.length - 3} more players
              </p>
            </Card>
          )}
        </div>
      </div>

      {/* Matches Preview */}
      <div>
        <h4 className="text-sm font-medium mb-2">Matches Sample</h4>
        <div className="grid grid-cols-1 gap-3">
          {matches.slice(0, 3).map((match, index) => (
            <MatchPreviewCard key={index} match={match} />
          ))}
          {matches.length > 3 && (
            <Card className="flex items-center justify-center p-4 border-dashed">
              <p className="text-sm text-muted-foreground">
                +{matches.length - 3} more matches
              </p>
            </Card>
          )}
        </div>
      </div>

      <div className="flex justify-end space-x-2 pt-4">
        <Button
          variant="outline"
          onClick={onCancel}
          disabled={isProcessing}
        >
          Cancel
        </Button>
        <Button
          onClick={onConfirm}
          disabled={isProcessing}
          className="bg-soccer-primary hover:bg-soccer-primary/90"
        >
          {isProcessing ? (
            <>
              <span className="mr-2">Importing...</span>
              <AlertCircle className="h-4 w-4 animate-spin" />
            </>
          ) : (
            <>
              <span className="mr-2">Confirm Import</span>
              <Check className="h-4 w-4" />
            </>
          )}
        </Button>
      </div>
    </div>
  );
};

// Helper component for player preview
const PlayerPreviewCard: React.FC<{ player: ImportPlayer }> = ({ player }) => {
  return (
    <Card className="overflow-hidden">
      <CardHeader className="p-2 pb-0">
        <CardTitle className="text-sm truncate">{player.name}</CardTitle>
      </CardHeader>
      <CardContent className="p-2 pt-1">
        <div className="grid grid-cols-3 gap-1 text-xs">
          <div>
            <p className="text-muted-foreground text-xs">Skills</p>
            <p className="font-medium">{player.skills}/100</p>
          </div>
          <div>
            <p className="text-muted-foreground text-xs">Effort</p>
            <p className="font-medium">{player.effort}/100</p>
          </div>
          <div>
            <p className="text-muted-foreground text-xs">Stamina</p>
            <p className="font-medium">{player.stamina}/100</p>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

// Helper component for match preview
const MatchPreviewCard: React.FC<{ match: ImportMatch }> = ({ match }) => {
  return (
    <Card className="overflow-hidden">
      <CardHeader className="p-2 pb-0">
        <CardTitle className="text-sm">
          {format(parseISO(match.match_date), 'MMM d, yyyy')}
        </CardTitle>
        <CardDescription className="text-xs">
          {match.teamaByName ? match.teamaByName.length : match.teama?.length || 0} vs {match.teambByName ? match.teambByName.length : match.teamb?.length || 0} players
        </CardDescription>
      </CardHeader>
      <CardContent className="p-2 pt-1">
        <div className="flex justify-between items-center">
          <div className="text-xs">
            <span className="font-medium">Team A:</span> {match.scorea ?? '-'}
          </div>
          <div className="text-xs text-muted-foreground">vs</div>
          <div className="text-xs">
            <span className="font-medium">Team B:</span> {match.scoreb ?? '-'}
          </div>
        </div>
        {match.winner && (
          <div className="mt-1 text-xs">
            Winner: <Badge variant="outline" className="text-xs px-1 py-0">{match.winner}</Badge>
          </div>
        )}

        {/* Show player names if available */}
        {(match.teamaByName || match.teambByName) && (
          <div className="mt-2 grid grid-cols-2 gap-2">
            <div className="text-xs">
              <p className="font-medium mb-1">Team A:</p>
              <ul className="list-disc pl-4 text-xs">
                {match.teamaByName?.slice(0, 3).map((name, i) => (
                  <li key={i} className="truncate">{name}</li>
                ))}
                {match.teamaByName && match.teamaByName.length > 3 && (
                  <li className="text-muted-foreground">+{match.teamaByName.length - 3} more</li>
                )}
              </ul>
            </div>
            <div className="text-xs">
              <p className="font-medium mb-1">Team B:</p>
              <ul className="list-disc pl-4 text-xs">
                {match.teambByName?.slice(0, 3).map((name, i) => (
                  <li key={i} className="truncate">{name}</li>
                ))}
                {match.teambByName && match.teambByName.length > 3 && (
                  <li className="text-muted-foreground">+{match.teambByName.length - 3} more</li>
                )}
              </ul>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default ImportPreview;
