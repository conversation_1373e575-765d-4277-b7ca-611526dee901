import { useState, useEffect } from 'react';
import { useAuth } from '@/context/AuthContext';
import { useToast } from '@/hooks/use-toast';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogFooter,
  DialogDescription,
} from '@/components/ui/dialog';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Badge } from '@/components/ui/badge';
import { Loader2, UserPlus, MoreHorizontal, UserX, Shield, Mail } from 'lucide-react';
import { 
  fetchGroupMembers, 
  updateMemberRole, 
  removeMember, 
  inviteUserByEmail,
  GroupMember,
  GroupRole
} from '@/lib/api/groups';

interface MemberManagerProps {
  groupId: string;
  isCreator: boolean;
  onMembersChange?: () => void;
}

export function MemberManager({ groupId, isCreator, onMembersChange }: MemberManagerProps) {
  const { toast } = useToast();
  const { user } = useAuth();
  const [isLoading, setIsLoading] = useState(true);
  const [members, setMembers] = useState<GroupMember[]>([]);
  const [isInviteDialogOpen, setIsInviteDialogOpen] = useState(false);
  const [inviteEmail, setInviteEmail] = useState('');
  const [inviteRole, setInviteRole] = useState<GroupRole>('Guest');
  const [isInviting, setIsInviting] = useState(false);

  // Fetch members on mount and when groupId changes
  useEffect(() => {
    loadMembers();
  }, [groupId]);

  const loadMembers = async () => {
    setIsLoading(true);
    try {
      const data = await fetchGroupMembers(groupId);
      setMembers(data);
    } catch (error: any) {
      console.error('Error fetching group members:', error);
      toast({
        title: 'Error',
        description: error?.message || 'Failed to load group members.',
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleRoleChange = async (memberId: string, newRole: GroupRole) => {
    try {
      await updateMemberRole(memberId, newRole);
      
      // Update local state
      setMembers(prev => 
        prev.map(member => 
          member.id === memberId ? { ...member, role: newRole } : member
        )
      );
      
      toast({
        title: 'Success',
        description: 'Member role updated successfully.',
      });
      
      if (onMembersChange) onMembersChange();
    } catch (error: any) {
      console.error('Error updating member role:', error);
      toast({
        title: 'Error',
        description: error?.message || 'Failed to update member role.',
        variant: 'destructive',
      });
    }
  };

  const handleRemoveMember = async (memberId: string) => {
    try {
      await removeMember(memberId);
      
      // Update local state
      setMembers(prev => prev.filter(member => member.id !== memberId));
      
      toast({
        title: 'Success',
        description: 'Member removed successfully.',
      });
      
      if (onMembersChange) onMembersChange();
    } catch (error: any) {
      console.error('Error removing member:', error);
      toast({
        title: 'Error',
        description: error?.message || 'Failed to remove member.',
        variant: 'destructive',
      });
    }
  };

  const handleInviteUser = async () => {
    if (!inviteEmail.trim()) {
      toast({
        title: 'Error',
        description: 'Please enter an email address.',
        variant: 'destructive',
      });
      return;
    }

    setIsInviting(true);
    try {
      await inviteUserByEmail(groupId, inviteEmail.trim(), inviteRole);
      
      // Reload members to include the new one
      await loadMembers();
      
      setIsInviteDialogOpen(false);
      setInviteEmail('');
      setInviteRole('Guest');
      
      toast({
        title: 'Success',
        description: 'User invited successfully.',
      });
      
      if (onMembersChange) onMembersChange();
    } catch (error: any) {
      console.error('Error inviting user:', error);
      toast({
        title: 'Error',
        description: error?.message || 'Failed to invite user.',
        variant: 'destructive',
      });
    } finally {
      setIsInviting(false);
    }
  };

  // Get role badge color
  const getRoleBadgeColor = (role: GroupRole) => {
    switch (role) {
      case 'Admin':
        return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-100';
      case 'Collaborator':
        return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-100';
      case 'Guest':
        return 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-100';
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-100';
    }
  };

  return (
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        <h3 className="text-lg font-medium">Group Members</h3>
        {(isCreator || members.some(m => m.user_id === user?.id && m.role === 'Admin')) && (
          <Button 
            onClick={() => setIsInviteDialogOpen(true)}
            size="sm"
            className="bg-soccer-primary hover:bg-soccer-primary/90"
          >
            <UserPlus className="h-4 w-4 mr-2" />
            Invite User
          </Button>
        )}
      </div>

      {isLoading ? (
        <div className="flex justify-center py-8">
          <Loader2 className="h-8 w-8 animate-spin text-soccer-primary" />
        </div>
      ) : members.length === 0 ? (
        <div className="text-center py-8 text-gray-500 dark:text-gray-400">
          No members found in this group.
        </div>
      ) : (
        <div className="border rounded-md">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>User</TableHead>
                <TableHead>Role</TableHead>
                <TableHead className="text-right">Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {members.map((member) => (
                <TableRow key={member.id}>
                  <TableCell className="font-medium">
                    <div className="flex flex-col">
                      <span>{member.user?.email || member.user_id}</span>
                      {member.isCreator && (
                        <span className="text-xs text-gray-500 dark:text-gray-400">Group Creator</span>
                      )}
                    </div>
                  </TableCell>
                  <TableCell>
                    <Badge className={getRoleBadgeColor(member.role)}>
                      {member.role}
                    </Badge>
                  </TableCell>
                  <TableCell className="text-right">
                    {/* Only show actions if current user is creator or admin */}
                    {(isCreator || members.some(m => m.user_id === user?.id && m.role === 'Admin')) && 
                     !member.isCreator && // Can't modify the creator
                     member.user_id !== user?.id && // Can't modify yourself
                     (
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="ghost" size="icon" className="h-8 w-8">
                            <MoreHorizontal className="h-4 w-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                          <DropdownMenuItem onClick={() => handleRoleChange(member.id, 'Admin')}>
                            <Shield className="h-4 w-4 mr-2" />
                            Make Admin
                          </DropdownMenuItem>
                          <DropdownMenuItem onClick={() => handleRoleChange(member.id, 'Collaborator')}>
                            <Shield className="h-4 w-4 mr-2" />
                            Make Collaborator
                          </DropdownMenuItem>
                          <DropdownMenuItem onClick={() => handleRoleChange(member.id, 'Guest')}>
                            <Shield className="h-4 w-4 mr-2" />
                            Make Guest
                          </DropdownMenuItem>
                          <DropdownMenuItem 
                            onClick={() => handleRemoveMember(member.id)}
                            className="text-red-600 focus:text-red-600"
                          >
                            <UserX className="h-4 w-4 mr-2" />
                            Remove Member
                          </DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    )}
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </div>
      )}

      {/* Invite User Dialog */}
      <Dialog open={isInviteDialogOpen} onOpenChange={setIsInviteDialogOpen}>
        <DialogContent className="sm:max-w-md dark:bg-gray-800 dark:border-gray-700">
          <DialogHeader>
            <DialogTitle className="dark:text-white">Invite User to Group</DialogTitle>
            <DialogDescription className="dark:text-gray-300">
              Enter the email address of the user you want to invite.
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4 py-2">
            <div className="space-y-2">
              <label htmlFor="email" className="text-sm font-medium dark:text-white">
                Email Address
              </label>
              <div className="flex items-center">
                <Mail className="h-4 w-4 mr-2 text-gray-500 dark:text-gray-400" />
                <Input
                  id="email"
                  placeholder="<EMAIL>"
                  value={inviteEmail}
                  onChange={(e) => setInviteEmail(e.target.value)}
                  className="dark:bg-gray-700 dark:border-gray-600 dark:text-white dark:placeholder-gray-400"
                />
              </div>
            </div>
            <div className="space-y-2">
              <label htmlFor="role" className="text-sm font-medium dark:text-white">
                Role
              </label>
              <Select value={inviteRole} onValueChange={(value) => setInviteRole(value as GroupRole)}>
                <SelectTrigger className="dark:bg-gray-700 dark:border-gray-600 dark:text-white">
                  <SelectValue placeholder="Select a role" />
                </SelectTrigger>
                <SelectContent className="dark:bg-gray-800 dark:border-gray-700">
                  <SelectItem value="Admin" className="dark:text-white">Admin</SelectItem>
                  <SelectItem value="Collaborator" className="dark:text-white">Collaborator</SelectItem>
                  <SelectItem value="Guest" className="dark:text-white">Guest</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
          <DialogFooter>
            <Button
              onClick={handleInviteUser}
              disabled={isInviting || !inviteEmail.trim()}
              className="bg-soccer-primary hover:bg-soccer-primary/90"
            >
              {isInviting ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Inviting...
                </>
              ) : (
                <>Invite User</>
              )}
            </Button>
            <Button
              variant="outline"
              onClick={() => setIsInviteDialogOpen(false)}
              className="dark:bg-gray-700 dark:border-gray-600 dark:text-white"
            >
              Cancel
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}

export default MemberManager;
