import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { useToast } from '@/hooks/use-toast';
import { createGroupWithNameBasedImport, NameBasedImportData } from '@/lib/api/nameBasedImport';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '@/context/AuthContext';
import { Loader2 } from 'lucide-react';

const NameBasedImport: React.FC = () => {
  const [file, setFile] = useState<File | null>(null);
  const [groupName, setGroupName] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [previewData, setPreviewData] = useState<NameBasedImportData | null>(null);
  const { toast } = useToast();
  const navigate = useNavigate();
  const { user } = useAuth();

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const selectedFile = e.target.files?.[0];
    if (selectedFile) {
      setFile(selectedFile);

      // Read the file to preview
      const reader = new FileReader();
      reader.onload = (event) => {
        try {
          const jsonData = JSON.parse(event.target?.result as string);
          setPreviewData(jsonData);
        } catch (error) {
          toast({
            title: 'Error parsing JSON',
            description: 'The selected file is not valid JSON.',
            variant: 'destructive',
          });
          setFile(null);
        }
      };
      reader.readAsText(selectedFile);
    }
  };

  const handleImport = async () => {
    if (!file || !groupName || !user) {
      toast({
        title: 'Missing information',
        description: 'Please provide a group name and select a file.',
        variant: 'destructive',
      });
      return;
    }

    setIsLoading(true);

    try {
      // Read the file
      const fileContent = await file.text();
      const importData = JSON.parse(fileContent) as NameBasedImportData;

      // Validate the data structure
      if (!importData.players || !importData.matches || !Array.isArray(importData.players) || !Array.isArray(importData.matches)) {
        throw new Error('Invalid data structure. The file must contain "players" and "matches" arrays.');
      }

      // Check if the file uses the name-based format
      const firstMatch = importData.matches[0];
      if (!firstMatch.teamaByName || !firstMatch.teambByName) {
        throw new Error('Invalid file format. This component requires a name-based import file.');
      }

      // Create the group with the imported data
      await createGroupWithNameBasedImport(groupName, user.id, importData);

      toast({
        title: 'Import successful',
        description: `Group "${groupName}" created with ${importData.players.length} players and ${importData.matches.length} matches.`,
      });

      // Redirect to the group selection page
      navigate('/group-selection');
    } catch (error) {
      console.error('Import error:', error);
      toast({
        title: 'Import failed',
        description: (error as Error).message,
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Card className="w-full max-w-md mx-auto">
      <CardHeader>
        <CardTitle>Name-Based Import</CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="space-y-2">
          <Label htmlFor="group-name">Group Name</Label>
          <Input
            id="group-name"
            value={groupName}
            onChange={(e) => setGroupName(e.target.value)}
            placeholder="Enter group name"
            disabled={isLoading}
          />
        </div>

        <div className="space-y-2">
          <Label htmlFor="import-file">Import File (Name-Based Format)</Label>
          <Input
            id="import-file"
            type="file"
            accept=".json"
            onChange={handleFileChange}
            disabled={isLoading}
          />
          <p className="text-xs text-muted-foreground">
            Select a JSON file in the name-based format.
          </p>
        </div>

        {previewData && (
          <div className="text-sm">
            <p>Preview:</p>
            <ul className="list-disc pl-5">
              <li>{previewData.players.length} players</li>
              <li>{previewData.matches.length} matches</li>
            </ul>
          </div>
        )}

        <Button
          onClick={handleImport}
          disabled={!file || !groupName || isLoading}
          className="w-full"
        >
          {isLoading ? (
            <>
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              Importing...
            </>
          ) : (
            'Import'
          )}
        </Button>
      </CardContent>
    </Card>
  );
};

export default NameBasedImport;
