import React from 'react';
import { useGroupPermissions } from '@/hooks/use-group-permissions';
import { GroupRole } from '@/lib/api/groups';

interface PermissionGateProps {
  children: React.ReactNode;
  requiredRole?: GroupRole;
  fallback?: React.ReactNode;
  requireEdit?: boolean;
  requireManageMembers?: boolean;
}

/**
 * Component that conditionally renders content based on user permissions
 */
export function PermissionGate({
  children,
  requiredRole,
  fallback = null,
  requireEdit = false,
  requireManageMembers = false
}: PermissionGateProps) {
  const { canView, canEdit, canManageMembers, userRole, isLoading } = useGroupPermissions();

  // While loading, don't render anything
  if (isLoading) {
    return null;
  }

  // Check specific role requirement
  if (requiredRole) {
    if (requiredRole === 'Admin' && userRole !== 'Admin') {
      return <>{fallback}</>;
    }
    
    if (requiredRole === 'Collaborator' && 
        userRole !== 'Admin' && 
        userRole !== 'Collaborator') {
      return <>{fallback}</>;
    }
  }

  // Check edit permission
  if (requireEdit && !canEdit) {
    return <>{fallback}</>;
  }

  // Check member management permission
  if (requireManageMembers && !canManageMembers) {
    return <>{fallback}</>;
  }

  // If all checks pass, render the children
  return <>{children}</>;
}

export default PermissionGate;
