import { useGroup } from '@/lib/context/GroupContext';
import { useTranslation } from 'react-i18next';
import { Badge } from '@/components/ui/badge';
import { Shield, ShieldAlert, ShieldCheck, ShieldQuestion } from 'lucide-react';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';

interface RoleIndicatorProps {
  showLabel?: boolean;
  className?: string;
}

export function RoleIndicator({ showLabel = true, className = '' }: RoleIndicatorProps) {
  const { t } = useTranslation();
  const { userRole, isLoading } = useGroup();

  if (isLoading) {
    return null;
  }

  // Simple badge without tooltip to prevent recursion issues
  const renderSimpleBadge = () => {
    if (!userRole) {
      return (
        <Badge variant="outline" className={`bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-100 ${className}`}>
          <ShieldQuestion className="h-3.5 w-3.5 mr-1" />
          {showLabel && t('groups.noRole', 'No Role')}
        </Badge>
      );
    }

    switch (userRole) {
      case 'Admin':
        return (
          <Badge className={`bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-100 ${className}`}>
            <ShieldAlert className="h-3.5 w-3.5 mr-1" />
            {showLabel && t('groups.admin')}
          </Badge>
        );
      case 'Collaborator':
        return (
          <Badge className={`bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-100 ${className}`}>
            <ShieldCheck className="h-3.5 w-3.5 mr-1" />
            {showLabel && t('groups.collaborator')}
          </Badge>
        );
      case 'Guest':
        return (
          <Badge className={`bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-100 ${className}`}>
            <Shield className="h-3.5 w-3.5 mr-1" />
            {showLabel && t('groups.guest')}
          </Badge>
        );
      default:
        return null;
    }
  };

  return renderSimpleBadge();
}

export default RoleIndicator;
