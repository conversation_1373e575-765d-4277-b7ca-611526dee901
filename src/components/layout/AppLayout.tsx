import { ReactNode } from "react";
import Navbar from "@/components/layout/Navbar";
import { useTheme } from "@/context/ThemeContext";

interface AppLayoutProps {
  children: ReactNode;
}

const AppLayout = ({ children }: AppLayoutProps) => {
  return (
    <div className="min-h-screen flex flex-col bg-background text-foreground transition-colors duration-200">
      <Navbar />
      <main className="flex-1">
        {children}
      </main>
    </div>
  );
};

export default AppLayout;
