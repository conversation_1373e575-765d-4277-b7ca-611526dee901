import { useState } from "react";
import { Link } from "react-router-dom";
import { cn } from "@/lib/utils";
import {
  Users,
  Calendar,
  LayoutDashboard,
  Medal,
  Shuffle,
  Handshake,
  UserCircle,
  Menu
} from "lucide-react";
import { useLocation } from "react-router-dom";
import { useTranslation } from "react-i18next";
import { Button } from "@/components/ui/button";
import {
  Sheet,
  <PERSON>etContent,
  SheetHeader,
  SheetTitle,
  SheetTrigger
} from "@/components/ui/sheet";
import GroupSelector from "@/components/GroupSelector";
import ThemeToggle from "@/components/ThemeToggle";
import LanguageToggle from "@/components/LanguageToggle";
import LogoComponent from "@/components/LogoComponent";

const getNavigation = (t) => [
  { name: t('nav.dashboard'), href: "/dashboard", icon: LayoutDashboard },
  { name: t('nav.players'), href: "/players", icon: Users },
  { name: t('nav.matches'), href: "/matches", icon: Calendar },
  { name: t('nav.profile'), href: "/profile", icon: UserCircle },
];

const Navbar = () => {
  const location = useLocation();
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const { t } = useTranslation();
  const navigation = getNavigation(t);

  // Mobile navigation links with text
  const renderNavLinks = (isMobile: boolean = false) => (
    navigation.map((item, index) => (
      <Link
        key={item.name}
        to={item.href}
        onClick={() => isMobile && setIsMobileMenuOpen(false)}
        className={cn(
          "px-3 py-2 rounded-md text-sm font-medium flex items-center gap-2 nav-item animate-slide-in-down",
          location.pathname === item.href
            ? "bg-white/10 text-white dark:text-white active"
            : "text-white/70 dark:text-white/70 hover:bg-white/5 hover:text-white dark:hover:text-white",
          isMobile ? "w-full justify-start" : "whitespace-nowrap"
        )}
        style={isMobile ? {
          animationDelay: `${index * 75}ms`
        } : undefined}
      >
        <item.icon className="w-4 h-4 animate-smooth group-hover:scale-110" />
        {item.name}
      </Link>
    ))
  );

  // Desktop navigation links with icons only and hover animation
  const renderDesktopNavLinks = () => (
    navigation.map((item, index) => (
      <Link
        key={item.name}
        to={item.href}
        className={cn(
          "px-3 py-2 rounded-md flex items-center group nav-item",
          "overflow-hidden animate-fade-in",
          location.pathname === item.href
            ? "bg-white/10 text-white dark:text-white w-10 hover:w-auto active"
            : "text-white/70 dark:text-white/70 hover:bg-white/5 hover:text-white dark:hover:text-white w-10 hover:w-auto"
        )}
        title={item.name}
        style={{
          animationDelay: `${index * 100}ms`
        }}
      >
        <item.icon className="w-5 h-5 min-w-[1.25rem] animate-smooth group-hover:scale-110" />
        <span className="ml-2 opacity-0 max-w-0 group-hover:max-w-[150px] group-hover:opacity-100 animate-slow whitespace-nowrap text-sm font-medium overflow-hidden block">
          {item.name}
        </span>
      </Link>
    ))
  );

  return (
    <nav className="bg-soccer-primary text-white dark:bg-soccer-dark dark:text-white transition-colors duration-200">
      <div className="container mx-auto">
        <div className="flex items-center justify-between h-16">
          <div className="px-4 flex items-center gap-4">
            <div className="h-8 w-8">
              <LogoComponent to="/dashboard" height="32px" width="32px" />
            </div>
            <GroupSelector />
          </div>

          <div className="hidden md:flex items-center gap-3">
            {renderDesktopNavLinks()}
            <div className="ml-2">
              <ThemeToggle />
            </div>
            <div className="ml-2">
              <LanguageToggle />
            </div>
          </div>

          <div className="md:hidden px-4">
            <Sheet open={isMobileMenuOpen} onOpenChange={setIsMobileMenuOpen}>
              <SheetTrigger asChild>
                <Button variant="ghost" size="icon" className="hover:bg-white/10 focus:bg-white/10 text-white dark:text-white">
                  <Menu className="h-6 w-6" />
                  <span className="sr-only">Open menu</span>
                </Button>
              </SheetTrigger>
              <SheetContent side="left" className="bg-soccer-primary dark:bg-soccer-dark text-white dark:text-white border-soccer-accent/20 dark:border-gray-800 pt-10 w-[250px] transition-colors duration-200">
                <SheetHeader className="mb-4 text-left">
                  <SheetTitle className="text-white dark:text-white flex items-center">
                    <div className="h-8 w-8">
                      <LogoComponent to="/dashboard" height="32px" width="32px" />
                    </div>
                  </SheetTitle>
                </SheetHeader>
                <div className="mb-4">
                  <GroupSelector />
                </div>
                <div className="flex flex-col space-y-2">
                  {renderNavLinks(true)}
                </div>
                <div className="mt-4 flex flex-col space-y-3">
                  <div className="flex items-center gap-2">
                    <ThemeToggle />
                    <span className="text-sm">{t('profile.theme')}</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <LanguageToggle />
                    <span className="text-sm">{t('profile.language')}</span>
                  </div>
                </div>
              </SheetContent>
            </Sheet>
          </div>
        </div>
      </div>
    </nav>
  );
};

export default Navbar;
