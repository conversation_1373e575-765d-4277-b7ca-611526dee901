import { ReactNode } from "react";

interface PageLayoutProps {
  children: ReactNode;
  title: string;
  action?: ReactNode;
}

const PageLayout = ({ children, title, action }: PageLayoutProps) => {
  return (
    <div className="container mx-auto py-6 space-y-6 transition-colors duration-200">
      <div className="flex justify-between items-center">
        <h1 className="text-3xl font-bold tracking-tight text-foreground">{title}</h1>
        {action}
      </div>
      <div className="w-full">{children}</div>
    </div>
  );
};

export default PageLayout;
