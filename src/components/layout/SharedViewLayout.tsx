import { ReactNode, useState } from "react";
import { Link, useLocation, useParams } from "react-router-dom";
import { cn } from "@/lib/utils";
import {
  Users,
  Calendar,
  LayoutDashboard,
  Medal,
  Shuffle,
  Handshake,
  Menu,
  Share2,
  MessageSquare,
  Coffee
} from "lucide-react";
import LogoComponent from "@/components/LogoComponent";
import { Button } from "@/components/ui/button";
import {
  Sheet,
  <PERSON><PERSON><PERSON>onte<PERSON>,
  <PERSON><PERSON><PERSON>eader,
  SheetTitle,
  SheetTrigger
} from "@/components/ui/sheet";
import ThemeToggle from "@/components/ThemeToggle";
import LanguageToggle from "@/components/LanguageToggle";
import { useTheme } from "@/context/ThemeContext";
import { useTranslation } from "react-i18next";

// Constants for external links
const FEEDBACK_FORM_URL = "https://docs.google.com/forms/d/e/1FAIpQLSdtZuMIn1UVQIqCdjC7cJOk7DYoqIMeYv-HtK9o-vPZ6UIrAg/viewform?usp=sharing";
const CAFECITO_URL = "https://cafecito.app/fulbitostats";

interface SharedViewLayoutProps {
  children: ReactNode;
  title?: string;
  groupName?: string;
  accessLevel?: 'read' | 'comment';
}

// Navigation links will be dynamically generated with the groupId

const SharedViewLayout = ({ children, title, groupName, accessLevel = 'read' }: SharedViewLayoutProps) => {
  const location = useLocation();
  const { groupId } = useParams<{ groupId: string }>();
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const { theme } = useTheme();
  const { t } = useTranslation();

  // Dynamically generate navigation links with the groupId
  const navigation = [
    { name: t('nav.dashboard'), href: `/shared/${groupId}/dashboard`, icon: LayoutDashboard },
    { name: t('nav.players'), href: `/shared/${groupId}/players`, icon: Users },
    { name: t('nav.matches'), href: `/shared/${groupId}/matches`, icon: Calendar },
  ];

  // Mobile navigation links with text
  const renderNavLinks = (isMobile: boolean = false) => (
    navigation.map((item) => (
      <Link
        key={item.name}
        to={item.href}
        onClick={() => isMobile && setIsMobileMenuOpen(false)}
        className={cn(
          "px-3 py-2 rounded-md text-sm font-medium flex items-center gap-2",
          location.pathname === item.href
            ? "bg-white/10 text-white"
            : "text-white/70 hover:bg-white/5 hover:text-white",
          isMobile ? "w-full justify-start" : "whitespace-nowrap"
        )}
      >
        <item.icon className="w-4 h-4" />
        {item.name}
      </Link>
    ))
  );

  // Desktop navigation links with icons only and hover animation
  const renderDesktopNavLinks = () => (
    navigation.map((item) => (
      <Link
        key={item.name}
        to={item.href}
        className={cn(
          "px-3 py-2 rounded-md flex items-center group",
          "transition-all duration-300 ease-in-out overflow-hidden",
          location.pathname === item.href
            ? "bg-white/10 text-white w-10 hover:w-auto"
            : "text-white/70 hover:bg-white/5 hover:text-white w-10 hover:w-auto"
        )}
        title={item.name}
      >
        <item.icon className="w-5 h-5 min-w-[1.25rem]" />
        <span className="ml-2 opacity-0 max-w-0 group-hover:max-w-[150px] group-hover:opacity-100 transition-all duration-300 whitespace-nowrap text-sm font-medium overflow-hidden block">
          {item.name}
        </span>
      </Link>
    ))
  );

  // Helper functions to open external links
  const openFeedbackForm = () => {
    window.open(FEEDBACK_FORM_URL, '_blank', 'noopener');
  };

  const openCafecitoPage = () => {
    window.open(CAFECITO_URL, '_blank', 'noopener');
  };

  return (
    <div className="min-h-screen flex flex-col bg-gray-50 dark:bg-soccer-dark transition-colors duration-200">
      <nav className="bg-soccer-secondary text-white">
        <div className="container mx-auto">
          <div className="flex items-center justify-between h-16">
            <div className="flex items-center gap-4">
              <div className="h-8 w-8">
                <LogoComponent to={`/shared/${groupId}/dashboard`} height="32px" width="32px" className="text-white" />
              </div>
              {groupName && (
                <div className="bg-white/10 px-3 py-1 rounded text-sm flex items-center">
                  <Share2 className="h-3.5 w-3.5 mr-2" />
                  {t('sharing.viewing')}: {groupName}
                </div>
              )}
            </div>

            <div className="hidden md:flex items-center gap-3">
              {renderDesktopNavLinks()}

              {/* Feedback button */}
              <Button
                variant="ghost"
                className="bg-gradient-to-r from-purple-500 to-purple-600 hover:from-purple-600 hover:to-purple-700 text-white flex items-center gap-2 shadow-md hover:shadow-lg hover:scale-105 transition-all duration-300"
                onClick={openFeedbackForm}
              >
                <MessageSquare className="h-4 w-4" />
                {t('common.feedback', 'Feedback')}
              </Button>

              {/* Cafecito button */}
              <Button
                variant="ghost"
                className="bg-gradient-to-r from-blue-400 to-blue-500 hover:from-blue-500 hover:to-blue-600 text-white flex items-center gap-2 shadow-md hover:shadow-lg hover:scale-105 transition-all duration-300"
                onClick={openCafecitoPage}
              >
                <Coffee className="h-4 w-4" />
                {t('common.donate', 'Donate')}
              </Button>

              <LanguageToggle className="ml-2" />
              <ThemeToggle className="ml-2" />
            </div>

            <div className="md:hidden">
              <Sheet open={isMobileMenuOpen} onOpenChange={setIsMobileMenuOpen}>
                <SheetTrigger asChild>
                  <Button variant="ghost" size="icon" className="hover:bg-white/10 focus:bg-white/10">
                    <Menu className="h-6 w-6" />
                    <span className="sr-only">{t('common.openMenu')}</span>
                  </Button>
                </SheetTrigger>
                <SheetContent side="left" className="bg-soccer-secondary text-white border-blue-800 pt-10 w-[250px]">
                  <SheetHeader className="mb-4 text-left">
                    <SheetTitle className="text-white">{t('common.navigation')}</SheetTitle>
                  </SheetHeader>
                  {groupName && (
                    <div className="mb-4 bg-white/10 px-3 py-2 rounded text-sm">
                      <Share2 className="h-3.5 w-3.5 mr-2 inline-block" />
                      {t('sharing.viewing')}: {groupName}
                    </div>
                  )}
                  <div className="flex flex-col space-y-2">
                    {renderNavLinks(true)}
                    <div className="mt-4 px-3 py-2 space-y-3">
                      {/* Feedback button */}
                      <div className="flex items-center gap-2">
                        <Button
                          variant="ghost"
                          className="w-full justify-start bg-gradient-to-r from-purple-500 to-purple-600 hover:from-purple-600 hover:to-purple-700 text-white shadow-md hover:shadow-lg hover:scale-105 transition-all duration-300"
                          onClick={openFeedbackForm}
                        >
                          <MessageSquare className="h-4 w-4 mr-2" />
                          {t('common.feedback', 'Feedback')}
                        </Button>
                      </div>

                      {/* Cafecito button */}
                      <div className="flex items-center gap-2">
                        <Button
                          variant="ghost"
                          className="w-full justify-start bg-gradient-to-r from-blue-400 to-blue-500 hover:from-blue-500 hover:to-blue-600 text-white shadow-md hover:shadow-lg hover:scale-105 transition-all duration-300"
                          onClick={openCafecitoPage}
                        >
                          <Coffee className="h-4 w-4 mr-2" />
                          {t('common.donate', 'Donate')}
                        </Button>
                      </div>

                      <div className="flex items-center gap-2">
                        <span className="text-sm font-medium">{t('profile.language')}:</span>
                        <LanguageToggle />
                      </div>
                      <div className="flex items-center gap-2">
                        <span className="text-sm font-medium">{t('profile.theme')}:</span>
                        <ThemeToggle />
                      </div>
                    </div>
                  </div>
                </SheetContent>
              </Sheet>
            </div>
          </div>
        </div>
      </nav>

      <main className="flex-1 container mx-auto py-6 dark:text-white">
        {title && (
          <div className="mb-6">
            <h1 className="text-2xl font-bold">{title}</h1>
            <div className="h-1 w-20 bg-soccer-primary mt-2"></div>
          </div>
        )}
        {children}
      </main>

      <footer className="bg-gray-100 dark:bg-gray-800 py-4 border-t dark:border-gray-700">
        <div className="container mx-auto text-center text-sm text-gray-500 dark:text-gray-400">
          <p>
            {accessLevel === 'read' ? t('sharing.viewOnlyMode') : t('sharing.commentEnabledMode')} {' '}
            <Link to="/" className="text-soccer-primary hover:underline">{t('auth.login')}</Link> {t('sharing.toEditData')}
          </p>
        </div>
      </footer>
    </div>
  );
};

export default SharedViewLayout;
