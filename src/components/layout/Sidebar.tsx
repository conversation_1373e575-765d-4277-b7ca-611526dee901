import { useState } from "react";
import { <PERSON> } from "react-router-dom";
import { cn } from "@/lib/utils";
import {
  UserPlus,
  LayoutDashboard,
  UserCircle,
  Menu,
  LogOut,
  Trophy,
  ChevronLeft,
  ChevronRight
} from "lucide-react";
import { useLocation } from "react-router-dom";
import { useTranslation } from "react-i18next";
import { Button } from "@/components/ui/button";
import {
  Sheet,
  SheetContent,
  SheetHeader,
  SheetTitle,
  SheetTrigger
} from "@/components/ui/sheet";
import GroupSelector from "@/components/GroupSelector";
import ThemeToggle from "@/components/ThemeToggle";
import LanguageToggle from "@/components/LanguageToggle";
import LogoComponent from "@/components/LogoComponent";
import { useAuth } from "@/context/AuthContext";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";

const getNavigation = (t) => [
  { name: t('nav.dashboard'), href: "/dashboard", icon: LayoutDashboard },
  { name: t('nav.players'), href: "/players", icon: UserPlus },
  { name: t('nav.matches'), href: "/matches", icon: Trophy },
];

const Sidebar = () => {
  const location = useLocation();
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const [isCollapsed, setIsCollapsed] = useState(false);
  const { t } = useTranslation();
  const { signOut } = useAuth();
  const navigation = getNavigation(t);

  const toggleSidebar = () => {
    setIsCollapsed(!isCollapsed);
  };

  // Navigation links with text
  const renderNavLinks = () => (
    navigation.map((item) => (
      <Link
        key={item.name}
        to={item.href}
        className={cn(
          "px-3 py-3 rounded-md text-sm font-medium flex items-center",
          location.pathname === item.href
            ? "bg-white/10 text-white"
            : "text-white/70 hover:bg-white/5 hover:text-white",
          isCollapsed ? "justify-center" : "gap-3"
        )}
      >
        <item.icon className="w-5 h-5" />
        {!isCollapsed && <span>{item.name}</span>}
      </Link>
    ))
  );

  // Mobile navigation links
  const renderMobileNavLinks = () => (
    navigation.map((item) => (
      <Link
        key={item.name}
        to={item.href}
        onClick={() => setIsMobileMenuOpen(false)}
        className={cn(
          "px-3 py-3 rounded-md text-sm font-medium flex items-center gap-3 w-full",
          location.pathname === item.href
            ? "bg-white/10 text-white"
            : "text-white/70 hover:bg-white/5 hover:text-white"
        )}
      >
        <item.icon className="w-5 h-5" />
        <span>{item.name}</span>
      </Link>
    ))
  );

  // Desktop sidebar
  const renderDesktopSidebar = () => (
    <div
      className={cn(
        "hidden md:flex flex-col h-screen min-h-screen sticky top-0 bg-soccer-primary dark:bg-soccer-primary-dark text-white transition-all duration-300",
        isCollapsed ? "w-[70px]" : "w-[250px]"
      )}
    >
      <div className="p-4 flex items-center justify-between">
        {!isCollapsed && <LogoComponent to="/dashboard" />}
        <Button
          variant="ghost"
          size="icon"
          className="hover:bg-white/10 focus:bg-white/10 ml-auto"
          onClick={toggleSidebar}
        >
          {isCollapsed ? <ChevronRight className="h-5 w-5" /> : <ChevronLeft className="h-5 w-5" />}
        </Button>
      </div>

      {!isCollapsed && (
        <div className="px-3 mb-6">
          <GroupSelector />
        </div>
      )}

      <div className="flex-1 px-3 py-2 space-y-2">
        {renderNavLinks()}
      </div>

      <div className="p-4 border-t border-white/10">
        {isCollapsed ? (
          <div className="flex flex-col items-center gap-4">
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Link to="/profile">
                    <Button
                      variant="ghost"
                      size="icon"
                      className="hover:bg-white/10 focus:bg-white/10"
                    >
                      <UserCircle className="h-5 w-5" />
                    </Button>
                  </Link>
                </TooltipTrigger>
                <TooltipContent side="right">
                  <p>{t('nav.profile')}</p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>

            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <div><ThemeToggle /></div>
                </TooltipTrigger>
                <TooltipContent side="right">
                  <p>{t('profile.theme')}</p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>

            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <div><LanguageToggle /></div>
                </TooltipTrigger>
                <TooltipContent side="right">
                  <p>{t('profile.language')}</p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>

            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button
                    variant="ghost"
                    size="icon"
                    className="hover:bg-white/10 focus:bg-white/10"
                    onClick={signOut}
                  >
                    <LogOut className="h-5 w-5" />
                  </Button>
                </TooltipTrigger>
                <TooltipContent side="right">
                  <p>{t('auth.signOut')}</p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
          </div>
        ) : (
          <div className="space-y-4">
            <Link to="/profile" className="block">
              <Button
                variant="ghost"
                className="w-full justify-start text-white/70 hover:bg-white/5 hover:text-white"
              >
                <UserCircle className="h-5 w-5 mr-2" />
                {t('nav.profile')}
              </Button>
            </Link>
            <div className="flex items-center justify-between">
              <span className="text-sm">{t('profile.theme')}</span>
              <ThemeToggle />
            </div>
            <div className="flex items-center justify-between">
              <span className="text-sm">{t('profile.language')}</span>
              <LanguageToggle />
            </div>
            <Button
              variant="ghost"
              className="w-full justify-start text-white/70 hover:bg-white/5 hover:text-white"
              onClick={signOut}
            >
              <LogOut className="h-5 w-5 mr-2" />
              {t('auth.signOut')}
            </Button>
          </div>
        )}
      </div>
    </div>
  );

  // Mobile header with hamburger menu
  const renderMobileHeader = () => (
    <div className="md:hidden bg-soccer-primary dark:bg-soccer-primary-dark text-white p-4 flex items-center justify-between">
      <div className="flex items-center gap-4">
        <LogoComponent to="/dashboard" />
      </div>

      <Sheet open={isMobileMenuOpen} onOpenChange={setIsMobileMenuOpen}>
        <SheetTrigger asChild>
          <Button variant="ghost" size="icon" className="hover:bg-white/10 focus:bg-white/10">
            <Menu className="h-6 w-6" />
            <span className="sr-only">Open menu</span>
          </Button>
        </SheetTrigger>
        <SheetContent side="left" className="bg-soccer-primary dark:bg-soccer-primary-dark text-white border-soccer-accent/20 pt-10 w-[280px] transition-colors duration-200">
          <SheetHeader className="mb-4 text-left">
            <SheetTitle className="text-white flex items-center">
              <LogoComponent to="/dashboard" />
            </SheetTitle>
          </SheetHeader>
          <div className="mb-6">
            <GroupSelector />
          </div>
          <div className="flex flex-col space-y-2">
            {renderMobileNavLinks()}
          </div>
          <div className="mt-6 pt-6 border-t border-white/10 space-y-4">
            <Link to="/profile" className="block">
              <Button
                variant="ghost"
                className="w-full justify-start text-white/70 hover:bg-white/5 hover:text-white"
              >
                <UserCircle className="h-5 w-5 mr-2" />
                {t('nav.profile')}
              </Button>
            </Link>
            <div className="flex items-center gap-3">
              <ThemeToggle />
              <span className="text-sm">{t('profile.theme')}</span>
            </div>
            <div className="flex items-center gap-3">
              <LanguageToggle />
              <span className="text-sm">{t('profile.language')}</span>
            </div>
            <Button
              variant="ghost"
              className="w-full justify-start text-white/70 hover:bg-white/5 hover:text-white mt-2"
              onClick={signOut}
            >
              <LogOut className="h-5 w-5 mr-2" />
              {t('auth.signOut')}
            </Button>
          </div>
        </SheetContent>
      </Sheet>
    </div>
  );

  return (
    <>
      {renderDesktopSidebar()}
      {renderMobileHeader()}
    </>
  );
};

export default Sidebar;
