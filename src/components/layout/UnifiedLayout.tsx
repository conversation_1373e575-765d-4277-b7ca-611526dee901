import { ReactNode } from "react";
import Sidebar from "@/components/layout/Sidebar";
import TopNavbar from "@/components/layout/TopNavbar";
import MobileBottomNav from "@/components/layout/MobileBottomNav";
import { useTheme } from "@/context/ThemeContext";
import { OfflineIndicator } from "@/components/ui/offline-indicator";
import { useOffline } from "@/context/OfflineContext";
import { useMediaQuery } from "@/hooks/use-media-query";
import { HalftoneBackground } from "@/components/ui/halftone-background";

interface UnifiedLayoutProps {
  children: ReactNode;
  title?: string;
  action?: ReactNode;
  showNavbar?: boolean;
  showExperimentalBadge?: boolean;
  className?: string;
}

/**
 * A unified layout component that can be configured to replace both AppLayout and PageLayout
 *
 * @param children - The content to render inside the layout
 * @param title - Optional page title to display
 * @param action - Optional action component to display next to the title
 * @param showNavbar - Whether to show the navbar (default: true)
 * @param showExperimentalBadge - Whether to show the experimental UI badge (default: false)
 * @param className - Additional CSS classes to apply to the container
 */
const UnifiedLayout = ({
  children,
  title,
  action,
  showNavbar = true,
  showExperimentalBadge = false,
  className
}: UnifiedLayoutProps) => {
  const { theme } = useTheme();
  const { isOffline } = useOffline();
  const isMobile = useMediaQuery("(max-width: 768px)");

  return (
    <div className="min-h-screen flex flex-col bg-background text-foreground transition-all duration-300 page-transition relative">
      {/* Dynamic Halftone Background */}
      <HalftoneBackground className="halftone-background" />

      {showNavbar && <TopNavbar />}

      <main className={`flex-1 w-full ${isMobile ? 'pb-16' : ''} relative z-10`}>
        <div className="md:py-8 md:px-8 p-4 space-y-8 transition-all duration-300 max-w-[1400px] mx-auto animate-fade-in">
          {title ? (
            <>
              <div className="flex justify-between items-center mb-6">
                <h1 className="text-2xl md:text-3xl font-bold tracking-tight text-foreground bg-gradient-to-r from-soccer-primary to-soccer-primary-light bg-clip-text text-transparent hover:scale-105 transition-transform duration-300">{title}</h1>
                {action}
              </div>
              <div className="w-full space-y-6">{children}</div>
            </>
          ) : (
            <div className="space-y-6">{children}</div>
          )}
        </div>
      </main>

      {isMobile && <MobileBottomNav />}

      {showExperimentalBadge && (
        <div className={`fixed bottom-4 left-4 text-xs px-2 py-1 rounded-md shadow-sm ${
          theme === 'dark' ? 'bg-soccer-dark text-muted-foreground' : 'bg-white/80 text-muted-foreground'
        }`}>
          <p>Experimental UI Mode</p>
        </div>
      )}

      {/* Offline indicator */}
      <div className={`fixed ${isMobile ? 'bottom-20' : 'bottom-4'} right-4 z-50 ${isOffline ? 'animate-pulse' : ''}`}>
        <div className={`px-3 py-2 rounded-md shadow-md ${theme === 'dark' ? 'bg-soccer-dark' : 'bg-white'}`}>
          <OfflineIndicator showSyncStatus={true} />
        </div>
      </div>
    </div>
  );
};

export default UnifiedLayout;
