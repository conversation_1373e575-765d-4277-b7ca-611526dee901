import { useState, useEffect, useRef } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Separator } from "@/components/ui/separator";
import { format, formatDistanceToNow } from "date-fns";
import { Loader2, MessageSquare, Send, Trash2, AlertCircle } from "lucide-react";
import { supabase } from "@/lib/supabase";
import { useToast } from "@/hooks/use-toast";
import { useAuth } from "@/context/AuthContext";
import { PlayerAvatar } from "@/components/players/PlayerAvatar";
import { useMediaQuery } from "@/hooks/use-media-query";
import { useTranslation } from "react-i18next";

interface Player {
  id: number;
  name: string;
  avatar_url?: string | null;
}

interface Comment {
  id: string;
  match_id: number;
  user_id: string;
  content: string;
  created_at: string;
  // Note: We're no longer using the user object directly from the join
  // Instead we'll use the user_id to determine if it's the current user
}

interface MatchCommentsProps {
  matchId: number;
  players: Player[];
}

export function MatchComments({ matchId, players }: MatchCommentsProps) {
  const { t } = useTranslation();
  const { toast } = useToast();
  const { user } = useAuth();
  const [comments, setComments] = useState<Comment[]>([]);
  const [newComment, setNewComment] = useState("");
  const [loading, setLoading] = useState(true);
  const [submitting, setSubmitting] = useState(false);
  const [userProfiles, setUserProfiles] = useState<{[key: string]: {name: string, avatar?: string}}>({});
  const [error, setError] = useState<string | null>(null);
  const [lastAddedCommentId, setLastAddedCommentId] = useState<string | null>(null);
  const commentsEndRef = useRef<HTMLDivElement>(null);
  const textareaRef = useRef<HTMLTextAreaElement>(null);
  const preferReducedMotion = useMediaQuery("(prefers-reduced-motion: reduce)");

  // Function to fetch user profiles for comments
  const fetchUserProfiles = async (comments: Comment[]) => {
    // If there are no comments, don't do anything
    if (!comments.length) return;

    // Get unique user IDs from comments
    const userIds = [...new Set(comments.map(comment => comment.user_id))];

    // For each user ID, if it's not the current user and not already in userProfiles, add a placeholder
    const newProfiles: {[key: string]: {name: string, avatar?: string}} = {...userProfiles};
    let needsUpdate = false;

    userIds.forEach(userId => {
      // If it's the current user, we already have their info
      if (user && userId === user.id) return;

      // If we don't have this user's profile yet, add a placeholder
      if (!newProfiles[userId]) {
        newProfiles[userId] = { name: `User ${Object.keys(newProfiles).length + 1}` };
        needsUpdate = true;
      }
    });

    // Only update state if we added new profiles
    if (needsUpdate) {
      setUserProfiles(newProfiles);
    }
  };

  // Fetch comments on mount and when matchId changes
  useEffect(() => {
    const fetchComments = async () => {
      setLoading(true);
      try {
        const { data, error } = await supabase
          .from("match_comments")
          .select("*, user_id, content, created_at")
          .eq("match_id", matchId)
          .order("created_at", { ascending: true });

        if (error) throw error;
        const commentsData = data || [];
        setComments(commentsData);

        // Fetch user profiles for the comments
        await fetchUserProfiles(commentsData);
      } catch (error) {
        console.error("Error fetching comments:", error);
        toast({
          title: t('matches.comments.error', 'Error'),
          description: t('matches.comments.loadError', 'Failed to load comments'),
          variant: "destructive",
        });
      } finally {
        setLoading(false);
      }
    };

    fetchComments();

    // Set up real-time subscription
    const subscription = supabase
      .channel(`match_comments:${matchId}`)
      .on(
        "postgres_changes",
        {
          event: "*",
          schema: "public",
          table: "match_comments",
          filter: `match_id=eq.${matchId}`,
        },
        () => {
          fetchComments();
        }
      )
      .subscribe();

    return () => {
      subscription.unsubscribe();
    };
  }, [matchId, toast]);

  // Scroll to the latest comment when comments change
  useEffect(() => {
    if (comments.length > 0 && lastAddedCommentId) {
      // Scroll to the bottom of comments when a new one is added
      commentsEndRef.current?.scrollIntoView({ behavior: preferReducedMotion ? 'auto' : 'smooth' });
      // Reset the last added comment ID
      setLastAddedCommentId(null);
    }
  }, [comments, lastAddedCommentId, preferReducedMotion]);

  // Handle comment submission
  const handleSubmitComment = async () => {
    if (!newComment.trim() || !user) {
      setError(t('matches.comments.enterComment', 'Please enter a comment'));
      textareaRef.current?.focus();
      return;
    }

    setError(null);
    setSubmitting(true);
    try {
      // Process @mentions
      const processedComment = processMentions(newComment);

      // Get the current group ID from localStorage
      const selectedGroupId = localStorage.getItem('selectedGroupId');

      const { data, error } = await supabase
        .from("match_comments")
        .insert({
          match_id: matchId,
          user_id: user.id,
          content: processedComment,
          group_id: selectedGroupId,
        })
        .select("*, user_id, content, created_at");

      if (error) throw error;

      if (data && data.length > 0) {
        const newComments = [...comments, data[0]];
        setComments(newComments);
        setNewComment("");
        setLastAddedCommentId(data[0].id);

        // Announce to screen readers
        toast({
          title: t('matches.comments.posted', 'Comment posted'),
          description: t('matches.comments.addSuccess', 'Your comment has been added successfully'),
        });

        // Update user profiles with the new comment
        await fetchUserProfiles(newComments);
      }
    } catch (error) {
      console.error("Error adding comment:", error);
      setError(t('matches.comments.addErrorTryAgain', 'Failed to add comment. Please try again.'));
      toast({
        title: t('matches.comments.error', 'Error'),
        description: t('matches.comments.addError', 'Failed to add comment'),
        variant: "destructive",
      });
    } finally {
      setSubmitting(false);
    }
  };

  // Handle keyboard submission with Ctrl+Enter or Cmd+Enter
  const handleKeyDown = (e: React.KeyboardEvent) => {
    if ((e.ctrlKey || e.metaKey) && e.key === 'Enter') {
      e.preventDefault();
      handleSubmitComment();
    }
  };

  // Handle comment deletion
  const handleDeleteComment = async (commentId: string) => {
    // Confirm deletion
    if (!window.confirm(t('matches.comments.confirmDelete', 'Are you sure you want to delete this comment?'))) {
      return;
    }

    try {
      const { error } = await supabase
        .from("match_comments")
        .delete()
        .eq("id", commentId);

      if (error) throw error;

      setComments(comments.filter((comment) => comment.id !== commentId));
      toast({
        title: t('matches.comments.deleted', 'Comment deleted'),
        description: t('matches.comments.deleteSuccess', 'Your comment has been removed'),
      });

      // Announce to screen readers
      const announcer = document.createElement('div');
      announcer.setAttribute('aria-live', 'assertive');
      announcer.className = 'sr-only';
      announcer.textContent = t('matches.comments.deleteSuccessAnnounce', 'Comment deleted successfully');
      document.body.appendChild(announcer);
      setTimeout(() => document.body.removeChild(announcer), 1000);

    } catch (error) {
      console.error("Error deleting comment:", error);
      toast({
        title: t('matches.comments.error', 'Error'),
        description: t('matches.comments.deleteError', 'Failed to delete comment'),
        variant: "destructive",
      });
    }
  };

  // Process @mentions in comment text
  const processMentions = (text: string): string => {
    // Find all @mentions in the text
    const mentionRegex = /@(\w+)/g;
    let matches;
    let processedText = text;

    while ((matches = mentionRegex.exec(text)) !== null) {
      const mentionName = matches[1];

      // Find player by name (case insensitive)
      const player = players.find(
        (p) => p.name.toLowerCase() === mentionName.toLowerCase()
      );

      if (player) {
        // Replace @mention with a special format that includes player ID
        processedText = processedText.replace(
          `@${mentionName}`,
          `@[player:${player.id}:${player.name}]`
        );
      }
    }

    return processedText;
  };

  // Render comment content with formatted @mentions
  const renderCommentContent = (content: string) => {
    // Match the special format for player mentions
    const mentionRegex = /@\[player:(\d+):([^\]]+)\]/g;
    const parts = [];
    let lastIndex = 0;
    let match;

    while ((match = mentionRegex.exec(content)) !== null) {
      // Add text before the mention
      if (match.index > lastIndex) {
        parts.push(content.substring(lastIndex, match.index));
      }

      // Add the mention as a styled span
      const playerId = parseInt(match[1]);
      const playerName = match[2];
      parts.push(
        <span
          key={`mention-${match.index}`}
          className="bg-primary/10 text-primary rounded px-1 py-0.5 font-medium"
          role="mark"
          aria-label={t('matches.comments.mentionedPlayer', 'Mentioned player: {{name}}', { name: playerName })}
        >
          @{playerName}
        </span>
      );

      lastIndex = match.index + match[0].length;
    }

    // Add any remaining text
    if (lastIndex < content.length) {
      parts.push(content.substring(lastIndex));
    }

    return parts;
  };

  // Get user display name
  const getUserName = (comment: Comment) => {
    // If the comment user_id matches the current user, use current user info
    if (user && comment.user_id === user.id) {
      return user.user_metadata?.full_name || user.email?.split('@')[0] || "Anonymous";
    }
    // Otherwise use the name from userProfiles or a fallback
    return userProfiles[comment.user_id]?.name || `User`;
  };

  // Get user avatar
  const getUserAvatar = (comment: Comment) => {
    // If the comment user_id matches the current user, use current user avatar
    if (user && comment.user_id === user.id) {
      return user.user_metadata?.avatar_url;
    }
    // Otherwise use the avatar from userProfiles or null
    return userProfiles[comment.user_id]?.avatar || null;
  };

  // Get user initials for avatar fallback
  const getUserInitials = (comment: Comment) => {
    // If the comment user_id matches the current user, use current user initials
    if (user && comment.user_id === user.id) {
      const name = user.user_metadata?.full_name || user.email?.split('@')[0] || "";
      if (!name) return "?";

      const nameParts = name.split(" ");
      if (nameParts.length === 1) return name.charAt(0).toUpperCase();
      return (nameParts[0].charAt(0) + nameParts[nameParts.length - 1].charAt(0)).toUpperCase();
    }
    // Otherwise use the first letter of the name from userProfiles
    const name = userProfiles[comment.user_id]?.name || "User";
    return name.charAt(0).toUpperCase();
  };

  return (
    <Card>
      <CardHeader className="py-3">
        <CardTitle className="flex items-center gap-1 text-base">
          <MessageSquare className="h-4 w-4" aria-hidden="true" />
          {t('matches.comments.title', 'Comments')}
        </CardTitle>
        <CardDescription className="text-xs">
          {t('matches.comments.description', 'Discuss this match with your teammates')}
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-3 py-2">
        {loading ? (
          <div className="flex justify-center py-4" aria-live="polite" aria-busy="true">
            <Loader2 className="h-5 w-5 animate-spin text-muted-foreground" />
            <span className="sr-only">{t('matches.comments.loading', 'Loading comments...')}</span>
          </div>
        ) : comments.length === 0 ? (
          <div className="text-center py-4 text-muted-foreground" role="status" aria-live="polite">
            <MessageSquare className="h-8 w-8 mx-auto mb-1 opacity-20" aria-hidden="true" />
            <p className="text-xs">{t('matches.comments.noComments', 'No comments yet')}</p>
            <p className="text-xs">{t('matches.comments.beFirst', 'Be the first to comment on this match')}</p>
          </div>
        ) : (
          <div
            className="space-y-2 max-h-[300px] overflow-y-auto pr-2"
            role="log"
            aria-label={t('matches.comments.matchComments', 'Match comments')}
            aria-live="polite"
          >
            {comments.map((comment) => (
              <article key={comment.id} className="flex gap-2">
                <Avatar className="h-6 w-6 flex-shrink-0">
                  <AvatarImage src={getUserAvatar(comment)} alt={`${getUserName(comment)}'s avatar`} />
                  <AvatarFallback>{getUserInitials(comment)}</AvatarFallback>
                </Avatar>
                <div className="flex-1 space-y-0.5">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-1">
                      <span className="font-medium text-xs">
                        {getUserName(comment)}
                      </span>
                      <time
                        dateTime={comment.created_at}
                        className="text-xs text-muted-foreground"
                      >
                        {formatDistanceToNow(new Date(comment.created_at), { addSuffix: true })}
                      </time>
                    </div>
                    {user?.id === comment.user_id && (
                      <Button
                        variant="ghost"
                        size="icon"
                        className="h-5 w-5"
                        onClick={() => handleDeleteComment(comment.id)}
                        aria-label={t('matches.comments.deleteComment', 'Delete comment')}
                      >
                        <Trash2 className="h-3 w-3" aria-hidden="true" />
                      </Button>
                    )}
                  </div>
                  <div className="text-xs">
                    {renderCommentContent(comment.content)}
                  </div>
                </div>
              </article>
            ))}
            <div ref={commentsEndRef} />
          </div>
        )}

        {user ? (
          <>
            <Separator className="my-2" />
            <form
              onSubmit={(e) => {
                e.preventDefault();
                handleSubmitComment();
              }}
              className="space-y-1"
              aria-label={t('matches.comments.addCommentForm', 'Add comment form')}
            >
              <div className="flex gap-2">
                <Avatar className="h-6 w-6 flex-shrink-0">
                  <AvatarImage
                    src={user.user_metadata?.avatar_url}
                    alt="Your avatar"
                  />
                  <AvatarFallback>
                    {user.user_metadata?.full_name
                      ? (user.user_metadata.full_name as string).charAt(0).toUpperCase()
                      : user.email?.charAt(0).toUpperCase() || "?"}
                  </AvatarFallback>
                </Avatar>
                <div className="flex-1">
                  <Textarea
                    ref={textareaRef}
                    placeholder={t('matches.comments.addCommentPlaceholder', 'Add a comment... (Use @ to mention players)')}
                    value={newComment}
                    onChange={(e) => {
                      setNewComment(e.target.value);
                      if (error) setError(null);
                    }}
                    onKeyDown={handleKeyDown}
                    className={`min-h-[60px] text-xs ${error ? 'border-red-500 focus-visible:ring-red-500' : ''}`}
                    aria-label="Comment text"
                    aria-invalid={error ? "true" : "false"}
                    aria-describedby={error ? "comment-error" : "comment-tip"}
                  />
                  {error ? (
                    <div id="comment-error" className="text-xs text-red-500 mt-0.5 flex items-center gap-1">
                      <AlertCircle className="h-3 w-3" aria-hidden="true" />
                      {error}
                    </div>
                  ) : (
                    <div id="comment-tip" className="text-xs text-muted-foreground mt-0.5">
                      {t('matches.comments.tip', 'Tip: Type @ to mention players. Press Ctrl+Enter to submit.')}
                    </div>
                  )}
                </div>
              </div>
              <div className="flex justify-end">
                <Button
                  type="submit"
                  disabled={submitting}
                  aria-busy={submitting}
                  size="sm"
                  className="h-7 text-xs"
                >
                  {submitting && <Loader2 className="mr-1 h-3 w-3 animate-spin" aria-hidden="true" />}
                  <Send className="mr-1 h-3 w-3" aria-hidden="true" />
                  {submitting ? t('matches.comments.posting', 'Posting...') : t('matches.comments.postComment', 'Post Comment')}
                </Button>
              </div>
            </form>
          </>
        ) : (
          <div className="text-center py-2 text-muted-foreground text-xs" role="alert">
            <p>{t('matches.comments.loginToComment', 'Please log in to comment')}</p>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
