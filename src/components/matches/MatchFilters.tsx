import { useState } from "react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { Calendar } from "@/components/ui/calendar";
import { Checkbox } from "@/components/ui/checkbox";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";
import { format } from "date-fns";
import { CalendarIcon, Filter, X, Check } from "lucide-react";
import { cn } from "@/lib/utils";
import { useTranslation } from "react-i18next";

export interface Player {
  id: number;
  name: string;
}

export interface MatchFilters {
  search: string;
  dateRange: {
    from: Date | undefined;
    to: Date | undefined;
  };
  players: number[];
  resultFilter: 'all' | 'wins' | 'losses' | 'draws';
  sortBy: 'date-desc' | 'date-asc' | 'score-desc' | 'score-asc';
}

interface MatchFiltersProps {
  filters: MatchFilters;
  onFiltersChange: (filters: MatchFilters) => void;
  players: Player[];
  onReset: () => void;
}

export const MatchFiltersComponent = ({
  filters,
  onFiltersChange,
  players,
  onReset
}: MatchFiltersProps) => {
  const { t } = useTranslation();
  const [isCalendarOpen, setIsCalendarOpen] = useState(false);
  const [isPlayersOpen, setIsPlayersOpen] = useState(false);

  const updateFilters = (partialFilters: Partial<MatchFilters>) => {
    onFiltersChange({ ...filters, ...partialFilters });
  };

  const handlePlayerToggle = (playerId: number) => {
    const newPlayers = filters.players.includes(playerId)
      ? filters.players.filter(id => id !== playerId)
      : [...filters.players, playerId];

    updateFilters({ players: newPlayers });
  };

  const getActiveFiltersCount = () => {
    let count = 0;
    if (filters.search) count++;
    if (filters.dateRange.from || filters.dateRange.to) count++;
    if (filters.players.length > 0) count++;
    if (filters.resultFilter !== 'all') count++;
    if (filters.sortBy !== 'date-desc') count++;
    return count;
  };

  const activeFiltersCount = getActiveFiltersCount();

  return (
    <div className="space-y-4 mb-6">
      <div className="flex flex-col sm:flex-row gap-2 items-start sm:items-center">
        <Input
          placeholder={t('matches.searchMatches', 'Search matches...')}
          value={filters.search}
          onChange={(e) => updateFilters({ search: e.target.value })}
          className="w-full sm:w-auto sm:flex-1"
        />

        <div className="flex flex-wrap gap-2">
          {/* Date Range Filter */}
          <Popover open={isCalendarOpen} onOpenChange={setIsCalendarOpen}>
            <PopoverTrigger asChild>
              <Button variant="outline" className="justify-start">
                <CalendarIcon className="mr-2 h-4 w-4" />
                {filters.dateRange.from || filters.dateRange.to ? (
                  <span>
                    {filters.dateRange.from
                      ? format(filters.dateRange.from, "MMM d, yyyy")
                      : t('matches.start', 'Start')}
                    {" - "}
                    {filters.dateRange.to
                      ? format(filters.dateRange.to, "MMM d, yyyy")
                      : t('matches.end', 'End')}
                  </span>
                ) : (
                  <span>{t('matches.dateRange', 'Date Range')}</span>
                )}
              </Button>
            </PopoverTrigger>
            <PopoverContent className="w-auto p-0" align="start">
              <Calendar
                initialFocus
                mode="range"
                selected={{
                  from: filters.dateRange.from,
                  to: filters.dateRange.to,
                }}
                onSelect={(range) => {
                  updateFilters({
                    dateRange: {
                      from: range?.from,
                      to: range?.to,
                    },
                  });
                  setIsCalendarOpen(false);
                }}
              />
              <div className="p-3 border-t border-border flex justify-between">
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => {
                    updateFilters({
                      dateRange: { from: undefined, to: undefined },
                    });
                  }}
                >
                  {t('matches.clear', 'Clear')}
                </Button>
                <Button
                  size="sm"
                  onClick={() => setIsCalendarOpen(false)}
                >
                  {t('matches.apply', 'Apply')}
                </Button>
              </div>
            </PopoverContent>
          </Popover>

          {/* Players Filter */}
          <Popover open={isPlayersOpen} onOpenChange={setIsPlayersOpen}>
            <PopoverTrigger asChild>
              <Button variant="outline">
                <Filter className="mr-2 h-4 w-4" />
                {t('common.players', 'Players')}
                {filters.players.length > 0 && (
                  <Badge variant="secondary" className="ml-2">
                    {filters.players.length}
                  </Badge>
                )}
              </Button>
            </PopoverTrigger>
            <PopoverContent className="w-[220px] p-0" align="start">
              <div className="p-3 border-b border-border">
                <Input
                  placeholder={t('matches.searchPlayers', 'Search players...')}
                  className="h-8"
                  onChange={(e) => {
                    // This would filter the displayed players list
                    // Implementation depends on how you want to handle this
                  }}
                />
              </div>
              <div className="py-2 max-h-[200px] overflow-y-auto">
                {players.map((player) => (
                  <div
                    key={player.id}
                    className="flex items-center space-x-2 px-3 py-1.5 hover:bg-accent cursor-pointer"
                    onClick={() => handlePlayerToggle(player.id)}
                  >
                    <Checkbox
                      id={`player-${player.id}`}
                      checked={filters.players.includes(player.id)}
                      onCheckedChange={() => handlePlayerToggle(player.id)}
                    />
                    <Label
                      htmlFor={`player-${player.id}`}
                      className="flex-grow cursor-pointer"
                    >
                      {player.name}
                    </Label>
                  </div>
                ))}
              </div>
              <div className="p-3 border-t border-border flex justify-between">
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => updateFilters({ players: [] })}
                >
                  {t('matches.clear', 'Clear')}
                </Button>
                <Button
                  size="sm"
                  onClick={() => setIsPlayersOpen(false)}
                >
                  {t('matches.apply', 'Apply')}
                </Button>
              </div>
            </PopoverContent>
          </Popover>

          {/* Result Filter */}
          <Select
            value={filters.resultFilter}
            onValueChange={(value: any) => updateFilters({ resultFilter: value })}
          >
            <SelectTrigger className="w-[130px]">
              <SelectValue placeholder={t('matches.result', 'Result')} />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">{t('matches.allResults', 'All Results')}</SelectItem>
              <SelectItem value="wins">{t('matches.wins', 'Wins')}</SelectItem>
              <SelectItem value="losses">{t('matches.losses', 'Losses')}</SelectItem>
              <SelectItem value="draws">{t('matches.draws', 'Draws')}</SelectItem>
            </SelectContent>
          </Select>

          {/* Sort By */}
          <Select
            value={filters.sortBy}
            onValueChange={(value: any) => updateFilters({ sortBy: value })}
          >
            <SelectTrigger className="w-[150px]">
              <SelectValue placeholder={t('matches.sortBy', 'Sort by')} />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="date-desc">{t('matches.newestFirst', 'Newest First')}</SelectItem>
              <SelectItem value="date-asc">{t('matches.oldestFirst', 'Oldest First')}</SelectItem>
              <SelectItem value="score-desc">{t('matches.highestScore', 'Highest Score')}</SelectItem>
              <SelectItem value="score-asc">{t('matches.lowestScore', 'Lowest Score')}</SelectItem>
            </SelectContent>
          </Select>

          {/* Reset Filters */}
          {activeFiltersCount > 0 && (
            <Button
              variant="ghost"
              size="icon"
              onClick={onReset}
              className="h-10 w-10"
              title={t('matches.resetFilters', 'Reset filters')}
            >
              <X className="h-4 w-4" />
            </Button>
          )}
        </div>
      </div>

      {/* Active Filters Display */}
      {activeFiltersCount > 0 && (
        <div className="flex flex-wrap gap-2">
          {filters.search && (
            <Badge variant="outline" className="flex items-center gap-1">
              {t('common.search', 'Search')}: {filters.search}
              <X
                className="h-3 w-3 cursor-pointer"
                onClick={() => updateFilters({ search: "" })}
              />
            </Badge>
          )}

          {(filters.dateRange.from || filters.dateRange.to) && (
            <Badge variant="outline" className="flex items-center gap-1">
              {t('matches.date', 'Date')}: {filters.dateRange.from
                ? format(filters.dateRange.from, "MMM d")
                : t('matches.start', 'Start')}
              {" - "}
              {filters.dateRange.to
                ? format(filters.dateRange.to, "MMM d")
                : t('matches.end', 'End')}
              <X
                className="h-3 w-3 cursor-pointer"
                onClick={() => updateFilters({ dateRange: { from: undefined, to: undefined } })}
              />
            </Badge>
          )}

          {filters.players.length > 0 && (
            <Badge variant="outline" className="flex items-center gap-1">
              {t('common.players', 'Players')}: {filters.players.length}
              <X
                className="h-3 w-3 cursor-pointer"
                onClick={() => updateFilters({ players: [] })}
              />
            </Badge>
          )}

          {filters.resultFilter !== 'all' && (
            <Badge variant="outline" className="flex items-center gap-1">
              {t('matches.result', 'Result')}: {t(`matches.${filters.resultFilter}`, filters.resultFilter.charAt(0).toUpperCase() + filters.resultFilter.slice(1))}
              <X
                className="h-3 w-3 cursor-pointer"
                onClick={() => updateFilters({ resultFilter: 'all' })}
              />
            </Badge>
          )}

          {filters.sortBy !== 'date-desc' && (
            <Badge variant="outline" className="flex items-center gap-1">
              {t('common.sort', 'Sort')}: {
                filters.sortBy === 'date-asc' ? t('matches.oldestFirst', 'Oldest First') :
                filters.sortBy === 'score-desc' ? t('matches.highestScore', 'Highest Score') :
                filters.sortBy === 'score-asc' ? t('matches.lowestScore', 'Lowest Score') : t('matches.newestFirst', 'Newest First')
              }
              <X
                className="h-3 w-3 cursor-pointer"
                onClick={() => updateFilters({ sortBy: 'date-desc' })}
              />
            </Badge>
          )}
        </div>
      )}
    </div>
  );
};
