import React, { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>le } from "@/components/ui/card";
import { X, ArrowRight, CheckCircle } from "lucide-react";
import { useMediaQuery } from "@/hooks/use-media-query";

export interface OnboardingStep {
  title: string;
  content: React.ReactNode;
  target?: string; // CSS selector for the element to highlight
  placement?: "top" | "right" | "bottom" | "left";
}

interface OnboardingTourProps {
  steps: OnboardingStep[];
  onComplete: () => void;
  onSkip?: () => void;
  isOpen: boolean;
}

export function OnboardingTour({ steps, onComplete, onSkip, isOpen }: OnboardingTourProps) {
  const [currentStep, setCurrentStep] = useState(0);
  const [targetElement, setTargetElement] = useState<HTMLElement | null>(null);
  const [position, setPosition] = useState({ top: 0, left: 0 });
  const isMobile = useMediaQuery("(max-width: 768px)");
  const preferReducedMotion = useMediaQuery("(prefers-reduced-motion: reduce)");

  // Find and position the tour relative to the target element
  useEffect(() => {
    if (!isOpen) return;

    const step = steps[currentStep];
    if (!step.target) {
      setTargetElement(null);
      return;
    }

    const element = document.querySelector(step.target) as HTMLElement;
    if (element) {
      setTargetElement(element);
      positionTooltip(element, step.placement || "bottom");
    } else {
      setTargetElement(null);
    }
  }, [currentStep, steps, isOpen]);

  // Reposition on window resize
  useEffect(() => {
    if (!isOpen || !targetElement) return;

    const handleResize = () => {
      positionTooltip(
        targetElement,
        steps[currentStep].placement || "bottom"
      );
    };

    window.addEventListener("resize", handleResize);
    return () => window.removeEventListener("resize", handleResize);
  }, [targetElement, currentStep, steps, isOpen]);

  // Position the tooltip relative to the target element
  const positionTooltip = (element: HTMLElement, placement: string) => {
    const rect = element.getBoundingClientRect();
    const tooltipWidth = 320; // Approximate width of our tooltip
    const tooltipHeight = 200; // Approximate height of our tooltip
    const margin = 12; // Margin between target and tooltip

    let top = 0;
    let left = 0;

    switch (placement) {
      case "top":
        top = rect.top - tooltipHeight - margin + window.scrollY;
        left = rect.left + rect.width / 2 - tooltipWidth / 2 + window.scrollX;
        break;
      case "right":
        top = rect.top + rect.height / 2 - tooltipHeight / 2 + window.scrollY;
        left = rect.right + margin + window.scrollX;
        break;
      case "bottom":
        top = rect.bottom + margin + window.scrollY;
        left = rect.left + rect.width / 2 - tooltipWidth / 2 + window.scrollX;
        break;
      case "left":
        top = rect.top + rect.height / 2 - tooltipHeight / 2 + window.scrollY;
        left = rect.left - tooltipWidth - margin + window.scrollX;
        break;
    }

    // Adjust if tooltip would go off screen
    const viewportWidth = window.innerWidth;
    const viewportHeight = window.innerHeight;

    if (left < 0) left = margin;
    if (left + tooltipWidth > viewportWidth) left = viewportWidth - tooltipWidth - margin;
    if (top < 0) top = margin;
    if (top + tooltipHeight > viewportHeight + window.scrollY)
      top = viewportHeight + window.scrollY - tooltipHeight - margin;

    setPosition({ top, left });
  };

  const handleNext = () => {
    if (currentStep < steps.length - 1) {
      setCurrentStep(currentStep + 1);
    } else {
      onComplete();
    }
  };

  const handlePrevious = () => {
    if (currentStep > 0) {
      setCurrentStep(currentStep - 1);
    }
  };

  if (!isOpen) return null;

  // For mobile, show a full-screen overlay
  if (isMobile) {
    return (
      <div
        className="fixed inset-0 bg-black/50 z-50 flex items-center justify-center p-4"
        role="dialog"
        aria-modal="true"
        aria-labelledby="onboarding-title"
      >
        <Card className="w-full max-w-md">
          <CardHeader>
            <CardTitle id="onboarding-title" className="flex items-center justify-between">
              {steps[currentStep].title}
              <Button
                variant="ghost"
                size="icon"
                onClick={onSkip}
                aria-label="Close onboarding tour"
              >
                <X className="h-4 w-4" />
              </Button>
            </CardTitle>
          </CardHeader>
          <CardContent>{steps[currentStep].content}</CardContent>
          <CardFooter className="flex justify-between">
            <div>
              {currentStep > 0 && (
                <Button variant="outline" onClick={handlePrevious}>
                  Back
                </Button>
              )}
            </div>
            <div className="flex gap-2">
              {onSkip && (
                <Button variant="ghost" onClick={onSkip}>
                  Skip
                </Button>
              )}
              <Button onClick={handleNext}>
                {currentStep < steps.length - 1 ? (
                  <>
                    Next <ArrowRight className="ml-2 h-4 w-4" />
                  </>
                ) : (
                  <>
                    Finish <CheckCircle className="ml-2 h-4 w-4" />
                  </>
                )}
              </Button>
            </div>
          </CardFooter>
        </Card>
      </div>
    );
  }

  // For desktop, show a positioned tooltip
  return (
    <>
      {/* Overlay for the entire page */}
      <div
        className="fixed inset-0 bg-black/30 z-40"
        aria-hidden="true"
        onClick={onSkip}
      />

      {/* Highlight for the target element */}
      {targetElement && (
        <div
          className="fixed z-50 pointer-events-none"
          style={{
            top: targetElement.getBoundingClientRect().top + window.scrollY,
            left: targetElement.getBoundingClientRect().left + window.scrollX,
            width: targetElement.offsetWidth,
            height: targetElement.offsetHeight,
            boxShadow: "0 0 0 9999px rgba(0, 0, 0, 0.5)",
            borderRadius: "4px",
            animation: preferReducedMotion ? "none" : "pulse 2s infinite",
          }}
        />
      )}

      {/* Tooltip */}
      <div
        className="fixed z-50 w-80"
        style={{
          top: `${position.top}px`,
          left: `${position.left}px`,
          transition: preferReducedMotion ? "none" : "all 0.3s ease",
        }}
        role="dialog"
        aria-modal="true"
        aria-labelledby="onboarding-step-title"
      >
        <Card>
          <CardHeader>
            <CardTitle id="onboarding-step-title" className="text-lg">
              {steps[currentStep].title}
            </CardTitle>
          </CardHeader>
          <CardContent>{steps[currentStep].content}</CardContent>
          <CardFooter className="flex justify-between">
            <div>
              {currentStep > 0 && (
                <Button variant="outline" onClick={handlePrevious} size="sm">
                  Back
                </Button>
              )}
            </div>
            <div className="flex gap-2">
              {onSkip && (
                <Button variant="ghost" onClick={onSkip} size="sm">
                  Skip
                </Button>
              )}
              <Button onClick={handleNext} size="sm">
                {currentStep < steps.length - 1 ? (
                  <>
                    Next <ArrowRight className="ml-1 h-3 w-3" />
                  </>
                ) : (
                  <>
                    Finish <CheckCircle className="ml-1 h-3 w-3" />
                  </>
                )}
              </Button>
            </div>
          </CardFooter>
        </Card>
      </div>
    </>
  );
}
