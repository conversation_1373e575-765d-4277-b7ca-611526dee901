import { useState } from "react";
import { Ava<PERSON>, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Button } from "@/components/ui/button";
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, <PERSON>alogTitle, DialogTrigger } from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Loader2, Upload, User, X } from "lucide-react";
import { supabase } from "@/lib/supabase";
import { useToast } from "@/hooks/use-toast";

interface PlayerAvatarProps {
  playerId: number;
  playerName: string;
  avatarUrl?: string | null;
  size?: "sm" | "md" | "lg";
  onAvatarChange?: (url: string) => void;
  editable?: boolean;
}

export function PlayerAvatar({
  playerId,
  playerName,
  avatarUrl,
  size = "md",
  onAvatarChange,
  editable = false,
}: PlayerAvatarProps) {
  const { toast } = useToast();
  const [isUploading, setIsUploading] = useState(false);
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [imageFile, setImageFile] = useState<File | null>(null);
  const [previewUrl, setPreviewUrl] = useState<string | null>(null);

  // Get size class
  const getSizeClass = () => {
    switch (size) {
      case "sm":
        return "h-8 w-8";
      case "lg":
        return "h-16 w-16";
      case "md":
      default:
        return "h-12 w-12";
    }
  };

  // Get initials from player name
  const getInitials = () => {
    if (!playerName) return "?";
    const nameParts = playerName.split(" ");
    if (nameParts.length === 1) return nameParts[0].charAt(0).toUpperCase();
    return (nameParts[0].charAt(0) + nameParts[nameParts.length - 1].charAt(0)).toUpperCase();
  };

  // Handle file selection
  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file) return;

    // Check file type
    if (!file.type.startsWith("image/")) {
      toast({
        title: "Invalid file type",
        description: "Please select an image file",
        variant: "destructive",
      });
      return;
    }

    // Check file size (max 2MB)
    if (file.size > 2 * 1024 * 1024) {
      toast({
        title: "File too large",
        description: "Please select an image smaller than 2MB",
        variant: "destructive",
      });
      return;
    }

    setImageFile(file);
    setPreviewUrl(URL.createObjectURL(file));
  };

  // Clear selected file
  const handleClearFile = () => {
    if (previewUrl) URL.revokeObjectURL(previewUrl);
    setImageFile(null);
    setPreviewUrl(null);
  };

  // Upload avatar to Supabase Storage
  const handleUpload = async () => {
    if (!imageFile) return;

    setIsUploading(true);
    try {
      // Upload to Supabase Storage
      const filePath = `player-avatars/${playerId}_${Date.now()}`;
      const { data, error } = await supabase.storage
        .from("avatars")
        .upload(filePath, imageFile, {
          cacheControl: "3600",
          upsert: true,
          contentType: imageFile.type,
        });

      if (error) throw error;

      // Get public URL
      const { data: urlData } = supabase.storage
        .from("avatars")
        .getPublicUrl(data.path);

      // Update player record with avatar URL
      const { error: updateError } = await supabase
        .from("players")
        .update({ avatar_url: urlData.publicUrl })
        .eq("id", playerId);

      if (updateError) throw updateError;

      // Call callback with new URL
      if (onAvatarChange) onAvatarChange(urlData.publicUrl);

      toast({
        title: "Avatar updated",
        description: "Your profile picture has been updated successfully",
      });

      setIsDialogOpen(false);
    } catch (error) {
      console.error("Error uploading avatar:", error);
      toast({
        title: "Upload failed",
        description: "Failed to upload avatar. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsUploading(false);
      handleClearFile();
    }
  };

  return (
    <>
      <Avatar className={getSizeClass()}>
        <AvatarImage src={avatarUrl || undefined} alt={playerName} />
        <AvatarFallback className="bg-primary/10">
          {getInitials()}
        </AvatarFallback>
      </Avatar>

      {editable && (
        <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
          <DialogTrigger asChild>
            <Button
              variant="ghost"
              size="sm"
              className="absolute bottom-0 right-0 h-6 w-6 rounded-full bg-background shadow-sm p-0"
            >
              <Upload className="h-3 w-3" />
              <span className="sr-only">Change avatar</span>
            </Button>
          </DialogTrigger>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Update Profile Picture</DialogTitle>
              <DialogDescription>
                Upload a new avatar for {playerName}
              </DialogDescription>
            </DialogHeader>

            <div className="space-y-4 py-4">
              <div className="flex justify-center">
                <Avatar className="h-24 w-24">
                  {previewUrl ? (
                    <AvatarImage src={previewUrl} alt="Preview" />
                  ) : (
                    <>
                      <AvatarImage src={avatarUrl || undefined} alt={playerName} />
                      <AvatarFallback className="bg-primary/10 text-xl">
                        {getInitials()}
                      </AvatarFallback>
                    </>
                  )}
                </Avatar>
              </div>

              {previewUrl ? (
                <div className="flex justify-center">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={handleClearFile}
                    disabled={isUploading}
                  >
                    <X className="h-4 w-4 mr-2" />
                    Clear
                  </Button>
                </div>
              ) : (
                <div className="grid w-full max-w-sm items-center gap-1.5 mx-auto">
                  <Label htmlFor="avatar">Select Image</Label>
                  <Input
                    id="avatar"
                    type="file"
                    accept="image/*"
                    onChange={handleFileChange}
                    disabled={isUploading}
                  />
                  <p className="text-xs text-muted-foreground">
                    JPG, PNG or GIF. Max 2MB.
                  </p>
                </div>
              )}
            </div>

            <DialogFooter>
              <Button
                variant="outline"
                onClick={() => setIsDialogOpen(false)}
                disabled={isUploading}
              >
                Cancel
              </Button>
              <Button
                onClick={handleUpload}
                disabled={!imageFile || isUploading}
              >
                {isUploading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                {isUploading ? "Uploading..." : "Upload"}
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      )}
    </>
  );
}
