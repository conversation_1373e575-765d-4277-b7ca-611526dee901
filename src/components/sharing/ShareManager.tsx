import React, { useState, useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { Badge } from '@/components/ui/badge';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
  DialogFooter,
  DialogDescription,
} from '@/components/ui/dialog';

import {
  SafeDialog,
  SafeDialogContent,
  SafeDialogHeader,
  SafeDialogTitle,
  SafeDialogTrigger,
  SafeDialogFooter,
  SafeDialogDescription,
} from '@/components/ui/safe-dialog';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { Calendar } from '@/components/ui/calendar';
import { format, addDays, addWeeks, addMonths } from 'date-fns';
import { useToast } from '@/hooks/use-toast';
import { supabase } from '@/lib/supabase';
import { fetchSharedLinks, createSharedLink, updateSharedLink, deleteSharedLink, refreshSharedLink } from '@/lib/api/sharing';
import { useAuth } from '@/context/AuthContext';
import { useOffline } from '@/context/OfflineContext';
import { getCache, setCache, CACHE_KEYS, CACHE_EXPIRY } from '@/lib/cache/storage';
import { cn } from '@/lib/utils';
import {
  Loader2,
  Share2,
  Link,
  Copy,
  Calendar as CalendarIcon,
  Trash2,
  Check,
  X,
  Clock,
  Eye,
  Settings,
  RefreshCw,
  Download,
  Plus,
  MessageSquare,
} from 'lucide-react';

// Import types from the sharing types file
import { SharedLink, AccessLevel, CreateSharedLinkParams, UpdateSharedLinkParams } from '@/types/sharing';

interface ShareManagerProps {
  groupId: string;
  groupName: string;
}

export function ShareManager({ groupId, groupName }: ShareManagerProps) {
  const { toast } = useToast();
  const { user } = useAuth();
  const { isOffline } = useOffline();
  const [isLoading, setIsLoading] = useState(true);
  const [sharedLinks, setSharedLinks] = useState<SharedLink[]>([]);
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
  const [activeTab, setActiveTab] = useState('links');
  const [newLinkName, setNewLinkName] = useState('');
  const [accessLevel, setAccessLevel] = useState<AccessLevel>('read');
  const [expiryDate, setExpiryDate] = useState<Date | undefined>(undefined);
  const [expiryType, setExpiryType] = useState<'never' | 'custom' | 'preset'>('never');
  const [expiryPreset, setExpiryPreset] = useState<'1day' | '1week' | '1month'>('1week');
  const [isCalendarOpen, setIsCalendarOpen] = useState(false);
  const [copiedLinkId, setCopiedLinkId] = useState<string | null>(null);

  // Fetch shared links
  useEffect(() => {
    if (!groupId) return;

    const fetchSharedLinks = async () => {
      setIsLoading(true);
      try {
        // Try to get from cache first
        const cacheKey = `${CACHE_KEYS.SHARED_LINKS}_${groupId}`;
        const cachedLinks = getCache<SharedLink[]>(cacheKey);

        if (cachedLinks) {
          setSharedLinks(cachedLinks);

          // If we're online, still fetch from server but don't block UI
          if (!isOffline) {
            fetchLinksFromServer();
          } else {
            setIsLoading(false);
          }
        } else {
          // No cache, must fetch from server
          await fetchLinksFromServer();
        }
      } catch (error) {
        console.error('Error fetching shared links:', error);
        setIsLoading(false);
      }
    };

    fetchSharedLinks();
  }, [groupId, isOffline]);

  // Fetch shared links from server
  const fetchLinksFromServer = async () => {
    try {
      // Use the fetchSharedLinks function from the API
      const links = await fetchSharedLinks(groupId);

      setSharedLinks(links);

      // Update cache
      const cacheKey = `${CACHE_KEYS.SHARED_LINKS}_${groupId}`;
      setCache(cacheKey, links, CACHE_EXPIRY.MEDIUM);

      setIsLoading(false);
    } catch (error) {
      console.error('Error fetching shared links from server:', error);
      toast({
        title: 'Error',
        description: 'Failed to load shared links',
        variant: 'destructive',
      });
      setSharedLinks([]);
      setIsLoading(false);
    }
  };

  // Create a new shared link
  const handleCreateLink = async () => {
    if (!newLinkName.trim() || !user) {
      toast({
        title: 'Error',
        description: 'Link name cannot be empty',
        variant: 'destructive',
      });
      return;
    }

    try {
      // Calculate expiry date
      let calculatedExpiryDate: Date | null = null;

      if (expiryType === 'custom' && expiryDate) {
        calculatedExpiryDate = expiryDate;
      } else if (expiryType === 'preset') {
        const now = new Date();
        if (expiryPreset === '1day') {
          calculatedExpiryDate = addDays(now, 1);
        } else if (expiryPreset === '1week') {
          calculatedExpiryDate = addWeeks(now, 1);
        } else if (expiryPreset === '1month') {
          calculatedExpiryDate = addMonths(now, 1);
        }
      }

      // If offline, show message
      if (isOffline) {
        toast({
          title: 'Offline Mode',
          description: 'Shared links cannot be created while offline',
          variant: 'destructive',
        });
        return;
      }

      const newLink: CreateSharedLinkParams = {
        group_id: groupId,
        created_by: user.id,
        name: newLinkName.trim(),
        access_level: accessLevel,
        expires_at: calculatedExpiryDate ? calculatedExpiryDate.toISOString() : null,
        is_active: true,
      };

      // Use the createSharedLink function from the API
      const data = await createSharedLink(newLink);

      if (!data) {
        toast({
          title: 'Feature Unavailable',
          description: 'The sharing feature is currently being set up. Please try again later.',
          variant: 'destructive',
        });
        setIsCreateDialogOpen(false);
        return;
      }

      // Update local state
      setSharedLinks(prev => [data, ...prev]);

      // Update cache
      const cacheKey = `${CACHE_KEYS.SHARED_LINKS}_${groupId}`;
      setCache(cacheKey, [data, ...sharedLinks], CACHE_EXPIRY.MEDIUM);

      // Reset form
      setNewLinkName('');
      setAccessLevel('read');
      setExpiryDate(undefined);
      setExpiryType('never');
      setExpiryPreset('1week');
      setIsCreateDialogOpen(false);

      toast({
        title: 'Success',
        description: 'Shared link created successfully',
      });
    } catch (error) {
      console.error('Error creating shared link:', error);

      // Check for table not existing error
      if ((error as any)?.code === '42P01' || (error as any)?.message?.includes('does not exist')) {
        toast({
          title: 'Feature Unavailable',
          description: 'The sharing feature is currently being set up. Please try again later.',
          variant: 'destructive',
        });
      } else {
        toast({
          title: 'Error',
          description: 'Failed to create shared link',
          variant: 'destructive',
        });
      }
      setIsCreateDialogOpen(false);
    }
  };

  // Delete a shared link
  const handleDeleteLink = async (linkId: string) => {
    try {
      // If offline, show message
      if (isOffline) {
        toast({
          title: 'Offline Mode',
          description: 'Shared links cannot be deleted while offline',
          variant: 'destructive',
        });
        return;
      }

      // Use the deleteSharedLink function from the API
      const success = await deleteSharedLink(linkId);

      if (!success) {
        toast({
          title: 'Feature Unavailable',
          description: 'The sharing feature is currently being set up. Please try again later.',
          variant: 'destructive',
        });
        return;
      }

      // Update local state
      setSharedLinks(prev => prev.filter(link => link.id !== linkId));

      // Update cache
      const cacheKey = `${CACHE_KEYS.SHARED_LINKS}_${groupId}`;
      setCache(
        cacheKey,
        sharedLinks.filter(link => link.id !== linkId),
        CACHE_EXPIRY.MEDIUM
      );

      toast({
        title: 'Success',
        description: 'Shared link deleted successfully',
      });
    } catch (error) {
      console.error('Error deleting shared link:', error);

      // Check for table not existing error
      if ((error as any)?.code === '42P01' || (error as any)?.message?.includes('does not exist')) {
        toast({
          title: 'Feature Unavailable',
          description: 'The sharing feature is currently being set up. Please try again later.',
          variant: 'destructive',
        });
      } else {
        toast({
          title: 'Error',
          description: 'Failed to delete shared link',
          variant: 'destructive',
        });
      }
    }
  };

  // Toggle link active status
  const handleToggleLinkActive = async (linkId: string, isActive: boolean) => {
    try {
      // If offline, show message
      if (isOffline) {
        toast({
          title: 'Offline Mode',
          description: 'Shared links cannot be updated while offline',
          variant: 'destructive',
        });
        return;
      }

      // Use the updateSharedLink function from the API
      const data = await updateSharedLink(linkId, { is_active: isActive });

      if (!data) {
        toast({
          title: 'Feature Unavailable',
          description: 'The sharing feature is currently being set up. Please try again later.',
          variant: 'destructive',
        });
        return;
      }

      // Update local state
      setSharedLinks(prev =>
        prev.map(link => (link.id === linkId ? data : link))
      );

      // Update cache
      const cacheKey = `${CACHE_KEYS.SHARED_LINKS}_${groupId}`;
      setCache(
        cacheKey,
        sharedLinks.map(link => (link.id === linkId ? data : link)),
        CACHE_EXPIRY.MEDIUM
      );

      toast({
        title: 'Success',
        description: `Shared link ${isActive ? 'activated' : 'deactivated'} successfully`,
      });
    } catch (error) {
      console.error('Error updating shared link:', error);

      // Check for table not existing error
      if ((error as any)?.code === '42P01' || (error as any)?.message?.includes('does not exist')) {
        toast({
          title: 'Feature Unavailable',
          description: 'The sharing feature is currently being set up. Please try again later.',
          variant: 'destructive',
        });
      } else {
        toast({
          title: 'Error',
          description: 'Failed to update shared link',
          variant: 'destructive',
        });
      }
    }
  };

  // Refresh a shared link (generate new ID)
  const handleRefreshLink = async (linkId: string) => {
    try {
      // If offline, show message
      if (isOffline) {
        toast({
          title: 'Offline Mode',
          description: 'Shared links cannot be refreshed while offline',
          variant: 'destructive',
        });
        return;
      }

      // Use the refreshSharedLink function from the API
      const newLinkData = await refreshSharedLink(linkId);

      if (!newLinkData) {
        toast({
          title: 'Feature Unavailable',
          description: 'The sharing feature is currently being set up. Please try again later.',
          variant: 'destructive',
        });
        return;
      }

      // Update local state
      setSharedLinks(prev => [
        newLinkData,
        ...prev.filter(link => link.id !== linkId),
      ]);

      // Update cache
      const cacheKey = `${CACHE_KEYS.SHARED_LINKS}_${groupId}`;
      setCache(
        cacheKey,
        [newLinkData, ...sharedLinks.filter(link => link.id !== linkId)],
        CACHE_EXPIRY.MEDIUM
      );

      toast({
        title: 'Success',
        description: 'Shared link refreshed successfully',
      });
    } catch (error) {
      console.error('Error refreshing shared link:', error);

      // Check for table not existing error
      if ((error as any)?.code === '42P01' || (error as any)?.message?.includes('does not exist')) {
        toast({
          title: 'Feature Unavailable',
          description: 'The sharing feature is currently being set up. Please try again later.',
          variant: 'destructive',
        });
      } else {
        toast({
          title: 'Error',
          description: 'Failed to refresh shared link',
          variant: 'destructive',
        });
      }
    }
  };

  // Copy link to clipboard
  const handleCopyLink = (linkId: string) => {
    // Use the new URL format for shared links
    const shareUrl = `${window.location.origin}/s/${linkId}`;
    navigator.clipboard.writeText(shareUrl);

    setCopiedLinkId(linkId);
    setTimeout(() => setCopiedLinkId(null), 2000);

    toast({
      title: 'Link Copied',
      description: 'Shared link copied to clipboard',
    });
  };

  // Format expiry date
  const formatExpiryDate = (expiryDate: string | null) => {
    if (!expiryDate) return 'Never expires';

    const date = new Date(expiryDate);
    return format(date, 'PPP');
  };

  // Check if link is expired
  const isLinkExpired = (expiryDate: string | null) => {
    if (!expiryDate) return false;

    const date = new Date(expiryDate);
    return date < new Date();
  };

  // Handle expiry type change
  const handleExpiryTypeChange = (value: string) => {
    setExpiryType(value as 'never' | 'custom' | 'preset');

    if (value === 'never') {
      setExpiryDate(undefined);
    } else if (value === 'preset') {
      // Set default preset
      const now = new Date();
      if (expiryPreset === '1day') {
        setExpiryDate(addDays(now, 1));
      } else if (expiryPreset === '1week') {
        setExpiryDate(addWeeks(now, 1));
      } else if (expiryPreset === '1month') {
        setExpiryDate(addMonths(now, 1));
      }
    }
  };

  // Handle preset change
  const handlePresetChange = (value: string) => {
    setExpiryPreset(value as '1day' | '1week' | '1month');

    const now = new Date();
    if (value === '1day') {
      setExpiryDate(addDays(now, 1));
    } else if (value === '1week') {
      setExpiryDate(addWeeks(now, 1));
    } else if (value === '1month') {
      setExpiryDate(addMonths(now, 1));
    }
  };



  return (
    <>
      <Button
        variant="outline"
        size="sm"
        onClick={() => setIsDialogOpen(true)}
        className="flex items-center gap-1"
      >
        <Share2 className="h-4 w-4 mr-1" />
        Share
      </Button>

      <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
        <DialogContent className="sm:max-w-2xl">
          <DialogHeader>
            <DialogTitle>Share Group Data</DialogTitle>
            <DialogDescription>
              Create and manage shared links for "{groupName}".
            </DialogDescription>
          </DialogHeader>

            <div className="space-y-4 mt-4">
              <div className="flex justify-between items-center">
                <h3 className="text-sm font-medium">Active Shared Links</h3>
                <SafeDialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
                  <SafeDialogTrigger asChild>
                    <Button size="sm">
                      <Plus className="h-4 w-4 mr-1" />
                      Create Link
                    </Button>
                  </SafeDialogTrigger>
                  <SafeDialogContent className="sm:max-w-md">
                    <SafeDialogHeader>
                      <SafeDialogTitle>Create Shared Link</SafeDialogTitle>
                      <SafeDialogDescription>
                        Create a new link to share your group data.
                      </SafeDialogDescription>
                    </SafeDialogHeader>
                    <div className="space-y-4 py-4">
                      <div className="space-y-2">
                        <Label htmlFor="linkName">Link Name</Label>
                        <Input
                          id="linkName"
                          value={newLinkName}
                          onChange={e => setNewLinkName(e.target.value)}
                          placeholder="e.g., Team Stats for July"
                        />
                      </div>

                      <div className="space-y-2">
                        <Label htmlFor="accessLevel">Access Level</Label>
                        <Select
                          value={accessLevel}
                          onValueChange={value => setAccessLevel(value as 'read' | 'comment')}
                        >
                          <SelectTrigger id="accessLevel">
                            <SelectValue placeholder="Select access level" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="read">
                              <div className="flex items-center gap-2">
                                <Eye className="h-4 w-4" />
                                <span>View Only</span>
                              </div>
                            </SelectItem>
                            <SelectItem value="comment">
                              <div className="flex items-center gap-2">
                                <MessageSquare className="h-4 w-4" />
                                <span>View & Comment</span>
                              </div>
                            </SelectItem>
                          </SelectContent>
                        </Select>
                        <p className="text-xs text-muted-foreground mt-1">
                          Comment access allows viewers to add comments to matches.
                        </p>
                      </div>

                      <div className="space-y-2">
                        <Label htmlFor="expiryType">Expiration</Label>
                        <Select
                          value={expiryType}
                          onValueChange={handleExpiryTypeChange}
                        >
                          <SelectTrigger id="expiryType">
                            <SelectValue placeholder="Select expiration type" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="never">Never Expires</SelectItem>
                            <SelectItem value="preset">Preset Duration</SelectItem>
                            <SelectItem value="custom">Custom Date</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>

                      {expiryType === 'preset' && (
                        <div className="space-y-2">
                          <Label htmlFor="expiryPreset">Duration</Label>
                          <Select
                            value={expiryPreset}
                            onValueChange={handlePresetChange}
                          >
                            <SelectTrigger id="expiryPreset">
                              <SelectValue placeholder="Select duration" />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value="1day">1 Day</SelectItem>
                              <SelectItem value="1week">1 Week</SelectItem>
                              <SelectItem value="1month">1 Month</SelectItem>
                            </SelectContent>
                          </Select>
                        </div>
                      )}

                      {expiryType === 'custom' && (
                        <div className="space-y-2">
                          <Label htmlFor="expiryDate">Expiry Date</Label>
                          <Popover open={isCalendarOpen} onOpenChange={setIsCalendarOpen}>
                            <PopoverTrigger asChild>
                              <Button
                                variant="outline"
                                className={cn(
                                  "w-full justify-start text-left font-normal",
                                  !expiryDate && "text-muted-foreground"
                                )}
                              >
                                <CalendarIcon className="mr-2 h-4 w-4" />
                                {expiryDate ? format(expiryDate, 'PPP') : "Select date"}
                              </Button>
                            </PopoverTrigger>
                            <PopoverContent className="w-auto p-0">
                              <Calendar
                                mode="single"
                                selected={expiryDate}
                                onSelect={date => {
                                  setExpiryDate(date);
                                  setIsCalendarOpen(false);
                                }}
                                initialFocus
                                disabled={date => date < new Date()}
                              />
                            </PopoverContent>
                          </Popover>
                        </div>
                      )}
                    </div>
                    <SafeDialogFooter>
                      <Button
                        variant="outline"
                        onClick={() => setIsCreateDialogOpen(false)}
                      >
                        Cancel
                      </Button>
                      <Button onClick={handleCreateLink} disabled={!newLinkName.trim()}>
                        Create Link
                      </Button>
                    </SafeDialogFooter>
                  </SafeDialogContent>
                </SafeDialog>
              </div>

              {isLoading ? (
                <div className="flex justify-center py-8">
                  <Loader2 className="h-8 w-8 animate-spin text-primary" />
                </div>
              ) : sharedLinks.length > 0 ? (
                <div className="border rounded-md">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>Name</TableHead>
                        <TableHead>Access</TableHead>
                        <TableHead>Expires</TableHead>
                        <TableHead>Status</TableHead>
                        <TableHead className="text-right">Actions</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {sharedLinks.map(link => (
                        <TableRow key={link.id}>
                          <TableCell className="font-medium">{link.name}</TableCell>
                          <TableCell>
                            {link.access_level === 'read' ? 'View Only' : 'View & Comment'}
                          </TableCell>
                          <TableCell>
                            <div className="flex items-center gap-1">
                              <Clock className="h-3.5 w-3.5 text-muted-foreground" />
                              <span
                                className={cn(
                                  isLinkExpired(link.expires_at) && "text-destructive"
                                )}
                              >
                                {formatExpiryDate(link.expires_at)}
                              </span>
                            </div>
                          </TableCell>
                          <TableCell>
                            <div className="flex items-center gap-1">
                              {link.is_active && !isLinkExpired(link.expires_at) ? (
                                <Badge variant="outline" className="bg-primary/10 text-primary border-primary/20">
                                  Active
                                </Badge>
                              ) : (
                                <Badge variant="outline" className="bg-destructive/10 text-destructive border-destructive/20">
                                  Inactive
                                </Badge>
                              )}
                            </div>
                          </TableCell>
                          <TableCell className="text-right">
                            <div className="flex items-center justify-end gap-1">
                              <Button
                                variant="ghost"
                                size="icon"
                                className="h-8 w-8"
                                onClick={() => handleCopyLink(link.id)}
                                disabled={!link.is_active || isLinkExpired(link.expires_at)}
                              >
                                {copiedLinkId === link.id ? (
                                  <Check className="h-4 w-4 text-primary" />
                                ) : (
                                  <Copy className="h-4 w-4" />
                                )}
                                <span className="sr-only">Copy Link</span>
                              </Button>
                              <Button
                                variant="ghost"
                                size="icon"
                                className="h-8 w-8"
                                onClick={() => handleToggleLinkActive(link.id, !link.is_active)}
                              >
                                {link.is_active ? (
                                  <X className="h-4 w-4 text-destructive" />
                                ) : (
                                  <Check className="h-4 w-4 text-primary" />
                                )}
                                <span className="sr-only">
                                  {link.is_active ? 'Deactivate' : 'Activate'}
                                </span>
                              </Button>
                              <Button
                                variant="ghost"
                                size="icon"
                                className="h-8 w-8"
                                onClick={() => handleRefreshLink(link.id)}
                              >
                                <RefreshCw className="h-4 w-4" />
                                <span className="sr-only">Refresh Link</span>
                              </Button>
                              <Button
                                variant="ghost"
                                size="icon"
                                className="h-8 w-8 text-destructive"
                                onClick={() => handleDeleteLink(link.id)}
                              >
                                <Trash2 className="h-4 w-4" />
                                <span className="sr-only">Delete</span>
                              </Button>
                            </div>
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </div>
              ) : (
                <Card>
                  <CardContent className="flex flex-col items-center justify-center py-8">
                    <Share2 className="h-12 w-12 text-muted-foreground opacity-20 mb-4" />
                    <h3 className="text-lg font-medium mb-1">No Shared Links</h3>
                    <p className="text-sm text-muted-foreground mb-4">
                      Create a link to share your group data with others.
                    </p>
                    <Button
                      onClick={() => setIsCreateDialogOpen(true)}
                      size="sm"
                    >
                      <Plus className="h-4 w-4 mr-1" />
                      Create First Link
                    </Button>
                  </CardContent>
                </Card>
              )}

              <div className="text-xs text-muted-foreground mt-2">
                <p>
                  Shared links allow others to view your group's data without needing an account.
                  You can deactivate or delete links at any time.
                </p>
              </div>
            </div>
        </DialogContent>
      </Dialog>
    </>
  );
}
