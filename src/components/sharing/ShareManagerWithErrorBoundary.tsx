import React from 'react';
import ErrorBoundary from '@/components/ErrorBoundary';
import { ShareManager } from './ShareManager';
import { Button } from '@/components/ui/button';
import { Share2 } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';

interface ShareManagerWithErrorBoundaryProps {
  groupId: string;
  groupName: string;
}

export function ShareManagerWithErrorBoundary({ groupId, groupName }: ShareManagerWithErrorBoundaryProps) {
  const { toast } = useToast();

  // Fallback UI when ShareManager fails
  const fallbackUI = (
    <Button
      variant="outline"
      size="sm"
      onClick={() => {
        toast({
          title: 'Feature Unavailable',
          description: 'The sharing feature is currently being set up. Please try again later.',
          variant: 'destructive',
        });
      }}
      className="flex items-center gap-1"
    >
      <Share2 className="h-4 w-4 mr-1" />
      Share
    </Button>
  );

  // Error handler
  const handleError = (error: Error) => {
    console.error('ShareManager error:', error);
    
    // Log the error to your monitoring service
    // This could be expanded to send errors to a service like Sentry
  };

  return (
    <ErrorBoundary fallback={fallbackUI} onError={handleError} maxRetries={1}>
      <ShareManager groupId={groupId} groupName={groupName} />
    </ErrorBoundary>
  );
}

export default ShareManagerWithErrorBoundary;
