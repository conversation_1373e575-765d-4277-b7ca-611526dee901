import { useState, useEffect } from "react";
import { DndProvider, useDrag, useDrop } from "react-dnd";
import { HTML5Backend } from "react-dnd-html5-backend";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Separator } from "@/components/ui/separator";
import { ArrowLeftRight, Save, User, Users } from "lucide-react";
import { Player } from "@/utils/teamGenerator";
import { TeamBalanceChart } from "./TeamBalanceChart";

// Define the drag item type
const ItemTypes = {
  PLAYER: "player",
};

interface DraggablePlayerProps {
  player: Player;
  team: "A" | "B" | "unassigned";
  onMovePlayer: (playerId: number, targetTeam: "A" | "B" | "unassigned") => void;
}

const DraggablePlayer = ({ player, team, onMovePlayer }: DraggablePlayerProps) => {
  // Set up drag
  const [{ isDragging }, drag] = useDrag(() => ({
    type: ItemTypes.PLAYER,
    item: { id: player.id, team },
    collect: (monitor) => ({
      isDragging: !!monitor.isDragging(),
    }),
  }));

  // Get background color based on team
  const getBgColor = () => {
    if (team === "A") return "bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-100";
    if (team === "B") return "bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-100";
    return "bg-gray-100 dark:bg-gray-800";
  };

  return (
    <div
      ref={drag}
      className={`${getBgColor()} p-2 mb-2 rounded-md cursor-move flex justify-between items-center ${
        isDragging ? "opacity-50" : "opacity-100"
      }`}
    >
      <div className="flex items-center gap-2">
        <User className="h-4 w-4" />
        <span>{player.name}</span>
      </div>
      <span className={`text-xs px-2 py-0.5 rounded-full ${team === "A" ? "bg-green-50 dark:bg-green-950 text-green-800 dark:text-green-100" : team === "B" ? "bg-blue-50 dark:bg-blue-950 text-blue-800 dark:text-blue-100" : "bg-background dark:bg-gray-700"}`}>
        {player.rating}
      </span>
    </div>
  );
};

interface DropZoneProps {
  team: "A" | "B" | "unassigned";
  players: Player[];
  onMovePlayer: (playerId: number, targetTeam: "A" | "B" | "unassigned") => void;
  title: string;
  icon?: React.ReactNode;
}

const DropZone = ({ team, players, onMovePlayer, title, icon }: DropZoneProps) => {
  // Set up drop
  const [{ isOver }, drop] = useDrop(() => ({
    accept: ItemTypes.PLAYER,
    drop: (item: { id: number; team: "A" | "B" | "unassigned" }) => {
      onMovePlayer(item.id, team);
    },
    collect: (monitor) => ({
      isOver: !!monitor.isOver(),
    }),
  }));

  // Get border color based on drop state
  const getBorderColor = () => {
    if (isOver) {
      if (team === "A") return "border-green-500 dark:border-green-400";
      if (team === "B") return "border-blue-500 dark:border-blue-400";
      return "border-gray-500 dark:border-gray-400";
    }
    return "border-border";
  };

  return (
    <div
      ref={drop}
      className={`border-2 ${getBorderColor()} rounded-md p-3 h-full min-h-[200px]`}
    >
      <div className="flex items-center gap-2 mb-3 font-medium">
        {icon}
        <span>{title}</span>
        <span className={`text-xs px-2 py-0.5 rounded-full ml-auto ${team === "A" ? "bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-100" : team === "B" ? "bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-100" : "bg-muted"}`}>
          {players.length} players
        </span>
      </div>
      <div className="space-y-2 max-h-[300px] overflow-y-auto pr-1">
        {players.map((player) => (
          <DraggablePlayer
            key={player.id}
            player={player}
            team={team}
            onMovePlayer={onMovePlayer}
          />
        ))}
        {players.length === 0 && (
          <div className="text-center py-8 text-muted-foreground">
            <p>Drop players here</p>
          </div>
        )}
      </div>
    </div>
  );
};

interface DraggableTeamEditorProps {
  teamA: Player[];
  teamB: Player[];
  availablePlayers: Player[];
  onTeamsChange: (teamA: Player[], teamB: Player[]) => void;
  onSaveTeams: () => void;
}

export function DraggableTeamEditor({
  teamA: initialTeamA,
  teamB: initialTeamB,
  availablePlayers,
  onTeamsChange,
  onSaveTeams,
}: DraggableTeamEditorProps) {
  // Initialize state with deep copies to avoid reference issues
  const [teams, setTeams] = useState({
    A: [...initialTeamA],
    B: [...initialTeamB],
    unassigned: availablePlayers.filter(p =>
      !initialTeamA.some(a => a.id === p.id) &&
      !initialTeamB.some(b => b.id === p.id)
    )
  });

  // Update available players when props change
  useEffect(() => {
    console.log('DEBUG: Props changed in DraggableTeamEditor');
    console.log('DEBUG: Initial Team A:', initialTeamA);
    console.log('DEBUG: Initial Team B:', initialTeamB);
    console.log('DEBUG: Available Players:', availablePlayers);
    console.log('DEBUG: Current teams state:', teams);

    // Check if initialTeamA or initialTeamB have changed
    const initialTeamAIds = new Set(initialTeamA.map(p => p.id));
    const initialTeamBIds = new Set(initialTeamB.map(p => p.id));
    const currentTeamAIds = new Set(teams.A.map(p => p.id));
    const currentTeamBIds = new Set(teams.B.map(p => p.id));

    // Check if teams have changed from outside
    const teamAChanged = initialTeamA.length !== teams.A.length ||
      initialTeamA.some(p => !currentTeamAIds.has(p.id));

    const teamBChanged = initialTeamB.length !== teams.B.length ||
      initialTeamB.some(p => !currentTeamBIds.has(p.id));

    console.log('DEBUG: Team A changed from outside:', teamAChanged);
    console.log('DEBUG: Team B changed from outside:', teamBChanged);

    // If teams have changed from outside, reset them
    if (teamAChanged || teamBChanged) {
      console.log('DEBUG: Resetting teams due to external change');
      setTeams({
        A: [...initialTeamA],
        B: [...initialTeamB],
        unassigned: availablePlayers.filter(p =>
          !initialTeamA.some(a => a.id === p.id) &&
          !initialTeamB.some(b => b.id === p.id)
        )
      });
      return;
    }

    // Create a map of all players by ID for quick lookup
    const allPlayers = new Map();

    // Add all available players to the map
    availablePlayers.forEach(player => {
      allPlayers.set(player.id, player);
    });

    // Get current player IDs in each team
    const teamAIds = new Set(teams.A.map(p => p.id));
    const teamBIds = new Set(teams.B.map(p => p.id));
    const unassignedIds = new Set(teams.unassigned.map(p => p.id));

    // Create a set of all player IDs we know about
    const allKnownIds = new Set([
      ...teamAIds,
      ...teamBIds,
      ...unassignedIds
    ]);

    // Find new players that aren't in any team yet
    const newUnassignedPlayers = availablePlayers.filter(p =>
      !allKnownIds.has(p.id) &&
      !initialTeamA.some(a => a.id === p.id) &&
      !initialTeamB.some(b => b.id === p.id)
    );

    console.log('DEBUG: New unassigned players:', newUnassignedPlayers);

    // Only update if we have new players
    if (newUnassignedPlayers.length > 0) {
      console.log('DEBUG: Adding new unassigned players');
      setTeams(prev => ({
        ...prev,
        unassigned: [...prev.unassigned, ...newUnassignedPlayers]
      }));
    }
  }, [availablePlayers, initialTeamA, initialTeamB]);

  // Handle moving a player between teams
  const handleMovePlayer = (playerId: number, targetTeam: "A" | "B" | "unassigned") => {
    console.log(`DEBUG: Moving player ${playerId} to team ${targetTeam}`);
    console.log('DEBUG: Current teams state:', JSON.stringify(teams, null, 2));

    // Find which team the player is currently in
    let sourceTeam: "A" | "B" | "unassigned" | null = null;
    let player: Player | undefined;

    if (teams.A.some(p => p.id === playerId)) {
      sourceTeam = "A";
      player = teams.A.find(p => p.id === playerId);
    } else if (teams.B.some(p => p.id === playerId)) {
      sourceTeam = "B";
      player = teams.B.find(p => p.id === playerId);
    } else if (teams.unassigned.some(p => p.id === playerId)) {
      sourceTeam = "unassigned";
      player = teams.unassigned.find(p => p.id === playerId);
    }

    console.log(`DEBUG: Player found in team ${sourceTeam}:`, player);

    // If player not found or already in target team, do nothing
    if (!player || sourceTeam === targetTeam) {
      console.log('DEBUG: Player not found or already in target team, returning');
      return;
    }

    // Create a new state object to avoid mutation
    const newTeams = {
      A: [...teams.A],
      B: [...teams.B],
      unassigned: [...teams.unassigned]
    };

    // Remove player from source team
    if (sourceTeam === "A") {
      newTeams.A = newTeams.A.filter(p => p.id !== playerId);
    } else if (sourceTeam === "B") {
      newTeams.B = newTeams.B.filter(p => p.id !== playerId);
    } else {
      newTeams.unassigned = newTeams.unassigned.filter(p => p.id !== playerId);
    }

    // Add player to target team
    if (targetTeam === "A") {
      newTeams.A.push(player);
    } else if (targetTeam === "B") {
      newTeams.B.push(player);
    } else {
      newTeams.unassigned.push(player);
    }

    console.log('DEBUG: New teams state after move:', JSON.stringify(newTeams, null, 2));

    // Update state
    setTeams(newTeams);

    // Notify parent component
    console.log('DEBUG: Notifying parent component with:', newTeams.A, newTeams.B);
    onTeamsChange(newTeams.A, newTeams.B);
  };

  // Auto-balance teams
  const handleAutoBalance = () => {
    // Combine all players
    const allPlayers = [...teams.A, ...teams.B, ...teams.unassigned];

    if (allPlayers.length === 0) return;

    // Sort by rating (highest to lowest)
    const sortedPlayers = [...allPlayers].sort((a, b) =>
      (b.rating || 0) - (a.rating || 0)
    );

    // Reset teams
    const newTeamA: Player[] = [];
    const newTeamB: Player[] = [];

    // Distribute players in snake draft order (1-2-2-1)
    sortedPlayers.forEach((player, index) => {
      if (index % 4 === 0 || index % 4 === 3) {
        newTeamA.push(player);
      } else {
        newTeamB.push(player);
      }
    });

    // Update state
    const newTeams = {
      A: newTeamA,
      B: newTeamB,
      unassigned: []
    };

    setTeams(newTeams);

    // Notify parent component
    onTeamsChange(newTeamA, newTeamB);
  };

  // Save changes
  const handleSaveChanges = () => {
    onTeamsChange(teams.A, teams.B);
    onSaveTeams();
  };

  return (
    <DndProvider backend={HTML5Backend}>
      <Card>
        <CardHeader>
          <CardTitle>Manual Team Editor</CardTitle>
          <CardDescription>
            Drag and drop players to create custom teams
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Team Balance Chart */}
          {(teams.A.length > 0 || teams.B.length > 0) && (
            <>
              <TeamBalanceChart teamA={teams.A} teamB={teams.B} />
              <Separator />
            </>
          )}

          {/* Team Drop Zones */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <DropZone
              team="A"
              players={teams.A}
              onMovePlayer={handleMovePlayer}
              title="Team A"
              icon={<Users className="h-4 w-4 text-green-600" />}
            />

            <DropZone
              team="unassigned"
              players={teams.unassigned}
              onMovePlayer={handleMovePlayer}
              title="Available Players"
              icon={<User className="h-4 w-4" />}
            />

            <DropZone
              team="B"
              players={teams.B}
              onMovePlayer={handleMovePlayer}
              title="Team B"
              icon={<Users className="h-4 w-4 text-blue-600" />}
            />
          </div>
        </CardContent>
        <CardFooter className="flex justify-between">
          <Button variant="outline" onClick={handleAutoBalance}>
            <ArrowLeftRight className="mr-2 h-4 w-4" />
            Auto-Balance
          </Button>
          <Button onClick={handleSaveChanges}>
            <Save className="mr-2 h-4 w-4" />
            Save Teams
          </Button>
        </CardFooter>
      </Card>
    </DndProvider>
  );
}
