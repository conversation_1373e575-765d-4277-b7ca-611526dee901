import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Dialog,
  DialogContent,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { format as dateFormat } from "date-fns"; // Rename to avoid conflicts
import { Player } from "@/utils/teamGenerator";
import { useTranslation } from "react-i18next";

interface SaveMatchDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  teamA: Player[];
  teamB: Player[];
  scoreA: number;
  scoreB: number;
  format: string;
  onScoreAChange: (value: number) => void;
  onScoreBChange: (value: number) => void;
  onSaveMatch: () => void;
}

const SaveMatchDialog = ({
  open,
  onOpenChange,
  teamA,
  teamB,
  scoreA,
  scoreB,
  format,
  onScoreAChange,
  onScoreBChange,
  onSaveMatch,
}: SaveMatchDialogProps) => {
  const { t } = useTranslation();
  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>{t('teamGenerator.saveGeneratedMatch', 'Save Generated Match')}</DialogTitle>
        </DialogHeader>
        <div className="grid gap-4 py-4">
          <div className="grid grid-cols-2 gap-6">
            <div>
              <Label htmlFor="scoreA" className="mb-2 block">{t('teamGenerator.teamAScore', 'Team A Score')}</Label>
              <Input
                id="scoreA"
                type="number"
                min={0}
                value={scoreA}
                onChange={(e) => onScoreAChange(parseInt(e.target.value) || 0)}
              />
              <div className="mt-2">
                <p className="font-medium text-sm">{t('common.players', 'Players')}:</p>
                <ul className="text-sm text-muted-foreground">
                  {teamA.map((player) => (
                    <li key={player.id}>{player.name}</li>
                  ))}
                </ul>
              </div>
            </div>
            <div>
              <Label htmlFor="scoreB" className="mb-2 block">{t('teamGenerator.teamBScore', 'Team B Score')}</Label>
              <Input
                id="scoreB"
                type="number"
                min={0}
                value={scoreB}
                onChange={(e) => onScoreBChange(parseInt(e.target.value) || 0)}
              />
              <div className="mt-2">
                <p className="font-medium text-sm">{t('common.players', 'Players')}:</p>
                <ul className="text-sm text-muted-foreground">
                  {teamB.map((player) => (
                    <li key={player.id}>{player.name}</li>
                  ))}
                </ul>
              </div>
            </div>
          </div>
          <div>
            <Label htmlFor="date" className="mb-2 block">{t('matches.date', 'Date')}</Label>
            <Input
              id="date"
              type="text"
              readOnly
              value={dateFormat(new Date(), "d/M/yyyy")}
            />
          </div>
          <div>
            <Label className="mb-2 block">{t('teamGenerator.format', 'Format')}</Label>
            <div className="text-sm font-medium bg-gray-100 dark:bg-gray-800 p-2 rounded">
              {format}
            </div>
          </div>
        </div>
        <DialogFooter>
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            {t('common.cancel', 'Cancel')}
          </Button>
          <Button onClick={onSaveMatch} className="bg-soccer-primary hover:bg-soccer-primary/90">
            {t('teamGenerator.saveMatch', 'Save Match')}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default SaveMatchDialog;
