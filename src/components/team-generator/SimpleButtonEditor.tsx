import { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Separator } from "@/components/ui/separator";
import { ArrowLeftRight, ArrowRight, Save, User, Users, AlertTriangle, Plus, Minus, Shuffle } from "lucide-react";
import { Player } from "@/utils/teamGenerator";
import { TeamBalanceChart } from "./TeamBalanceChart";

// Simple player card component with buttons
const PlayerCard = ({ player, team, onMoveToTeamA, onMoveToTeamB, onMoveToUnassigned }) => {
  // Get background color based on team
  const getBgColor = () => {
    if (team === "teamA") return "bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-100";
    if (team === "teamB") return "bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-100";
    return "bg-gray-100 dark:bg-gray-800";
  };

  return (
    <div className={`${getBgColor()} p-2 mb-2 rounded-md flex justify-between items-center`}>
      <div className="flex items-center gap-2">
        <User className="h-4 w-4" />
        <span>{player.name}</span>
        <span className={`text-xs px-2 py-0.5 rounded-full ${team === "teamA" ? "bg-green-50 dark:bg-green-950 text-green-800 dark:text-green-100" : team === "teamB" ? "bg-blue-50 dark:bg-blue-950 text-blue-800 dark:text-blue-100" : "bg-background dark:bg-gray-700"}`}>
          {player.rating}
        </span>
      </div>
      <div className="flex gap-1">
        {team !== "teamA" && (
          <Button
            variant="ghost"
            size="icon"
            className="h-6 w-6 rounded-full bg-green-100 dark:bg-green-900 hover:bg-green-100 dark:hover:bg-green-900"
            onClick={onMoveToTeamA}
            title={team === "unassigned" ? "Add to Team A" : "Move to Team A"}
          >
            {team === "unassigned" ? <Plus className="h-3 w-3" /> : <Shuffle className="h-3 w-3" />}
          </Button>
        )}
        {team !== "teamB" && (
          <Button
            variant="ghost"
            size="icon"
            className="h-6 w-6 rounded-full bg-blue-100 dark:bg-blue-900 hover:bg-blue-100 dark:hover:bg-blue-900"
            onClick={onMoveToTeamB}
            title={team === "unassigned" ? "Add to Team B" : "Move to Team B"}
          >
            {team === "unassigned" ? <Plus className="h-3 w-3" /> : <Shuffle className="h-3 w-3" />}
          </Button>
        )}
        {team !== "unassigned" && (
          <Button
            variant="ghost"
            size="icon"
            className="h-6 w-6 rounded-full bg-red-100 dark:bg-red-900 hover:bg-red-100 dark:hover:bg-red-900 text-red-600 dark:text-red-200 hover:scale-100 hover:shadow-none transition-none"
            onClick={onMoveToUnassigned}
            title="Remove from team"
          >
            <Minus className="h-3 w-3" />
          </Button>
        )}
      </div>
    </div>
  );
};

// Team container component
const TeamContainer = ({ team, players, onMoveToTeamA, onMoveToTeamB, onMoveToUnassigned, title, icon }) => {
  return (
    <div className="border-2 border-border rounded-md p-3 h-full min-h-[200px]">
      <div className="flex items-center gap-2 mb-3 font-medium">
        {icon}
        <span>{title}</span>
        <span className={`text-xs px-2 py-0.5 rounded-full ml-auto ${team === "teamA" ? "bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-100" : team === "teamB" ? "bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-100" : "bg-muted"}`}>
          {players.length} players
        </span>
      </div>
      <div className="space-y-2 max-h-[300px] overflow-y-auto pr-1">
        {players.map((player) => (
          <PlayerCard
            key={player.id}
            player={player}
            team={team}
            onMoveToTeamA={() => onMoveToTeamA(player.id)}
            onMoveToTeamB={() => onMoveToTeamB(player.id)}
            onMoveToUnassigned={() => onMoveToUnassigned(player.id)}
          />
        ))}
        {players.length === 0 && (
          <div className="text-center py-8 text-muted-foreground">
            <p>No players in this team</p>
          </div>
        )}
      </div>
    </div>
  );
};

export function SimpleButtonEditor({
  teamA: initialTeamA,
  teamB: initialTeamB,
  availablePlayers: initialAvailablePlayers,
  onTeamsChange,
  onSaveTeams,
}) {
  // State for each team
  const [teamA, setTeamA] = useState([...initialTeamA]);
  const [teamB, setTeamB] = useState([...initialTeamB]);
  const [unassigned, setUnassigned] = useState([]);

  // Initialize unassigned players
  useEffect(() => {
    // Get the IDs of players already in teams
    const teamAIds = new Set(initialTeamA.map(p => p.id));
    const teamBIds = new Set(initialTeamB.map(p => p.id));

    // Filter available players to exclude those already in teams
    const filteredUnassigned = initialAvailablePlayers.filter(
      p => !teamAIds.has(p.id) && !teamBIds.has(p.id)
    );

    setTeamA([...initialTeamA]);
    setTeamB([...initialTeamB]);
    setUnassigned(filteredUnassigned);
  }, [initialTeamA, initialTeamB, initialAvailablePlayers]);

  // Move player to Team A
  const moveToTeamA = (playerId) => {
    // Find the player
    let player;
    let sourceTeam;

    if (teamB.some(p => p.id === playerId)) {
      player = teamB.find(p => p.id === playerId);
      sourceTeam = "teamB";
    } else if (unassigned.some(p => p.id === playerId)) {
      player = unassigned.find(p => p.id === playerId);
      sourceTeam = "unassigned";
    } else {
      return; // Player not found or already in Team A
    }

    // Remove from source team
    if (sourceTeam === "teamB") {
      setTeamB(teamB.filter(p => p.id !== playerId));
    } else {
      setUnassigned(unassigned.filter(p => p.id !== playerId));
    }

    // Add to Team A
    setTeamA([...teamA, player]);
  };

  // Move player to Team B
  const moveToTeamB = (playerId) => {
    // Find the player
    let player;
    let sourceTeam;

    if (teamA.some(p => p.id === playerId)) {
      player = teamA.find(p => p.id === playerId);
      sourceTeam = "teamA";
    } else if (unassigned.some(p => p.id === playerId)) {
      player = unassigned.find(p => p.id === playerId);
      sourceTeam = "unassigned";
    } else {
      return; // Player not found or already in Team B
    }

    // Remove from source team
    if (sourceTeam === "teamA") {
      setTeamA(teamA.filter(p => p.id !== playerId));
    } else {
      setUnassigned(unassigned.filter(p => p.id !== playerId));
    }

    // Add to Team B
    setTeamB([...teamB, player]);
  };

  // Move player to unassigned
  const moveToUnassigned = (playerId) => {
    // Find the player
    let player;
    let sourceTeam;

    if (teamA.some(p => p.id === playerId)) {
      player = teamA.find(p => p.id === playerId);
      sourceTeam = "teamA";
    } else if (teamB.some(p => p.id === playerId)) {
      player = teamB.find(p => p.id === playerId);
      sourceTeam = "teamB";
    } else {
      return; // Player not found or already unassigned
    }

    // Remove from source team
    if (sourceTeam === "teamA") {
      setTeamA(teamA.filter(p => p.id !== playerId));
    } else {
      setTeamB(teamB.filter(p => p.id !== playerId));
    }

    // Add to unassigned
    setUnassigned([...unassigned, player]);
  };

  // Auto-balance teams
  const handleAutoBalance = () => {
    // Combine all players
    const allPlayers = [...teamA, ...teamB, ...unassigned];

    if (allPlayers.length === 0) return;

    // Sort by rating (highest to lowest)
    const sortedPlayers = [...allPlayers].sort((a, b) =>
      (b.rating || 0) - (a.rating || 0)
    );

    // Reset teams
    const newTeamA = [];
    const newTeamB = [];

    // Distribute players in snake draft order (1-2-2-1)
    sortedPlayers.forEach((player, index) => {
      if (index % 4 === 0 || index % 4 === 3) {
        newTeamA.push(player);
      } else {
        newTeamB.push(player);
      }
    });

    // Update state
    setTeamA(newTeamA);
    setTeamB(newTeamB);
    setUnassigned([]);
  };

  // Save changes
  const handleSaveTeams = () => {
    // Notify the parent component with the final teams
    onTeamsChange(teamA, teamB);
    onSaveTeams();
  };

  return (
    <Card hoverable={false}>
      <CardHeader>
        <CardTitle>Manual Team Editor</CardTitle>
        <CardDescription>
          Use the buttons to move players between teams
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Team Balance Chart */}
        {(teamA.length > 0 || teamB.length > 0) && (
          <>
            <TeamBalanceChart teamA={teamA} teamB={teamB} />
            <Separator />
          </>
        )}

        {/* Team Containers */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <TeamContainer
            team="teamA"
            players={teamA}
            onMoveToTeamA={() => {}}
            onMoveToTeamB={moveToTeamB}
            onMoveToUnassigned={moveToUnassigned}
            title="Team A"
            icon={<Users className="h-4 w-4 text-green-600" />}
          />

          <TeamContainer
            team="unassigned"
            players={unassigned}
            onMoveToTeamA={moveToTeamA}
            onMoveToTeamB={moveToTeamB}
            onMoveToUnassigned={() => {}}
            title="Available Players"
            icon={<User className="h-4 w-4" />}
          />

          <TeamContainer
            team="teamB"
            players={teamB}
            onMoveToTeamA={moveToTeamA}
            onMoveToTeamB={() => {}}
            onMoveToUnassigned={moveToUnassigned}
            title="Team B"
            icon={<Users className="h-4 w-4 text-blue-600" />}
          />
        </div>
      </CardContent>
      <CardFooter className="flex justify-between">
        <Button variant="outline" onClick={handleAutoBalance} className="hover:scale-100 hover:shadow-none transition-none">
          <ArrowLeftRight className="mr-2 h-4 w-4" />
          Auto-Balance
        </Button>
        <Button onClick={handleSaveTeams} className="hover:scale-100 hover:shadow-none transition-none">
          <Save className="mr-2 h-4 w-4" />
          Save Teams
        </Button>
      </CardFooter>
    </Card>
  );
}
