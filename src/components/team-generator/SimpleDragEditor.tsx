import { useState, useEffect } from "react";
import { DndProvider, useDrag, useDrop } from "react-dnd";
import { HTML5Backend } from "react-dnd-html5-backend";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Separator } from "@/components/ui/separator";
import { ArrowLeftRight, Save, User, Users } from "lucide-react";
import { Player } from "@/utils/teamGenerator";
import { TeamBalanceChart } from "./TeamBalanceChart";

// Define the drag item type
const ItemTypes = {
  PLAYER: "player",
};

// Simple player card component
const PlayerCard = ({ player, team, onDrag }) => {
  const [{ isDragging }, drag] = useDrag(() => ({
    type: ItemTypes.PLAYER,
    item: { id: player.id, sourceTeam: team },
    collect: (monitor) => ({
      isDragging: !!monitor.isDragging(),
    }),
  }));

  // Get background color based on team
  const getBgColor = () => {
    if (team === "teamA") return "bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-100";
    if (team === "teamB") return "bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-100";
    return "bg-gray-100 dark:bg-gray-800";
  };

  return (
    <div
      ref={drag}
      className={`${getBgColor()} p-2 mb-2 rounded-md cursor-move flex justify-between items-center ${
        isDragging ? "opacity-50" : "opacity-100"
      }`}
    >
      <div className="flex items-center gap-2">
        <User className="h-4 w-4" />
        <span>{player.name}</span>
      </div>
      <span className={`text-xs px-2 py-0.5 rounded-full ${team === "teamA" ? "bg-green-50 dark:bg-green-950 text-green-800 dark:text-green-100" : team === "teamB" ? "bg-blue-50 dark:bg-blue-950 text-blue-800 dark:text-blue-100" : "bg-background dark:bg-gray-700"}`}>
        {player.rating}
      </span>
    </div>
  );
};

// Team container component
const TeamContainer = ({ team, players, onDrop, title, icon }) => {
  const [{ isOver }, drop] = useDrop(() => ({
    accept: ItemTypes.PLAYER,
    drop: (item) => {
      console.log(`Dropping player ${item.id} from ${item.sourceTeam} to ${team}`);
      onDrop(item.id, item.sourceTeam, team);
    },
    collect: (monitor) => ({
      isOver: !!monitor.isOver(),
    }),
  }));

  // Get border color based on drop state
  const getBorderColor = () => {
    if (isOver) {
      if (team === "teamA") return "border-green-500 dark:border-green-400";
      if (team === "teamB") return "border-blue-500 dark:border-blue-400";
      return "border-gray-500 dark:border-gray-400";
    }
    return "border-border";
  };

  return (
    <div
      ref={drop}
      className={`border-2 ${getBorderColor()} rounded-md p-3 h-full min-h-[200px] transition-colors`}
    >
      <div className="flex items-center gap-2 mb-3 font-medium">
        {icon}
        <span>{title}</span>
        <span className={`text-xs px-2 py-0.5 rounded-full ml-auto ${team === "teamA" ? "bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-100" : team === "teamB" ? "bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-100" : "bg-muted"}`}>
          {players.length} players
        </span>
      </div>
      <div className="space-y-2 max-h-[300px] overflow-y-auto pr-1">
        {players.map((player) => (
          <PlayerCard
            key={player.id}
            player={player}
            team={team}
            onDrag={() => {}}
          />
        ))}
        {players.length === 0 && (
          <div className="text-center py-8 text-muted-foreground">
            <p>Drop players here</p>
          </div>
        )}
      </div>
    </div>
  );
};

export function SimpleDragEditor({
  teamA: initialTeamA,
  teamB: initialTeamB,
  availablePlayers,
  onTeamsChange,
  onSaveTeams,
}) {
  // State for each team
  const [teamA, setTeamA] = useState([...initialTeamA]);
  const [teamB, setTeamB] = useState([...initialTeamB]);
  const [unassigned, setUnassigned] = useState(
    availablePlayers.filter(
      p => !initialTeamA.some(a => a.id === p.id) && !initialTeamB.some(b => b.id === p.id)
    )
  );

  // Update when props change
  useEffect(() => {
    console.log("Props changed in SimpleDragEditor");
    setTeamA([...initialTeamA]);
    setTeamB([...initialTeamB]);
    
    // Update unassigned players
    const teamAIds = new Set(initialTeamA.map(p => p.id));
    const teamBIds = new Set(initialTeamB.map(p => p.id));
    
    setUnassigned(
      availablePlayers.filter(
        p => !teamAIds.has(p.id) && !teamBIds.has(p.id)
      )
    );
  }, [initialTeamA, initialTeamB, availablePlayers]);

  // Handle player drop
  const handleDrop = (playerId, sourceTeam, targetTeam) => {
    console.log(`Moving player ${playerId} from ${sourceTeam} to ${targetTeam}`);
    
    // Find the player
    let player;
    if (sourceTeam === "teamA") {
      player = teamA.find(p => p.id === playerId);
    } else if (sourceTeam === "teamB") {
      player = teamB.find(p => p.id === playerId);
    } else {
      player = unassigned.find(p => p.id === playerId);
    }
    
    if (!player) {
      console.error(`Player ${playerId} not found in ${sourceTeam}`);
      return;
    }
    
    // Remove from source team
    if (sourceTeam === "teamA") {
      setTeamA(teamA.filter(p => p.id !== playerId));
    } else if (sourceTeam === "teamB") {
      setTeamB(teamB.filter(p => p.id !== playerId));
    } else {
      setUnassigned(unassigned.filter(p => p.id !== playerId));
    }
    
    // Add to target team
    if (targetTeam === "teamA") {
      setTeamA([...teamA, player]);
    } else if (targetTeam === "teamB") {
      setTeamB([...teamB, player]);
    } else {
      setUnassigned([...unassigned, player]);
    }
    
    // Notify parent
    if (targetTeam === "teamA" || targetTeam === "teamB" || 
        sourceTeam === "teamA" || sourceTeam === "teamB") {
      
      // Calculate the new teams
      const newTeamA = targetTeam === "teamA" 
        ? [...teamA.filter(p => p.id !== playerId), player]
        : sourceTeam === "teamA"
          ? teamA.filter(p => p.id !== playerId)
          : [...teamA];
          
      const newTeamB = targetTeam === "teamB" 
        ? [...teamB.filter(p => p.id !== playerId), player]
        : sourceTeam === "teamB"
          ? teamB.filter(p => p.id !== playerId)
          : [...teamB];
      
      // Notify parent
      onTeamsChange(newTeamA, newTeamB);
    }
  };

  // Auto-balance teams
  const handleAutoBalance = () => {
    // Combine all players
    const allPlayers = [...teamA, ...teamB, ...unassigned];
    
    if (allPlayers.length === 0) return;
    
    // Sort by rating (highest to lowest)
    const sortedPlayers = [...allPlayers].sort((a, b) => 
      (b.rating || 0) - (a.rating || 0)
    );
    
    // Reset teams
    const newTeamA = [];
    const newTeamB = [];
    
    // Distribute players in snake draft order (1-2-2-1)
    sortedPlayers.forEach((player, index) => {
      if (index % 4 === 0 || index % 4 === 3) {
        newTeamA.push(player);
      } else {
        newTeamB.push(player);
      }
    });
    
    // Update state
    setTeamA(newTeamA);
    setTeamB(newTeamB);
    setUnassigned([]);
    
    // Notify parent
    onTeamsChange(newTeamA, newTeamB);
  };

  return (
    <DndProvider backend={HTML5Backend}>
      <Card hoverable={false}>
        <CardHeader>
          <CardTitle>Manual Team Editor</CardTitle>
          <CardDescription>
            Drag and drop players to create custom teams
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Team Balance Chart */}
          {(teamA.length > 0 || teamB.length > 0) && (
            <>
              <TeamBalanceChart teamA={teamA} teamB={teamB} />
              <Separator />
            </>
          )}

          {/* Team Drop Zones */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <TeamContainer
              team="teamA"
              players={teamA}
              onDrop={handleDrop}
              title="Team A"
              icon={<Users className="h-4 w-4 text-green-600" />}
            />

            <TeamContainer
              team="unassigned"
              players={unassigned}
              onDrop={handleDrop}
              title="Available Players"
              icon={<User className="h-4 w-4" />}
            />

            <TeamContainer
              team="teamB"
              players={teamB}
              onDrop={handleDrop}
              title="Team B"
              icon={<Users className="h-4 w-4 text-blue-600" />}
            />
          </div>
        </CardContent>
        <CardFooter className="flex justify-between">
          <Button variant="outline" onClick={handleAutoBalance}>
            <ArrowLeftRight className="mr-2 h-4 w-4" />
            Auto-Balance
          </Button>
          <Button onClick={() => onSaveTeams()}>
            <Save className="mr-2 h-4 w-4" />
            Save Teams
          </Button>
        </CardFooter>
      </Card>
    </DndProvider>
  );
}
