import { useState, useEffect } from "react";
import { DndProvider, useDrag, useDrop } from "react-dnd";
import { HTML5Backend } from "react-dnd-html5-backend";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Separator } from "@/components/ui/separator";
import { ArrowLeftRight, Save, User, Users } from "lucide-react";
import { Player } from "@/utils/teamGenerator";
import { TeamBalanceChart } from "./TeamBalanceChart";

// Define the drag item type
const ItemTypes = {
  PLAYER: "player",
};

// Simple player card component
const PlayerCard = ({ player, team, onDrag }) => {
  const [{ isDragging }, drag] = useDrag(() => ({
    type: ItemTypes.PLAYER,
    item: { id: player.id, sourceTeam: team },
    collect: (monitor) => ({
      isDragging: !!monitor.isDragging(),
    }),
  }));

  // Get background color based on team
  const getBgColor = () => {
    if (team === "teamA") return "bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-100";
    if (team === "teamB") return "bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-100";
    return "bg-gray-100 dark:bg-gray-800";
  };

  return (
    <div
      ref={drag}
      className={`${getBgColor()} p-2 mb-2 rounded-md cursor-move flex justify-between items-center ${
        isDragging ? "opacity-50" : "opacity-100"
      }`}
    >
      <div className="flex items-center gap-2">
        <User className="h-4 w-4" />
        <span>{player.name}</span>
      </div>
      <span className={`text-xs px-2 py-0.5 rounded-full ${team === "teamA" ? "bg-green-50 dark:bg-green-950 text-green-800 dark:text-green-100" : team === "teamB" ? "bg-blue-50 dark:bg-blue-950 text-blue-800 dark:text-blue-100" : "bg-background dark:bg-gray-700"}`}>
        {player.rating}
      </span>
    </div>
  );
};

// Team container component
const TeamContainer = ({ team, players, onDrop, title, icon }) => {
  const [{ isOver }, drop] = useDrop(() => ({
    accept: ItemTypes.PLAYER,
    drop: (item) => {
      onDrop(item.id, item.sourceTeam, team);
    },
    collect: (monitor) => ({
      isOver: !!monitor.isOver(),
    }),
  }));

  // Get border color based on drop state
  const getBorderColor = () => {
    if (isOver) {
      if (team === "teamA") return "border-green-500 dark:border-green-400";
      if (team === "teamB") return "border-blue-500 dark:border-blue-400";
      return "border-gray-500 dark:border-gray-400";
    }
    return "border-border";
  };

  return (
    <div
      ref={drop}
      className={`border-2 ${getBorderColor()} rounded-md p-3 h-full min-h-[200px] transition-colors`}
    >
      <div className="flex items-center gap-2 mb-3 font-medium">
        {icon}
        <span>{title}</span>
        <span className={`text-xs px-2 py-0.5 rounded-full ml-auto ${team === "teamA" ? "bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-100" : team === "teamB" ? "bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-100" : "bg-muted"}`}>
          {players.length} players
        </span>
      </div>
      <div className="space-y-2 max-h-[300px] overflow-y-auto pr-1">
        {players.map((player) => (
          <PlayerCard
            key={player.id}
            player={player}
            team={team}
            onDrag={() => {}}
          />
        ))}
        {players.length === 0 && (
          <div className="text-center py-8 text-muted-foreground">
            <p>Drop players here</p>
          </div>
        )}
      </div>
    </div>
  );
};

export function StandaloneDragEditor({
  teamA: initialTeamA,
  teamB: initialTeamB,
  availablePlayers: initialAvailablePlayers,
  onTeamsChange,
  onSaveTeams,
}) {
  // Initialize state only once on mount
  const [initialized, setInitialized] = useState(false);
  const [teams, setTeams] = useState({
    teamA: [],
    teamB: [],
    unassigned: []
  });

  // Initialize teams only once when component mounts
  useEffect(() => {
    if (!initialized) {
      // Create a map of all players by ID
      const allPlayersMap = new Map();
      initialAvailablePlayers.forEach(player => {
        allPlayersMap.set(player.id, player);
      });
      
      // Get the initial teams
      const teamA = [...initialTeamA];
      const teamB = [...initialTeamB];
      
      // Get the IDs of players already in teams
      const teamAIds = new Set(teamA.map(p => p.id));
      const teamBIds = new Set(teamB.map(p => p.id));
      
      // Filter available players to exclude those already in teams
      const unassigned = initialAvailablePlayers.filter(
        p => !teamAIds.has(p.id) && !teamBIds.has(p.id)
      );
      
      // Set the initial state
      setTeams({
        teamA,
        teamB,
        unassigned
      });
      
      setInitialized(true);
    }
  }, [initialized, initialTeamA, initialTeamB, initialAvailablePlayers]);

  // Handle player drop
  const handleDrop = (playerId, sourceTeam, targetTeam) => {
    // Find the player in the source team
    let player;
    if (sourceTeam === "teamA") {
      player = teams.teamA.find(p => p.id === playerId);
    } else if (sourceTeam === "teamB") {
      player = teams.teamB.find(p => p.id === playerId);
    } else {
      player = teams.unassigned.find(p => p.id === playerId);
    }
    
    if (!player) {
      console.error(`Player ${playerId} not found in ${sourceTeam}`);
      return;
    }
    
    // Create a new state object
    const newTeams = {
      teamA: [...teams.teamA],
      teamB: [...teams.teamB],
      unassigned: [...teams.unassigned]
    };
    
    // Remove from source team
    if (sourceTeam === "teamA") {
      newTeams.teamA = newTeams.teamA.filter(p => p.id !== playerId);
    } else if (sourceTeam === "teamB") {
      newTeams.teamB = newTeams.teamB.filter(p => p.id !== playerId);
    } else {
      newTeams.unassigned = newTeams.unassigned.filter(p => p.id !== playerId);
    }
    
    // Add to target team
    if (targetTeam === "teamA") {
      newTeams.teamA.push(player);
    } else if (targetTeam === "teamB") {
      newTeams.teamB.push(player);
    } else {
      newTeams.unassigned.push(player);
    }
    
    // Update state
    setTeams(newTeams);
    
    // Notify parent component only when saving
    // This prevents the parent from updating and causing a re-render
  };

  // Auto-balance teams
  const handleAutoBalance = () => {
    // Combine all players
    const allPlayers = [...teams.teamA, ...teams.teamB, ...teams.unassigned];
    
    if (allPlayers.length === 0) return;
    
    // Sort by rating (highest to lowest)
    const sortedPlayers = [...allPlayers].sort((a, b) => 
      (b.rating || 0) - (a.rating || 0)
    );
    
    // Reset teams
    const newTeamA = [];
    const newTeamB = [];
    
    // Distribute players in snake draft order (1-2-2-1)
    sortedPlayers.forEach((player, index) => {
      if (index % 4 === 0 || index % 4 === 3) {
        newTeamA.push(player);
      } else {
        newTeamB.push(player);
      }
    });
    
    // Update state
    setTeams({
      teamA: newTeamA,
      teamB: newTeamB,
      unassigned: []
    });
    
    // Don't notify parent until save
  };

  // Save changes
  const handleSaveTeams = () => {
    // Now notify the parent component with the final teams
    onTeamsChange(teams.teamA, teams.teamB);
    onSaveTeams();
  };

  // If not initialized yet, show loading
  if (!initialized) {
    return (
      <Card>
        <CardContent className="py-10">
          <div className="text-center">Loading teams...</div>
        </CardContent>
      </Card>
    );
  }

  return (
    <DndProvider backend={HTML5Backend}>
      <Card>
        <CardHeader>
          <CardTitle>Manual Team Editor</CardTitle>
          <CardDescription>
            Drag and drop players to create custom teams
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Team Balance Chart */}
          {(teams.teamA.length > 0 || teams.teamB.length > 0) && (
            <>
              <TeamBalanceChart teamA={teams.teamA} teamB={teams.teamB} />
              <Separator />
            </>
          )}

          {/* Team Drop Zones */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <TeamContainer
              team="teamA"
              players={teams.teamA}
              onDrop={handleDrop}
              title="Team A"
              icon={<Users className="h-4 w-4 text-green-600" />}
            />

            <TeamContainer
              team="unassigned"
              players={teams.unassigned}
              onDrop={handleDrop}
              title="Available Players"
              icon={<User className="h-4 w-4" />}
            />

            <TeamContainer
              team="teamB"
              players={teams.teamB}
              onDrop={handleDrop}
              title="Team B"
              icon={<Users className="h-4 w-4 text-blue-600" />}
            />
          </div>
        </CardContent>
        <CardFooter className="flex justify-between">
          <Button variant="outline" onClick={handleAutoBalance}>
            <ArrowLeftRight className="mr-2 h-4 w-4" />
            Auto-Balance
          </Button>
          <Button onClick={handleSaveTeams}>
            <Save className="mr-2 h-4 w-4" />
            Save Teams
          </Button>
        </CardFooter>
      </Card>
    </DndProvider>
  );
}
