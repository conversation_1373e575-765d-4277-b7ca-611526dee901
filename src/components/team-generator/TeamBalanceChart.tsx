import { useMemo } from "react";
import { ChartContainer } from "@/components/ui/chart";
import {
  <PERSON>,
  <PERSON><PERSON>hart,
  CartesianGrid,
  Legend,
  RadarChart,
  PolarGrid,
  PolarAngleAxis,
  PolarRadiusAxis,
  Radar,
  ResponsiveContainer,
  Tooltip,
  XAxis,
  YAxis,
} from "recharts";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>bsList, TabsTrigger } from "@/components/ui/tabs";

interface Player {
  id: number;
  name: string;
  skills?: number;
  effort?: number;
  stamina?: number;
  rating?: number;
}

interface TeamBalanceChartProps {
  teamA: Player[];
  teamB: Player[];
}

export function TeamBalanceChart({ teamA, teamB }: TeamBalanceChartProps) {
  // Calculate average stats for each team
  const teamStats = useMemo(() => {
    const calculateTeamAverage = (team: Player[], stat: keyof Player) => {
      if (team.length === 0) return 0;
      const sum = team.reduce((acc, player) => {
        const value = player[stat];
        return acc + (typeof value === 'number' ? value : 0);
      }, 0);
      return Math.round(sum / team.length);
    };

    const calculateTeamRating = (team: Player[]) => {
      if (team.length === 0) return 0;
      const sum = team.reduce((acc, player) => acc + (player.rating || 0), 0);
      return Math.round(sum / team.length);
    };

    return [
      {
        name: "Skills",
        "Team A": calculateTeamAverage(teamA, "skills"),
        "Team B": calculateTeamAverage(teamB, "skills"),
      },
      {
        name: "Effort",
        "Team A": calculateTeamAverage(teamA, "effort"),
        "Team B": calculateTeamAverage(teamB, "effort"),
      },
      {
        name: "Stamina",
        "Team A": calculateTeamAverage(teamA, "stamina"),
        "Team B": calculateTeamAverage(teamB, "stamina"),
      },
      {
        name: "Rating",
        "Team A": calculateTeamRating(teamA),
        "Team B": calculateTeamRating(teamB),
      },
    ];
  }, [teamA, teamB]);

  // Prepare data for radar chart
  const radarData = useMemo(() => {
    return [
      {
        subject: "Skills",
        "Team A": teamStats[0]["Team A"],
        "Team B": teamStats[0]["Team B"],
        fullMark: 100,
      },
      {
        subject: "Effort",
        "Team A": teamStats[1]["Team A"],
        "Team B": teamStats[1]["Team B"],
        fullMark: 100,
      },
      {
        subject: "Stamina",
        "Team A": teamStats[2]["Team A"],
        "Team B": teamStats[2]["Team B"],
        fullMark: 100,
      },
      {
        subject: "Rating",
        "Team A": teamStats[3]["Team A"],
        "Team B": teamStats[3]["Team B"],
        fullMark: 100,
      },
    ];
  }, [teamStats]);

  // Calculate team balance score (0-100)
  const balanceScore = useMemo(() => {
    if (teamA.length === 0 || teamB.length === 0) return 0;

    // Calculate difference in each stat
    const skillsDiff = Math.abs(teamStats[0]["Team A"] - teamStats[0]["Team B"]);
    const effortDiff = Math.abs(teamStats[1]["Team A"] - teamStats[1]["Team B"]);
    const staminaDiff = Math.abs(teamStats[2]["Team A"] - teamStats[2]["Team B"]);
    const ratingDiff = Math.abs(teamStats[3]["Team A"] - teamStats[3]["Team B"]);

    // Average difference (weighted more towards rating)
    const avgDiff = (skillsDiff + effortDiff + staminaDiff + ratingDiff * 2) / 5;

    // Convert to a 0-100 score (0 = perfectly balanced)
    const rawScore = Math.max(0, 100 - avgDiff * 5);

    return Math.round(rawScore);
  }, [teamA, teamB, teamStats]);

  // Get balance score color
  const getBalanceColor = (score: number) => {
    if (score >= 90) return "text-green-500";
    if (score >= 75) return "text-lime-500";
    if (score >= 60) return "text-yellow-500";
    if (score >= 40) return "text-orange-500";
    return "text-red-500";
  };

  if (teamA.length === 0 || teamB.length === 0) {
    return (
      <div className="h-[200px] flex items-center justify-center text-muted-foreground">
        Generate teams to see balance statistics
      </div>
    );
  }

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-center gap-2">
        <span className="text-sm font-medium">Team Balance Score:</span>
        <span className={`text-xl font-bold ${getBalanceColor(balanceScore)}`}>
          {balanceScore}%
        </span>
      </div>

      <Tabs defaultValue="radar">
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="radar">Radar Chart</TabsTrigger>
          <TabsTrigger value="bar">Bar Chart</TabsTrigger>
        </TabsList>

        <TabsContent value="radar" className="h-[250px] overflow-hidden">
          <div className="w-full h-full flex items-center justify-center">
            <ChartContainer
              className="w-full max-w-[400px] mx-auto"
              config={{
                "Team A": {
                  label: "Team A",
                  theme: {
                    light: "#35db71",
                    dark: "#35db71",
                  },
                },
                "Team B": {
                  label: "Team B",
                  theme: {
                    light: "#3b82f6",
                    dark: "#3b82f6",
                  },
                },
              }}
            >
              <ResponsiveContainer width="100%" height={220}>
                <RadarChart data={radarData} margin={{ top: 20, right: 30, left: 30, bottom: 10 }}>
                  <PolarGrid />
                  <PolarAngleAxis dataKey="subject" />
                  <PolarRadiusAxis domain={[0, 100]} />
                  <Radar
                    name="Team A"
                    dataKey="Team A"
                    stroke="#35db71"
                    fill="#35db71"
                    fillOpacity={0.5}
                  />
                  <Radar
                    name="Team B"
                    dataKey="Team B"
                    stroke="#3b82f6"
                    fill="#3b82f6"
                    fillOpacity={0.5}
                  />
                  <Legend />
                  <Tooltip />
                </RadarChart>
              </ResponsiveContainer>
            </ChartContainer>
          </div>
        </TabsContent>

        <TabsContent value="bar" className="h-[250px] overflow-hidden">
          <div className="w-full h-full flex items-center justify-center">
            <ChartContainer
              className="w-full max-w-[400px] mx-auto"
              config={{
                "Team A": {
                  label: "Team A",
                  theme: {
                    light: "#35db71",
                    dark: "#35db71",
                  },
                },
                "Team B": {
                  label: "Team B",
                  theme: {
                    light: "#3b82f6",
                    dark: "#3b82f6",
                  },
                },
              }}
            >
              <ResponsiveContainer width="100%" height={220}>
                <BarChart
                  data={teamStats}
                  margin={{ top: 20, right: 30, left: 20, bottom: 5 }}
                >
                  <CartesianGrid strokeDasharray="3 3" opacity={0.2} />
                  <XAxis dataKey="name" />
                  <YAxis domain={[0, 100]} />
                  <Tooltip />
                  <Legend />
                  <Bar dataKey="Team A" fill="#35db71" />
                  <Bar dataKey="Team B" fill="#3b82f6" />
                </BarChart>
              </ResponsiveContainer>
            </ChartContainer>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
}
