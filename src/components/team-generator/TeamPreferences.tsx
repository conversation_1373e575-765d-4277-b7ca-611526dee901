import { useState, useEffect } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Dialog, DialogContent, DialogDescription, DialogFooter, Di<PERSON>Header, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Switch } from "@/components/ui/switch";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Trash2, Save, Settings, Zap, Bookmark } from "lucide-react";
import { GameFormatKey, GenerationMode } from "@/utils/teamGenerator";
import { useToast } from "@/hooks/use-toast";

export interface TeamPreference {
  id: string;
  name: string;
  format: GameFormatKey;
  generationMode: GenerationMode;
  balancePriority: "skills" | "effort" | "stamina" | "rating";
  includeInactivePlayer: boolean;
  playerIds?: number[];
}

interface TeamPreferencesProps {
  onQuickGenerate: (preference: TeamPreference) => void;
  format: GameFormatKey;
  generationMode: GenerationMode;
  selectedPlayers: number[];
  gameFormats: Record<string, { title: string; playersPerTeam: number }>;
}

export function TeamPreferences({
  onQuickGenerate,
  format,
  generationMode,
  selectedPlayers,
  gameFormats,
}: TeamPreferencesProps) {
  const { toast } = useToast();
  const [preferences, setPreferences] = useState<TeamPreference[]>([]);
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [newPreference, setNewPreference] = useState<TeamPreference>({
    id: "",
    name: "",
    format,
    generationMode,
    balancePriority: "rating",
    includeInactivePlayer: false,
  });
  const [editingPreferenceId, setEditingPreferenceId] = useState<string | null>(null);

  // Load saved preferences from localStorage
  useEffect(() => {
    const savedPreferences = localStorage.getItem("teamPreferences");
    if (savedPreferences) {
      try {
        setPreferences(JSON.parse(savedPreferences));
      } catch (error) {
        console.error("Failed to parse saved team preferences:", error);
      }
    }
  }, []);

  // Save preferences to localStorage whenever they change
  useEffect(() => {
    localStorage.setItem("teamPreferences", JSON.stringify(preferences));
  }, [preferences]);

  const handleSavePreference = () => {
    if (!newPreference.name.trim()) {
      toast({
        title: "Name Required",
        description: "Please enter a name for this preference set",
        variant: "destructive",
      });
      return;
    }

    if (editingPreferenceId) {
      // Update existing preference
      setPreferences(
        preferences.map((pref) =>
          pref.id === editingPreferenceId
            ? { ...newPreference, id: editingPreferenceId, playerIds: selectedPlayers }
            : pref
        )
      );
      toast({
        title: "Preferences Updated",
        description: `"${newPreference.name}" has been updated`,
      });
    } else {
      // Create new preference
      const newId = Date.now().toString();
      setPreferences([
        ...preferences,
        { ...newPreference, id: newId, playerIds: selectedPlayers },
      ]);
      toast({
        title: "Preferences Saved",
        description: `"${newPreference.name}" has been saved`,
      });
    }

    setIsDialogOpen(false);
    setEditingPreferenceId(null);
    setNewPreference({
      id: "",
      name: "",
      format,
      generationMode,
      balancePriority: "rating",
      includeInactivePlayer: false,
    });
  };

  const handleEditPreference = (preference: TeamPreference) => {
    setNewPreference(preference);
    setEditingPreferenceId(preference.id);
    setIsDialogOpen(true);
  };

  const handleDeletePreference = (id: string) => {
    setPreferences(preferences.filter((pref) => pref.id !== id));
    toast({
      title: "Preference Deleted",
      description: "The preference set has been removed",
    });
  };

  const handleQuickGenerate = (preference: TeamPreference) => {
    onQuickGenerate(preference);
    toast({
      title: "Quick Generate",
      description: `Using "${preference.name}" preferences`,
    });
  };

  return (
    <div>
      <div className="flex justify-between items-center mb-4">
        <h3 className="text-lg font-semibold">Saved Preferences</h3>
        <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
          <DialogTrigger asChild>
            <Button
              variant="outline"
              size="sm"
              onClick={() => {
                setEditingPreferenceId(null);
                setNewPreference({
                  id: "",
                  name: "",
                  format,
                  generationMode,
                  balancePriority: "rating",
                  includeInactivePlayer: false,
                });
              }}
            >
              <Save className="h-4 w-4 mr-2" />
              Save Current
            </Button>
          </DialogTrigger>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>
                {editingPreferenceId ? "Edit Preference" : "Save Preference"}
              </DialogTitle>
              <DialogDescription>
                Save your current team generation preferences for quick access later.
              </DialogDescription>
            </DialogHeader>
            <div className="space-y-4 py-4">
              <div className="space-y-2">
                <Label htmlFor="name">Preference Name</Label>
                <Input
                  id="name"
                  value={newPreference.name}
                  onChange={(e) =>
                    setNewPreference({ ...newPreference, name: e.target.value })
                  }
                  placeholder="e.g., Friday Night 5v5"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="format">Game Format</Label>
                <Select
                  value={newPreference.format}
                  onValueChange={(value) =>
                    setNewPreference({
                      ...newPreference,
                      format: value as GameFormatKey,
                    })
                  }
                >
                  <SelectTrigger id="format">
                    <SelectValue placeholder="Select format" />
                  </SelectTrigger>
                  <SelectContent>
                    {Object.entries(gameFormats).map(([key, format]) => (
                      <SelectItem key={key} value={key}>
                        {format.title}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              <div className="space-y-2">
                <Label htmlFor="generationMode">Generation Mode</Label>
                <Select
                  value={newPreference.generationMode}
                  onValueChange={(value) =>
                    setNewPreference({
                      ...newPreference,
                      generationMode: value as GenerationMode,
                    })
                  }
                >
                  <SelectTrigger id="generationMode">
                    <SelectValue placeholder="Select mode" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="balanced">Balanced</SelectItem>
                    <SelectItem value="random">Random</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="space-y-2">
                <Label htmlFor="balancePriority">Balance Priority</Label>
                <Select
                  value={newPreference.balancePriority}
                  onValueChange={(value) =>
                    setNewPreference({
                      ...newPreference,
                      balancePriority: value as "skills" | "effort" | "stamina" | "rating",
                    })
                  }
                >
                  <SelectTrigger id="balancePriority">
                    <SelectValue placeholder="Select priority" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="rating">Overall Rating</SelectItem>
                    <SelectItem value="skills">Skills</SelectItem>
                    <SelectItem value="effort">Effort</SelectItem>
                    <SelectItem value="stamina">Stamina</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="flex items-center space-x-2">
                <Switch
                  id="includeInactive"
                  checked={newPreference.includeInactivePlayer}
                  onCheckedChange={(checked) =>
                    setNewPreference({
                      ...newPreference,
                      includeInactivePlayer: checked,
                    })
                  }
                />
                <Label htmlFor="includeInactive">Include inactive players</Label>
              </div>
            </div>
            <DialogFooter>
              <Button variant="outline" onClick={() => setIsDialogOpen(false)}>
                Cancel
              </Button>
              <Button onClick={handleSavePreference}>
                {editingPreferenceId ? "Update" : "Save"}
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </div>

      {preferences.length === 0 ? (
        <div className="text-center py-8 text-muted-foreground">
          <Bookmark className="h-12 w-12 mx-auto mb-2 opacity-20" />
          <p>No saved preferences yet</p>
          <p className="text-sm">
            Save your current settings to quickly generate teams later
          </p>
        </div>
      ) : (
        <div className="space-y-3">
          {preferences.map((preference) => (
            <Card key={preference.id} className="overflow-hidden">
              <div className="flex items-stretch">
                <div className="flex-grow p-4">
                  <div className="font-medium">{preference.name}</div>
                  <div className="text-sm text-muted-foreground">
                    {preference.format} • {preference.generationMode} •{" "}
                    {preference.playerIds?.length || 0} players
                  </div>
                </div>
                <div className="flex">
                  <Button
                    variant="ghost"
                    size="icon"
                    onClick={() => handleEditPreference(preference)}
                    className="rounded-none border-l h-auto"
                  >
                    <Settings className="h-4 w-4" />
                  </Button>
                  <Button
                    variant="ghost"
                    size="icon"
                    onClick={() => handleDeletePreference(preference.id)}
                    className="rounded-none border-l h-auto text-destructive"
                  >
                    <Trash2 className="h-4 w-4" />
                  </Button>
                  <Button
                    variant="default"
                    className="rounded-none px-3 h-auto bg-soccer-primary hover:bg-soccer-primary/90"
                    onClick={() => handleQuickGenerate(preference)}
                  >
                    <Zap className="h-4 w-4 mr-2" />
                    Quick
                  </Button>
                </div>
              </div>
            </Card>
          ))}
        </div>
      )}
    </div>
  );
}
