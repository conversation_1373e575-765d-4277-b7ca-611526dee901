import * as React from "react";
import { cn } from "@/lib/utils";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { ChevronRight } from "lucide-react";

interface Column<T> {
  header: string;
  accessorKey: keyof T;
  cell?: (item: T) => React.ReactNode;
  priority?: "high" | "medium" | "low";
  meta?: {
    align?: "left" | "center" | "right";
    isBadge?: boolean;
    badgeVariant?: "default" | "secondary" | "outline" | "destructive";
    isNumeric?: boolean;
  };
}

interface CompactMobileTableProps<T> {
  data: T[];
  columns: Column<T>[];
  className?: string;
  onRowClick?: (item: T) => void;
  isLoading?: boolean;
  emptyState?: React.ReactNode;
  keyExtractor: (item: T) => string | number;
  rowClassName?: string | ((item: T) => string);
}

export function CompactMobileTable<T>({
  data,
  columns,
  className,
  onRowClick,
  isLoading = false,
  emptyState,
  keyExtractor,
  rowClassName,
}: CompactMobileTableProps<T>) {
  // Filter columns by priority for mobile view
  const highPriorityColumns = columns.filter(col => col.priority === "high" || col.priority === undefined);
  const mediumPriorityColumns = columns.filter(col => col.priority === "medium");

  if (isLoading) {
    return (
      <div className="flex items-center justify-center py-8">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-soccer-primary" />
      </div>
    );
  }

  if (data.length === 0 && emptyState) {
    return <>{emptyState}</>;
  }

  return (
    <div className={cn("space-y-3", className)}>
      {data.map((item) => (
        <Card
          key={keyExtractor(item)}
          className={cn(
            "overflow-hidden",
            onRowClick && "cursor-pointer",
            typeof rowClassName === "function" ? rowClassName(item) : rowClassName
          )}
          onClick={() => onRowClick && onRowClick(item)}
        >
          <CardContent className="p-3">
            <div className="flex items-center justify-between">
              <div className="flex-1 space-y-1">
                {/* Primary row - high priority columns */}
                <div className="flex items-center justify-between">
                  {highPriorityColumns.map((column, index) => {
                    // Special case for the first column (rank/trophy)
                    if (index === 0) {
                      return (
                        <div key={index} className="mr-2">
                          {column.cell ? column.cell(item) : null}
                        </div>
                      );
                    }
                    // Special case for the last column (games played)
                    else if (index === highPriorityColumns.length - 1) {
                      return (
                        <div
                          key={index}
                          className={cn(
                            "flex-none ml-auto",
                            column.meta?.align === "center" && "text-center",
                            column.meta?.align === "right" && "text-right"
                          )}
                        >
                          {column.cell ? column.cell(item) : null}
                        </div>
                      );
                    }
                    // Middle column (name)
                    else {
                      return (
                        <div
                          key={index}
                          className={cn(
                            "flex-1",
                            column.meta?.align === "center" && "text-center",
                            column.meta?.align === "right" && "text-right"
                          )}
                        >
                          {column.cell ? (
                            column.cell(item)
                          ) : column.meta?.isBadge ? (
                            <Badge variant={column.meta.badgeVariant}>
                              {String(item[column.accessorKey])}
                            </Badge>
                          ) : (
                            <div className="text-lg font-medium">
                              {String(item[column.accessorKey])}
                            </div>
                          )}
                        </div>
                      );
                    }
                  })}
                </div>

                {/* Secondary row - medium priority columns */}
                {mediumPriorityColumns.length > 0 && (
                  <div className="flex items-center justify-between text-xs text-muted-foreground">
                    {/* W/D/L Stats Group */}
                    <div className="flex space-x-3">
                      {/* Win column */}
                      {mediumPriorityColumns
                        .filter(col => col.accessorKey === 'wins')
                        .map((column, index) => (
                          <div key={index} className="flex items-center gap-1">
                            <span className="text-[10px] uppercase opacity-70">{column.header}:</span>
                            {column.cell ? column.cell(item) : null}
                          </div>
                        ))}

                      {/* Draw column */}
                      {mediumPriorityColumns
                        .filter(col => col.accessorKey === 'draws')
                        .map((column, index) => (
                          <div key={index} className="flex items-center gap-1">
                            <span className="text-[10px] uppercase opacity-70">{column.header}:</span>
                            {column.cell ? column.cell(item) : null}
                          </div>
                        ))}

                      {/* Loss column */}
                      {mediumPriorityColumns
                        .filter(col => col.accessorKey === 'losses')
                        .map((column, index) => (
                          <div key={index} className="flex items-center gap-1">
                            <span className="text-[10px] uppercase opacity-70">{column.header}:</span>
                            {column.cell ? column.cell(item) : null}
                          </div>
                        ))}
                    </div>

                    {/* Played Games */}
                    <div className="flex items-center">
                      {mediumPriorityColumns
                        .filter(col => col.accessorKey === 'played')
                        .map((column, index) => (
                          <div key={index} className="flex items-center gap-1">
                            <span className="text-[10px] uppercase opacity-70">P:</span>
                            {column.cell ? column.cell(item) : null}
                          </div>
                        ))}
                    </div>

                    {/* Rating and World Cup Runs */}
                    <div className="flex space-x-2">
                      {/* Rating */}
                      {mediumPriorityColumns
                        .filter(col => col.accessorKey === 'rating')
                        .map((column, index) => (
                          <div key={index} className="flex items-center">
                            {column.cell ? column.cell(item) : null}
                          </div>
                        ))}

                      {/* World Cup Runs */}
                      {mediumPriorityColumns
                        .filter(col => col.accessorKey === 'worldCupRuns')
                        .map((column, index) => (
                          <div key={index} className="flex items-center">
                            {column.cell ? column.cell(item) : null}
                          </div>
                        ))}
                    </div>
                  </div>
                )}
              </div>

              {onRowClick && (
                <ChevronRight className="h-4 w-4 text-muted-foreground ml-2" />
              )}
            </div>
          </CardContent>
        </Card>
      ))}
    </div>
  );
}
