import React from "react";
import { cn } from "@/lib/utils";
import { Button, ButtonProps } from "@/components/ui/button";
import { LucideIcon } from "lucide-react";

interface EmptyStateProps {
  icon?: LucideIcon;
  title: string;
  description?: string;
  actionLabel?: string;
  actionIcon?: LucideIcon;
  actionVariant?: ButtonProps["variant"];
  actionHref?: string;
  onAction?: () => void;
  className?: string;
  iconClassName?: string;
  children?: React.ReactNode;
}

export function EmptyState({
  icon: Icon,
  title,
  description,
  actionLabel,
  actionIcon: ActionIcon,
  actionVariant = "default",
  actionHref,
  onAction,
  className,
  iconClassName,
  children,
}: EmptyStateProps) {
  return (
    <div
      className={cn(
        "flex flex-col items-center justify-center text-center p-8 rounded-xl border-2 border-dashed border-muted-foreground/20 bg-gradient-to-br from-muted/20 to-muted/10 hover:border-soccer-primary/30 hover:bg-soccer-primary/5 transition-all duration-300",
        className
      )}
      role="region"
      aria-label={title}
    >
      {Icon && (
        <div className={cn("mb-6", iconClassName)}>
          <div className="bg-gradient-to-br from-muted to-muted-foreground/20 p-4 rounded-full">
            <Icon
              className="h-8 w-8 text-muted-foreground opacity-70"
              aria-hidden="true"
            />
          </div>
        </div>
      )}
      <h3 className="text-xl font-semibold mb-3 text-foreground">{title}</h3>
      {description && (
        <p className="text-muted-foreground mb-8 max-w-md leading-relaxed">{description}</p>
      )}
      {children}
      {actionLabel && (
        <Button
          variant={actionVariant}
          onClick={onAction}
          {...(actionHref ? { asChild: true } : {})}
          className="mt-4 bg-gradient-to-r from-soccer-primary to-soccer-primary-light hover:from-soccer-primary-light hover:to-soccer-primary text-primary-foreground shadow-lg hover:shadow-xl hover:scale-105 transition-all duration-300"
        >
          {actionHref ? (
            <a href={actionHref} className="flex items-center">
              {ActionIcon && <ActionIcon className="mr-2 h-4 w-4" />}
              {actionLabel}
            </a>
          ) : (
            <>
              {ActionIcon && <ActionIcon className="mr-2 h-4 w-4" />}
              {actionLabel}
            </>
          )}
        </Button>
      )}
    </div>
  );
}
