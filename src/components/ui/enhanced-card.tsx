import React from "react";
import { cn } from "@/lib/utils";
import { <PERSON>, CardContent, CardDescription, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";

interface EnhancedCardProps {
  className?: string;
  children: React.ReactNode;
  interactive?: boolean;
  hoverable?: boolean;
  noScale?: boolean;
  variant?: "default" | "outline" | "secondary" | "primary" | "destructive";
}

interface EnhancedCardHeaderProps {
  className?: string;
  children: React.ReactNode;
}

interface EnhancedCardTitleProps {
  className?: string;
  children: React.ReactNode;
}

interface EnhancedCardDescriptionProps {
  className?: string;
  children: React.ReactNode;
}

interface EnhancedCardContentProps {
  className?: string;
  children: React.ReactNode;
}

interface EnhancedCardFooterProps {
  className?: string;
  children: React.ReactNode;
}

const EnhancedCard = ({
  className,
  children,
  interactive = false,
  hoverable = true,
  noScale = false,
  variant = "default",
}: EnhancedCardProps) => {
  const variantStyles = {
    default: "",
    outline: "border-2",
    secondary: "bg-secondary text-secondary-foreground",
    primary: "bg-primary/5 border-primary/20",
    destructive: "bg-destructive/5 border-destructive/20",
  };

  return (
    <Card
      interactive={interactive}
      hoverable={hoverable}
      noScale={noScale}
      variant={variant}
      className={cn(
        "overflow-hidden animate-fade-in",
        variantStyles[variant],
        className
      )}
    >
      {children}
    </Card>
  );
};

const EnhancedCardHeader = ({ className, children }: EnhancedCardHeaderProps) => {
  return <CardHeader className={cn("pb-3", className)}>{children}</CardHeader>;
};

const EnhancedCardTitle = ({ className, children }: EnhancedCardTitleProps) => {
  return <CardTitle className={cn("text-xl font-semibold", className)}>{children}</CardTitle>;
};

const EnhancedCardDescription = ({ className, children }: EnhancedCardDescriptionProps) => {
  return <CardDescription className={cn("text-sm text-muted-foreground", className)}>{children}</CardDescription>;
};

const EnhancedCardContent = ({ className, children }: EnhancedCardContentProps) => {
  return <CardContent className={cn("pt-0", className)}>{children}</CardContent>;
};

const EnhancedCardFooter = ({ className, children }: EnhancedCardFooterProps) => {
  return <CardFooter className={cn("flex flex-col gap-2", className)}>{children}</CardFooter>;
};

EnhancedCard.Header = EnhancedCardHeader;
EnhancedCard.Title = EnhancedCardTitle;
EnhancedCard.Description = EnhancedCardDescription;
EnhancedCard.Content = EnhancedCardContent;
EnhancedCard.Footer = EnhancedCardFooter;

export { EnhancedCard };
