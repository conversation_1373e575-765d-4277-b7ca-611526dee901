import * as React from "react";
import { format } from "date-fns";
import { Calendar as CalendarIcon } from "lucide-react";
import { cn } from "@/lib/utils";
import { Button } from "@/components/ui/button";
import { Calendar } from "@/components/ui/calendar";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { Label } from "@/components/ui/label";

export interface EnhancedDatePickerProps {
  date: Date | undefined;
  setDate: (date: Date | undefined) => void;
  label?: string;
  placeholder?: string;
  className?: string;
  disabled?: boolean;
  error?: string;
  helperText?: string;
  required?: boolean;
  id?: string;
  name?: string;
  fromDate?: Date;
  toDate?: Date;
}

export function EnhancedDatePicker({
  date,
  setDate,
  label,
  placeholder = "Select a date",
  className,
  disabled,
  error,
  helperText,
  required,
  id,
  name,
  fromDate,
  toDate,
}: EnhancedDatePickerProps) {
  const inputId = id || React.useId();
  
  return (
    <div className="space-y-2">
      {label && (
        <Label
          htmlFor={inputId}
          className={cn(
            "text-sm font-medium",
            error && "text-destructive"
          )}
        >
          {label}
          {required && <span className="text-destructive ml-1">*</span>}
        </Label>
      )}
      
      <Popover>
        <PopoverTrigger asChild>
          <Button
            id={inputId}
            name={name}
            variant="outline"
            className={cn(
              "w-full justify-start text-left font-normal",
              !date && "text-muted-foreground",
              error && "border-destructive",
              className
            )}
            disabled={disabled}
          >
            <CalendarIcon className="mr-2 h-4 w-4" />
            {date ? format(date, "PPP") : <span>{placeholder}</span>}
          </Button>
        </PopoverTrigger>
        <PopoverContent className="w-auto p-0" align="start">
          <Calendar
            mode="single"
            selected={date}
            onSelect={setDate}
            disabled={disabled}
            initialFocus
            fromDate={fromDate}
            toDate={toDate}
          />
          <div className="p-3 border-t border-border flex justify-between">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setDate(undefined)}
              disabled={!date}
            >
              Clear
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={() => setDate(new Date())}
            >
              Today
            </Button>
          </div>
        </PopoverContent>
      </Popover>
      
      {error ? (
        <p className="text-xs text-destructive animate-in fade-in-50">
          {error}
        </p>
      ) : helperText ? (
        <p className="text-xs text-muted-foreground">
          {helperText}
        </p>
      ) : null}
    </div>
  );
}
