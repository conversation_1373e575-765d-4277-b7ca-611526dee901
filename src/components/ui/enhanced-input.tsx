import * as React from "react";
import { cn } from "@/lib/utils";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { AlertCircle, CheckCircle2 } from "lucide-react";

export interface EnhancedInputProps
  extends React.InputHTMLAttributes<HTMLInputElement> {
  label?: string;
  helperText?: string;
  error?: string;
  success?: boolean;
  startIcon?: React.ReactNode;
  endIcon?: React.ReactNode;
  containerClassName?: string;
  labelClassName?: string;
  helperTextClassName?: string;
  errorClassName?: string;
}

const EnhancedInput = React.forwardRef<HTMLInputElement, EnhancedInputProps>(
  (
    {
      className,
      label,
      helperText,
      error,
      success,
      startIcon,
      endIcon,
      containerClassName,
      labelClassName,
      helperTextClassName,
      errorClassName,
      id,
      ...props
    },
    ref
  ) => {
    const inputId = id || React.useId();
    
    return (
      <div className={cn("space-y-2 animate-fade-in", containerClassName)}>
        {label && (
          <Label
            htmlFor={inputId}
            className={cn(
              "text-sm font-medium animate-smooth",
              error && "text-destructive",
              labelClassName
            )}
          >
            {label}
          </Label>
        )}

        <div className="relative group">
          {startIcon && (
            <div className="absolute left-3 top-1/2 -translate-y-1/2 text-muted-foreground group-focus-within:text-soccer-primary animate-smooth">
              {startIcon}
            </div>
          )}

          <Input
            id={inputId}
            className={cn(
              startIcon && "pl-10",
              endIcon && "pr-10",
              error && "border-destructive focus-visible:ring-destructive/30 animate-pulse",
              success && "border-green-500 focus-visible:ring-green-500/30",
              className
            )}
            ref={ref}
            aria-invalid={!!error}
            aria-describedby={
              error
                ? `${inputId}-error`
                : helperText
                ? `${inputId}-helper-text`
                : undefined
            }
            {...props}
          />
          
          {(endIcon || error || success) && (
            <div className="absolute right-3 top-1/2 -translate-y-1/2">
              {error ? (
                <AlertCircle className="h-4 w-4 text-destructive" />
              ) : success ? (
                <CheckCircle2 className="h-4 w-4 text-green-500" />
              ) : (
                endIcon
              )}
            </div>
          )}
        </div>
        
        {error ? (
          <p
            id={`${inputId}-error`}
            className={cn(
              "text-xs text-destructive animate-in fade-in-50",
              errorClassName
            )}
          >
            {error}
          </p>
        ) : helperText ? (
          <p
            id={`${inputId}-helper-text`}
            className={cn(
              "text-xs text-muted-foreground",
              helperTextClassName
            )}
          >
            {helperText}
          </p>
        ) : null}
      </div>
    );
  }
);

EnhancedInput.displayName = "EnhancedInput";

export { EnhancedInput };
