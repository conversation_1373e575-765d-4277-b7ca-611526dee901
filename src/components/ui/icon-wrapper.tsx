import React from 'react';
import * as LucideIcons from 'lucide-react';

// This component ensures proper loading of Lucide icons in production
export function Icon({ name, ...props }: { name: string } & React.SVGProps<SVGSVGElement>) {
  const IconComponent = LucideIcons[name as keyof typeof LucideIcons];
  
  if (!IconComponent) {
    console.warn(`Icon "${name}" not found in lucide-react`);
    return null;
  }
  
  return <IconComponent {...props} />;
}
