import * as React from "react";
import { cn } from "@/lib/utils";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";

interface Column<T> {
  header: string;
  accessorKey: keyof T;
  cell?: (item: T) => React.ReactNode;
  priority?: "high" | "medium" | "low";
  meta?: {
    align?: "left" | "center" | "right";
    isBadge?: boolean;
    badgeVariant?: "default" | "secondary" | "outline" | "destructive";
    isNumeric?: boolean;
    width?: string;
  };
}

interface MobileTableProps<T> {
  data: T[];
  columns: Column<T>[];
  className?: string;
  onRowClick?: (item: T) => void;
  isLoading?: boolean;
  emptyState?: React.ReactNode;
  keyExtractor: (item: T) => string | number;
  rowClassName?: string | ((item: T) => string);
}

export function MobileTable<T>({
  data,
  columns,
  className,
  onRowClick,
  isLoading = false,
  emptyState,
  keyExtractor,
  rowClassName,
}: MobileTableProps<T>) {
  // Filter columns by priority for mobile view
  const visibleColumns = columns.filter(col => col.priority === "high" || col.priority === undefined);

  if (isLoading) {
    return (
      <div className="flex items-center justify-center py-8">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-soccer-primary" />
      </div>
    );
  }

  if (data.length === 0 && emptyState) {
    return <>{emptyState}</>;
  }

  return (
    <div className={cn("w-full overflow-hidden rounded-lg border border-border", className)}>
      <Table className="w-full">
        <TableHeader>
          <TableRow className="border-b bg-muted/30 dark:bg-muted/10">
            {visibleColumns.map((column, index) => (
              <TableHead
                key={index}
                className={cn(
                  "py-2 px-2 text-xs font-semibold text-foreground",
                  column.meta?.align === "center" && "text-center",
                  column.meta?.align === "right" && "text-right",
                  column.meta?.width && column.meta.width,
                  index % 2 === 0 ? "bg-soccer-primary/5 dark:bg-soccer-primary/10" : "bg-muted/20 dark:bg-muted/5"
                )}
              >
                {column.header}
              </TableHead>
            ))}
          </TableRow>
        </TableHeader>
        <TableBody>
          {data.map((item, rowIndex) => (
            <TableRow
              key={keyExtractor(item)}
              className={cn(
                "border-b transition-colors duration-200",
                onRowClick && "cursor-pointer hover:bg-soccer-primary/5 dark:hover:bg-soccer-primary/10",
                rowIndex % 2 === 0 ? "bg-background" : "bg-muted/20 dark:bg-muted/5",
                typeof rowClassName === "function" ? rowClassName(item) : rowClassName
              )}
              onClick={() => onRowClick && onRowClick(item)}
            >
              {visibleColumns.map((column, index) => (
                <TableCell
                  key={index}
                  className={cn(
                    "py-2 px-2 text-xs font-medium",
                    column.meta?.align === "center" && "text-center",
                    column.meta?.align === "right" && "text-right",
                    index % 2 === 0 ? "bg-soccer-primary/3 dark:bg-soccer-primary/5" : ""
                  )}
                >
                  {column.cell ? (
                    column.cell(item)
                  ) : (
                    <span className="text-foreground">{String(item[column.accessorKey])}</span>
                  )}
                </TableCell>
              ))}
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </div>
  );
}
