import React from 'react';
import { useOffline } from '@/context/OfflineContext';
import { cn } from '@/lib/utils';
import { Wifi, WifiOff, RefreshCw, CheckCircle2 } from 'lucide-react';
import { Button } from '@/components/ui/button';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip';

interface OfflineIndicatorProps {
  className?: string;
  showSyncStatus?: boolean;
}

export function OfflineIndicator({
  className,
  showSyncStatus = true,
}: OfflineIndicatorProps) {
  const { isOffline, syncStatus, syncNow } = useOffline();

  const handleSyncClick = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    syncNow();
  };

  // Format last sync time
  const formatLastSync = () => {
    if (!syncStatus.lastSync) return 'Never';
    
    const now = new Date();
    const lastSync = new Date(syncStatus.lastSync);
    const diffMs = now.getTime() - lastSync.getTime();
    
    // Less than a minute
    if (diffMs < 60000) {
      return 'Just now';
    }
    
    // Less than an hour
    if (diffMs < 3600000) {
      const minutes = Math.floor(diffMs / 60000);
      return `${minutes} minute${minutes !== 1 ? 's' : ''} ago`;
    }
    
    // Less than a day
    if (diffMs < 86400000) {
      const hours = Math.floor(diffMs / 3600000);
      return `${hours} hour${hours !== 1 ? 's' : ''} ago`;
    }
    
    // Format as date
    return lastSync.toLocaleDateString();
  };

  return (
    <div
      className={cn(
        'flex items-center gap-2 text-sm',
        isOffline ? 'text-destructive' : 'text-muted-foreground',
        className
      )}
      role="status"
      aria-live="polite"
    >
      <TooltipProvider>
        <Tooltip>
          <TooltipTrigger asChild>
            <div className="flex items-center gap-1.5">
              {isOffline ? (
                <WifiOff className="h-4 w-4" />
              ) : (
                <Wifi className="h-4 w-4" />
              )}
              <span className="hidden sm:inline">
                {isOffline ? 'Offline' : 'Online'}
              </span>
            </div>
          </TooltipTrigger>
          <TooltipContent side="bottom">
            <p>
              {isOffline
                ? 'You are currently offline. Changes will be synced when you reconnect.'
                : 'You are connected to the server.'}
            </p>
            {showSyncStatus && syncStatus.pending > 0 && (
              <p className="mt-1 text-xs">
                {syncStatus.pending} item{syncStatus.pending !== 1 ? 's' : ''} pending sync
              </p>
            )}
            {showSyncStatus && syncStatus.lastSync && (
              <p className="mt-1 text-xs">Last synced: {formatLastSync()}</p>
            )}
          </TooltipContent>
        </Tooltip>
      </TooltipProvider>

      {showSyncStatus && syncStatus.pending > 0 && (
        <div className="flex items-center gap-1">
          <span className="text-xs">
            {syncStatus.pending} pending
          </span>
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <Button
                  variant="ghost"
                  size="icon"
                  className="h-6 w-6"
                  onClick={handleSyncClick}
                  disabled={isOffline || syncStatus.syncing}
                  aria-label="Sync now"
                >
                  <RefreshCw
                    className={cn(
                      'h-3 w-3',
                      syncStatus.syncing && 'animate-spin'
                    )}
                  />
                </Button>
              </TooltipTrigger>
              <TooltipContent side="bottom">
                <p>
                  {isOffline
                    ? 'Cannot sync while offline'
                    : syncStatus.syncing
                    ? 'Syncing...'
                    : 'Sync now'}
                </p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
        </div>
      )}

      {showSyncStatus && !isOffline && syncStatus.pending === 0 && syncStatus.lastSync && (
        <TooltipProvider>
          <Tooltip>
            <TooltipTrigger asChild>
              <div className="flex items-center gap-1 text-xs text-muted-foreground">
                <CheckCircle2 className="h-3 w-3 text-primary" />
                <span className="hidden sm:inline">Synced</span>
              </div>
            </TooltipTrigger>
            <TooltipContent side="bottom">
              <p>All changes are synced</p>
              <p className="mt-1 text-xs">Last synced: {formatLastSync()}</p>
            </TooltipContent>
          </Tooltip>
        </TooltipProvider>
      )}
    </div>
  );
}
