import * as React from "react";
import { cn } from "@/lib/utils";
import { ChevronDown, ChevronUp, ChevronsUpDown } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";

interface Column<T> {
  header: string;
  accessorKey: keyof T;
  cell?: (item: T) => React.ReactNode;
  sortable?: boolean;
  className?: string;
  meta?: {
    align?: "left" | "center" | "right";
    width?: string;
    minWidth?: string;
    maxWidth?: string;
    isNumeric?: boolean;
    isBadge?: boolean;
    badgeVariant?: "default" | "secondary" | "outline" | "destructive";
  };
}

interface ResponsiveTableProps<T> {
  data: T[];
  columns: Column<T>[];
  className?: string;
  onRowClick?: (item: T) => void;
  isLoading?: boolean;
  emptyState?: React.ReactNode;
  keyExtractor: (item: T) => string | number;
  defaultSortColumn?: keyof T;
  defaultSortDirection?: "asc" | "desc";
  cardView?: boolean;
  cardViewBreakpoint?: "sm" | "md" | "lg" | "xl" | "2xl";
  rowClassName?: string | ((item: T) => string);
  stickyHeader?: boolean;
  pagination?: {
    pageSize: number;
    pageIndex: number;
    totalPages: number;
    onPageChange: (page: number) => void;
  };
}

export function ResponsiveTable<T>({
  data,
  columns,
  className,
  onRowClick,
  isLoading = false,
  emptyState,
  keyExtractor,
  defaultSortColumn,
  defaultSortDirection = "asc",
  cardView = true,
  cardViewBreakpoint = "md",
  rowClassName,
  stickyHeader = false,
  pagination,
}: ResponsiveTableProps<T>) {
  const [sortColumn, setSortColumn] = React.useState<keyof T | undefined>(
    defaultSortColumn
  );
  const [sortDirection, setSortDirection] = React.useState<"asc" | "desc">(
    defaultSortDirection
  );

  const handleSort = (column: keyof T) => {
    if (sortColumn === column) {
      setSortDirection(sortDirection === "asc" ? "desc" : "asc");
    } else {
      setSortColumn(column);
      setSortDirection("asc");
    }
  };

  const sortedData = React.useMemo(() => {
    if (!sortColumn) return data;

    return [...data].sort((a, b) => {
      const aValue = a[sortColumn];
      const bValue = b[sortColumn];

      if (aValue === bValue) return 0;

      const comparison = aValue < bValue ? -1 : 1;
      return sortDirection === "asc" ? comparison : -comparison;
    });
  }, [data, sortColumn, sortDirection]);

  const paginatedData = React.useMemo(() => {
    if (!pagination) return sortedData;

    const { pageIndex, pageSize } = pagination;
    const start = pageIndex * pageSize;
    return sortedData.slice(start, start + pageSize);
  }, [sortedData, pagination]);

  const renderSortIcon = (column: keyof T) => {
    if (sortColumn !== column) return <ChevronsUpDown className="h-4 w-4" />;
    return sortDirection === "asc" ? (
      <ChevronUp className="h-4 w-4" />
    ) : (
      <ChevronDown className="h-4 w-4" />
    );
  };

  if (isLoading) {
    return (
      <div className="w-full flex items-center justify-center py-8">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary" />
      </div>
    );
  }

  if (data.length === 0) {
    return (
      <div className="w-full py-8">
        {emptyState || (
          <div className="text-center">
            <p className="text-muted-foreground">No data available</p>
          </div>
        )}
      </div>
    );
  }

  // Table view
  const tableView = (
    <div className={cn("w-full overflow-auto", className)}>
      <table className="w-full caption-bottom text-sm">
        <thead className={cn(stickyHeader && "sticky top-0 z-10 bg-background")}>
          <tr className="border-b transition-colors hover:bg-muted/50">
            {columns.map((column, index) => (
              <th
                key={index}
                className={cn(
                  "h-12 px-4 text-left align-middle font-medium text-muted-foreground",
                  column.meta?.align === "center" && "text-center",
                  column.meta?.align === "right" && "text-right",
                  column.meta?.width && `w-[${column.meta.width}]`,
                  column.meta?.minWidth && `min-w-[${column.meta.minWidth}]`,
                  column.meta?.maxWidth && `max-w-[${column.meta.maxWidth}]`,
                  column.className
                )}
              >
                {column.sortable ? (
                  <Button
                    variant="ghost"
                    size="sm"
                    className="-ml-3 h-8 data-[state=open]:bg-accent"
                    onClick={() => handleSort(column.accessorKey)}
                  >
                    <span>{column.header}</span>
                    {renderSortIcon(column.accessorKey)}
                  </Button>
                ) : (
                  <div>{column.header}</div>
                )}
              </th>
            ))}
          </tr>
        </thead>
        <tbody>
          {paginatedData.map((row) => (
            <tr
              key={keyExtractor(row)}
              className={cn(
                "border-b transition-colors hover:bg-muted/50",
                onRowClick && "cursor-pointer",
                typeof rowClassName === "function"
                  ? rowClassName(row)
                  : rowClassName
              )}
              onClick={() => onRowClick && onRowClick(row)}
            >
              {columns.map((column, cellIndex) => (
                <td
                  key={cellIndex}
                  className={cn(
                    "p-4 align-middle",
                    column.meta?.align === "center" && "text-center",
                    column.meta?.align === "right" && "text-right",
                    column.meta?.isNumeric && "font-mono",
                    column.className
                  )}
                >
                  {column.cell
                    ? column.cell(row)
                    : column.meta?.isBadge
                    ? (
                        <Badge variant={column.meta.badgeVariant}>
                          {String(row[column.accessorKey])}
                        </Badge>
                      )
                    : String(row[column.accessorKey])}
                </td>
              ))}
            </tr>
          ))}
        </tbody>
      </table>

      {pagination && (
        <div className="flex items-center justify-end space-x-2 py-4">
          <Button
            variant="outline"
            size="sm"
            onClick={() => pagination.onPageChange(pagination.pageIndex - 1)}
            disabled={pagination.pageIndex === 0}
          >
            Previous
          </Button>
          <div className="text-sm text-muted-foreground">
            Page {pagination.pageIndex + 1} of {pagination.totalPages}
          </div>
          <Button
            variant="outline"
            size="sm"
            onClick={() => pagination.onPageChange(pagination.pageIndex + 1)}
            disabled={pagination.pageIndex === pagination.totalPages - 1}
          >
            Next
          </Button>
        </div>
      )}
    </div>
  );

  // Card view for mobile
  const cardViewComponent = (
    <div className="grid grid-cols-1 gap-4">
      {paginatedData.map((row) => (
        <Card
          key={keyExtractor(row)}
          className={cn(
            "overflow-hidden",
            onRowClick && "cursor-pointer",
            typeof rowClassName === "function"
              ? rowClassName(row)
              : rowClassName
          )}
          onClick={() => onRowClick && onRowClick(row)}
        >
          <div className="p-4 space-y-3">
            {columns.map((column, index) => (
              <div key={index} className="flex justify-between items-center">
                <div className="text-sm font-medium text-muted-foreground">
                  {column.header}
                </div>
                <div
                  className={cn(
                    "text-sm",
                    column.meta?.isNumeric && "font-mono",
                    column.className
                  )}
                >
                  {column.cell
                    ? column.cell(row)
                    : column.meta?.isBadge
                    ? (
                        <Badge variant={column.meta.badgeVariant}>
                          {String(row[column.accessorKey])}
                        </Badge>
                      )
                    : String(row[column.accessorKey])}
                </div>
              </div>
            ))}
          </div>
        </Card>
      ))}

      {pagination && (
        <div className="flex items-center justify-center space-x-2 py-4">
          <Button
            variant="outline"
            size="sm"
            onClick={() => pagination.onPageChange(pagination.pageIndex - 1)}
            disabled={pagination.pageIndex === 0}
          >
            Previous
          </Button>
          <div className="text-sm text-muted-foreground">
            Page {pagination.pageIndex + 1} of {pagination.totalPages}
          </div>
          <Button
            variant="outline"
            size="sm"
            onClick={() => pagination.onPageChange(pagination.pageIndex + 1)}
            disabled={pagination.pageIndex === pagination.totalPages - 1}
          >
            Next
          </Button>
        </div>
      )}
    </div>
  );

  if (cardView) {
    return (
      <>
        <div className={`hidden ${cardViewBreakpoint}:block`}>{tableView}</div>
        <div className={`block ${cardViewBreakpoint}:hidden`}>
          {cardViewComponent}
        </div>
      </>
    );
  }

  return tableView;
}
