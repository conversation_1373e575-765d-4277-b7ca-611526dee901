import { useToast } from "@/hooks/use-toast"
import {
  EnhancedToast,
  ToastProvider,
  ToastViewport,
} from "@/components/ui/toast"

export function Toaster() {
  const { toasts } = useToast()

  return (
    <ToastProvider>
      {toasts.map(function ({ id, title, description, action, variant, ...props }) {
        return (
          <EnhancedToast
            key={id}
            title={title}
            description={description}
            action={action}
            variant={variant as "default" | "destructive" | "success" | "warning" | "info"}
            {...props}
          />
        )
      })}
      <ToastViewport />
    </ToastProvider>
  )
}
