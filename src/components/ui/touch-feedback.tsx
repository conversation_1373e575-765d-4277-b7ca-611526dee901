import React, { useRef, useCallback, useState } from 'react';
import { cn } from '@/lib/utils';

interface TouchFeedbackProps {
  children: React.ReactNode;
  className?: string;
  disabled?: boolean;
  onTap?: () => void;
  onLongPress?: () => void;
  ripple?: boolean;
  scale?: boolean;
  longPressDelay?: number;
}

interface RippleEffect {
  id: number;
  x: number;
  y: number;
  size: number;
}

export const TouchFeedback: React.FC<TouchFeedbackProps> = ({
  children,
  className,
  disabled = false,
  onTap,
  onLongPress,
  ripple = true,
  scale = true,
  longPressDelay = 500,
}) => {
  const elementRef = useRef<HTMLDivElement>(null);
  const [ripples, setRipples] = useState<RippleEffect[]>([]);
  const [isPressed, setIsPressed] = useState(false);
  const longPressTimer = useRef<NodeJS.Timeout | null>(null);
  const rippleCounter = useRef(0);

  const createRipple = useCallback((e: React.TouchEvent | React.MouseEvent) => {
    if (!ripple || !elementRef.current) return;

    const rect = elementRef.current.getBoundingClientRect();
    const size = Math.max(rect.width, rect.height);
    const x = ('touches' in e ? e.touches[0].clientX : e.clientX) - rect.left - size / 2;
    const y = ('touches' in e ? e.touches[0].clientY : e.clientY) - rect.top - size / 2;

    const newRipple: RippleEffect = {
      id: rippleCounter.current++,
      x,
      y,
      size,
    };

    setRipples(prev => [...prev, newRipple]);

    // Remove ripple after animation
    setTimeout(() => {
      setRipples(prev => prev.filter(r => r.id !== newRipple.id));
    }, 600);
  }, [ripple]);

  const handleStart = useCallback((e: React.TouchEvent | React.MouseEvent) => {
    if (disabled) return;

    setIsPressed(true);
    createRipple(e);

    if (onLongPress) {
      longPressTimer.current = setTimeout(() => {
        onLongPress();
        setIsPressed(false);
      }, longPressDelay);
    }
  }, [disabled, createRipple, onLongPress, longPressDelay]);

  const handleEnd = useCallback(() => {
    if (disabled) return;

    setIsPressed(false);

    if (longPressTimer.current) {
      clearTimeout(longPressTimer.current);
      longPressTimer.current = null;
      onTap?.();
    }
  }, [disabled, onTap]);

  const handleCancel = useCallback(() => {
    setIsPressed(false);
    if (longPressTimer.current) {
      clearTimeout(longPressTimer.current);
      longPressTimer.current = null;
    }
  }, []);

  return (
    <div
      ref={elementRef}
      className={cn(
        'relative overflow-hidden select-none',
        scale && 'animate-smooth active:scale-95',
        disabled && 'opacity-50 pointer-events-none',
        className
      )}
      onTouchStart={handleStart}
      onTouchEnd={handleEnd}
      onTouchCancel={handleCancel}
      onMouseDown={handleStart}
      onMouseUp={handleEnd}
      onMouseLeave={handleCancel}
      style={{
        WebkitTapHighlightColor: 'transparent',
        WebkitTouchCallout: 'none',
        WebkitUserSelect: 'none',
        userSelect: 'none',
      }}
    >
      {children}
      
      {/* Ripple effects */}
      {ripples.map(ripple => (
        <span
          key={ripple.id}
          className="absolute pointer-events-none rounded-full bg-white/20 animate-ping"
          style={{
            left: ripple.x,
            top: ripple.y,
            width: ripple.size,
            height: ripple.size,
            animationDuration: '600ms',
            animationTimingFunction: 'cubic-bezier(0.4, 0, 0.2, 1)',
          }}
        />
      ))}
      
      {/* Press overlay */}
      {isPressed && (
        <div className="absolute inset-0 bg-black/5 animate-fade-in" />
      )}
    </div>
  );
};

// Hook for touch gestures with haptic feedback
export const useTouchGestures = () => {
  const triggerHaptic = useCallback((type: 'light' | 'medium' | 'heavy' = 'light') => {
    if ('vibrate' in navigator) {
      const patterns = {
        light: [10],
        medium: [20],
        heavy: [30],
      };
      navigator.vibrate(patterns[type]);
    }
  }, []);

  const handleTouchFeedback = useCallback((callback?: () => void, haptic: 'light' | 'medium' | 'heavy' = 'light') => {
    return () => {
      triggerHaptic(haptic);
      callback?.();
    };
  }, [triggerHaptic]);

  return {
    triggerHaptic,
    handleTouchFeedback,
  };
};

export default TouchFeedback;
