import React, { useEffect, useRef, useState } from 'react';
import { cn } from '@/lib/utils';

interface VirtualizedListProps<T> {
  items: T[];
  renderItem: (item: T, index: number) => React.ReactNode;
  itemHeight: number;
  className?: string;
  overscan?: number;
  onEndReached?: () => void;
  endReachedThreshold?: number;
  emptyComponent?: React.ReactNode;
  loadingComponent?: React.ReactNode;
  isLoading?: boolean;
}

export function VirtualizedList<T>({
  items,
  renderItem,
  itemHeight,
  className,
  overscan = 5,
  onEndReached,
  endReachedThreshold = 200,
  emptyComponent,
  loadingComponent,
  isLoading = false,
}: VirtualizedListProps<T>) {
  const containerRef = useRef<HTMLDivElement>(null);
  const [visibleRange, setVisibleRange] = useState({ start: 0, end: 0 });
  const [containerHeight, setContainerHeight] = useState(0);
  const [scrollTop, setScrollTop] = useState(0);
  const totalHeight = items.length * itemHeight;
  const lastEndReachedRef = useRef(false);

  // Calculate visible range based on scroll position
  useEffect(() => {
    if (!containerRef.current) return;

    const calculateVisibleRange = () => {
      const container = containerRef.current;
      if (!container) return;

      const currentScrollTop = container.scrollTop;
      setScrollTop(currentScrollTop);

      const visibleHeight = container.clientHeight;
      setContainerHeight(visibleHeight);

      const start = Math.max(0, Math.floor(currentScrollTop / itemHeight) - overscan);
      const end = Math.min(
        items.length - 1,
        Math.ceil((currentScrollTop + visibleHeight) / itemHeight) + overscan
      );

      setVisibleRange({ start, end });

      // Check if we've reached the end
      if (
        onEndReached &&
        !lastEndReachedRef.current &&
        totalHeight - currentScrollTop - visibleHeight < endReachedThreshold
      ) {
        lastEndReachedRef.current = true;
        onEndReached();
      } else if (totalHeight - currentScrollTop - visibleHeight >= endReachedThreshold) {
        lastEndReachedRef.current = false;
      }
    };

    calculateVisibleRange();

    const handleScroll = () => {
      calculateVisibleRange();
    };

    const container = containerRef.current;
    container.addEventListener('scroll', handleScroll);
    window.addEventListener('resize', calculateVisibleRange);

    return () => {
      container.removeEventListener('scroll', handleScroll);
      window.removeEventListener('resize', calculateVisibleRange);
    };
  }, [itemHeight, items.length, overscan, onEndReached, endReachedThreshold, totalHeight]);

  // Reset scroll position when items change significantly
  useEffect(() => {
    lastEndReachedRef.current = false;
  }, [items.length]);

  // Render only visible items
  const visibleItems = [];
  for (let i = visibleRange.start; i <= visibleRange.end; i++) {
    if (i >= 0 && i < items.length) {
      visibleItems.push(
        <div
          key={i}
          style={{
            position: 'absolute',
            top: 0,
            transform: `translateY(${i * itemHeight}px)`,
            width: '100%',
            height: itemHeight,
          }}
          data-index={i}
        >
          {renderItem(items[i], i)}
        </div>
      );
    }
  }

  if (isLoading && loadingComponent) {
    return <>{loadingComponent}</>;
  }

  if (items.length === 0 && emptyComponent) {
    return <>{emptyComponent}</>;
  }

  return (
    <div
      ref={containerRef}
      className={cn('relative overflow-auto', className)}
      style={{ height: '100%', willChange: 'transform' }}
      role="list"
      aria-label="Virtualized list"
    >
      <div style={{ height: totalHeight, position: 'relative' }}>
        {visibleItems}
      </div>
    </div>
  );
}
