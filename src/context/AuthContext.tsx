import React, { createContext, useContext, useState, useEffect } from 'react';
import { supabase, AuthUser, AuthSession, handleAuthRedirect } from '@/lib/supabase';
import { useNavigate, useLocation } from 'react-router-dom';
// We'll use direct Supabase calls instead of useGroup to avoid circular dependencies

interface AuthContextType {
  isAuthenticated: boolean;
  user: AuthUser | null;
  session: AuthSession | null;
  isLoading: boolean;
  error: string | null;
  login: (email: string, password: string, rememberMe?: boolean) => Promise<void>;
  logout: () => Promise<void>;
  signUp: (email: string, password: string, fullName: string) => Promise<void>;
  resetPassword: (email: string) => Promise<void>;
  updateProfile: (updates: Partial<AuthUser['user_metadata']>) => Promise<void>;
  signInWithGoogle: () => Promise<void>;
  hasLoadedGroups: boolean;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const AuthProvider = ({ children }: { children: React.ReactNode }) => {
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [user, setUser] = useState<AuthUser | null>(null);
  const [session, setSession] = useState<AuthSession | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const navigate = useNavigate();
  const location = useLocation();
  // We'll implement our own group fetching logic to avoid circular dependencies
  const [hasLoadedGroups, setHasLoadedGroups] = useState<boolean>(false);

  // Direct group fetching function to avoid circular dependencies
  const fetchGroupsDirectly = async () => {
    // Get the current user ID directly from Supabase
    const { data } = await supabase.auth.getSession();
    const userId = data?.session?.user?.id;

    if (!userId) {
      console.log('No user ID available to fetch groups');
      setHasLoadedGroups(true); // Mark as loaded even if no user
      return;
    }

    setHasLoadedGroups(false);
    console.log('Directly fetching groups for user:', userId);

    try {
      // Fetch groups where user is a member
      const { data: memberGroups, error: memberError } = await supabase
        .from('group_members')
        .select('group_id, friend_groups(id, name, created_by, created_at)')
        .eq('user_id', userId);

      if (memberError) throw memberError;

      // Fetch groups where user is the creator
      const { data: createdGroups, error: creatorError } = await supabase
        .from('friend_groups')
        .select('id, name, created_by, created_at')
        .eq('created_by', userId);

      if (creatorError) throw creatorError;

      // Process and combine the results
      const memberGroupsData = memberGroups
        .filter(item => item.friend_groups) // Filter out any null entries
        .map(item => ({
          id: item.friend_groups.id,
          name: item.friend_groups.name,
          created_by: item.friend_groups.created_by,
          created_at: item.friend_groups.created_at
        }));

      // Combine and deduplicate
      const allGroups = [...memberGroupsData];

      // Add created groups that aren't already in the list
      createdGroups.forEach(group => {
        if (!allGroups.some(g => g.id === group.id)) {
          allGroups.push(group);
        }
      });

      console.log('Fetched groups directly:', allGroups.length);

      if (allGroups.length > 0) {
        // Store in localStorage for caching
        localStorage.setItem('cachedGroups', JSON.stringify(allGroups));

        // Set selected group if needed
        const selectedId = localStorage.getItem('selectedGroupId');
        const selectedGroup = selectedId ? allGroups.find(g => g.id === selectedId) : null;

        if (!selectedGroup && allGroups.length > 0) {
          // If no valid selection, use the first group
          localStorage.setItem('selectedGroupId', allGroups[0].id);
        }
      } else {
        // If no groups found, check cache
        const cachedData = localStorage.getItem('cachedGroups');
        if (cachedData) {
          const parsedData = JSON.parse(cachedData);
          if (parsedData.length > 0) {
            console.log('Using cached groups:', parsedData.length);
          }
        }
      }

      setHasLoadedGroups(true);
    } catch (err) {
      console.error('Error fetching groups directly:', err);
      setHasLoadedGroups(true); // Still mark as loaded even on error
    }
  };

  // Initialize auth state
  useEffect(() => {
    const initializeAuth = async () => {
      try {
        console.log('Initializing auth state...');

        // Check for OAuth redirects first
        const redirectSession = await handleAuthRedirect();
        if (redirectSession) {
          console.log('Found session from redirect');
          setSession(redirectSession);
          setUser(redirectSession.user);
          setIsAuthenticated(true);
          return;
        }

        // Otherwise get the regular session
        const { data: { session } } = await supabase.auth.getSession();
        if (session) {
          console.log('Found existing session:', session);
          setSession(session);
          setUser(session.user);
          setIsAuthenticated(true);

          // If we're on the login or signup page and have a session, fetch groups and check for group selection
          const handleInitialRedirect = async () => {
            // Fetch groups first
            console.log('Fetching groups during initialization...');
            await fetchGroupsDirectly();
            console.log('Groups loaded during initialization:', hasLoadedGroups);

            const selectedGroupId = localStorage.getItem('selectedGroupId');

            // Add a flag in sessionStorage to prevent redirect loops
            const hasRedirected = sessionStorage.getItem('hasRedirected');

            if ((location.pathname === '/' || location.pathname === '/signup') && !hasRedirected) {
              // Set the flag before redirecting
              sessionStorage.setItem('hasRedirected', 'true');

              if (selectedGroupId) {
                navigate('/dashboard');
              } else {
                navigate('/group-selection');
              }
            } else if (hasRedirected) {
              // Clear the flag after a successful redirect
              sessionStorage.removeItem('hasRedirected');
            }
          };

          handleInitialRedirect();
        } else {
          console.log('No existing session found');
          // If we're on a protected route and have no session, redirect to login
          // Skip redirect for shared routes and shared links
          if (location.pathname !== '/' &&
              location.pathname !== '/signup' &&
              !location.pathname.startsWith('/shared/') &&
              !location.pathname.startsWith('/s/')) {
            navigate('/', { replace: true });
          }
        }
      } catch (err) {
        console.error('Error initializing auth:', err);
        setError(err instanceof Error ? err.message : 'Failed to initialize auth');
      } finally {
        setIsLoading(false);
      }
    };

    initializeAuth();

    // Listen for auth state changes
    const { data: { subscription } } = supabase.auth.onAuthStateChange(
      async (event, session) => {
        console.log('Auth state changed:', event, session);
        if (session) {
          setSession(session);
          setUser(session.user);
          setIsAuthenticated(true);

          // On successful sign in, fetch groups and then check for group selection
          const handleRedirect = async () => {
            // Fetch groups first
            console.log('Fetching groups after auth state change...');
            await fetchGroupsDirectly();
            console.log('Groups loaded:', hasLoadedGroups);

            const selectedGroupId = localStorage.getItem('selectedGroupId');

            // Add a flag in sessionStorage to prevent redirect loops
            const hasRedirected = sessionStorage.getItem('hasRedirected');

            if ((location.pathname === '/' || location.pathname === '/signup') && !hasRedirected) {
              // Set the flag before redirecting
              sessionStorage.setItem('hasRedirected', 'true');

              if (selectedGroupId) {
                navigate('/dashboard');
              } else {
                navigate('/group-selection');
              }
            } else if (hasRedirected) {
              // Clear the flag after a successful redirect
              sessionStorage.removeItem('hasRedirected');
            }
          };

          handleRedirect();
        } else {
          setSession(null);
          setUser(null);
          setIsAuthenticated(false);

          // On sign out, redirect to login if not already there
          // Skip redirect for shared routes and shared links
          if (location.pathname !== '/' &&
              location.pathname !== '/signup' &&
              !location.pathname.startsWith('/shared/') &&
              !location.pathname.startsWith('/s/')) {
            navigate('/', { replace: true });
          }
        }
        setIsLoading(false);
      }
    );

    return () => {
      subscription.unsubscribe();
    };
  }, [navigate, location.pathname]);

  const login = async (email: string, password: string, rememberMe = false) => {
    try {
      console.log('Attempting login with:', { email, rememberMe });
      setIsLoading(true);
      setError(null);
      const { error } = await supabase.auth.signInWithPassword({
        email,
        password
      });
      if (error) {
        console.error('Login error:', error);
        throw error;
      }
      console.log('Login successful');

      // Fetch groups after successful login
      console.log('Fetching groups after login...');
      await fetchGroupsDirectly();
      console.log('Groups loaded:', hasLoadedGroups);

      const selectedGroupId = localStorage.getItem('selectedGroupId');
      if (selectedGroupId) {
        navigate('/dashboard');
      } else {
        navigate('/group-selection');
      }
    } catch (err) {
      console.error('Login failed:', err);
      setError(err instanceof Error ? err.message : 'Failed to login');
      throw err;
    } finally {
      setIsLoading(false);
    }
  };

  const logout = async () => {
    try {
      setIsLoading(true);
      setError(null);
      // Clear any stored group selection
      localStorage.removeItem('selectedGroupId');

      const { error } = await supabase.auth.signOut();
      if (error) throw error;
      navigate('/');
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to logout');
      throw err;
    } finally {
      setIsLoading(false);
    }
  };

  const signUp = async (email: string, password: string, fullName: string) => {
    try {
      setIsLoading(true);
      setError(null);
      const { error } = await supabase.auth.signUp({
        email,
        password,
        options: {
          data: {
            full_name: fullName,
          },
        },
      });
      if (error) throw error;
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to sign up');
      throw err;
    } finally {
      setIsLoading(false);
    }
  };

  const resetPassword = async (email: string) => {
    try {
      setIsLoading(true);
      setError(null);
      const { error } = await supabase.auth.resetPasswordForEmail(email);
      if (error) throw error;
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to reset password');
      throw err;
    } finally {
      setIsLoading(false);
    }
  };

  const updateProfile = async (updates: Partial<AuthUser['user_metadata']>) => {
    try {
      setIsLoading(true);
      setError(null);
      const { error } = await supabase.auth.updateUser({
        data: updates,
      });
      if (error) throw error;
      if (user) {
        setUser({
          ...user,
          user_metadata: {
            ...user.user_metadata,
            ...updates,
          },
        });
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to update profile');
      throw err;
    } finally {
      setIsLoading(false);
    }
  };

  const signInWithGoogle = async () => {
    try {
      console.log('Attempting Google sign in...');
      setIsLoading(true);
      setError(null);

      // Get the current URL for proper redirect
      const redirectUrl = `${window.location.origin}/dashboard`;
      console.log('Using redirect URL:', redirectUrl);

      const { data, error } = await supabase.auth.signInWithOAuth({
        provider: 'google',
        options: {
          redirectTo: redirectUrl,
          queryParams: {
            // Optional: Add additional scopes if needed
            // access_type: 'offline', // Get refresh token
            // prompt: 'consent',      // Force consent screen
          },
        },
      });

      if (error) {
        console.error('Google sign in error:', error);
        throw error;
      }

      // Log the URL that Supabase is redirecting to
      if (data?.url) {
        console.log('Redirecting to OAuth URL:', data.url);
      }

      console.log('Google sign in initiated successfully');
    } catch (err) {
      console.error('Google sign in failed:', err);
      // Provide more detailed error message
      let errorMessage = 'Failed to sign in with Google';

      if (err instanceof Error) {
        errorMessage = err.message;
        // Check for specific error types
        if (errorMessage.includes('invalid_client')) {
          errorMessage = 'Google OAuth client not properly configured. Please check your Supabase settings.';
        }
      }

      setError(errorMessage);
      throw err;
    } finally {
      setIsLoading(false);
    }
  };



  return (
    <AuthContext.Provider
      value={{
        isAuthenticated,
        user,
        session,
        isLoading,
        error,
        login,
        logout,
        signUp,
        resetPassword,
        updateProfile,
        signInWithGoogle,
        hasLoadedGroups,
      }}
    >
      {children}
    </AuthContext.Provider>
  );
};





export const useAuth = () => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};