import React, { createContext, useContext, ReactNode } from 'react';
import { DEMO_DATA, DemoPlayer, DemoMatch, DemoGroup } from '@/lib/demo/sampleData';

interface DemoContextType {
  isDemoMode: boolean;
  players: DemoPlayer[];
  matches: DemoMatch[];
  group: DemoGroup;
  // Simulated async functions that return demo data
  fetchPlayers: () => Promise<{ data: DemoPlayer[]; error: null }>;
  fetchMatches: () => Promise<{ data: DemoMatch[]; error: null }>;
  fetchGroup: () => Promise<{ data: DemoGroup; error: null }>;
  // Simulated mutation functions (no-ops in demo)
  addPlayer: (player: Omit<DemoPlayer, 'id'>) => Promise<{ data: DemoPlayer; error: null }>;
  updatePlayer: (id: number, updates: Partial<DemoPlayer>) => Promise<{ data: DemoPlayer; error: null }>;
  deletePlayer: (id: number) => Promise<{ data: null; error: null }>;
  addMatch: (match: Omit<DemoMatch, 'id'>) => Promise<{ data: DemoMatch; error: null }>;
  updateMatch: (id: number, updates: Partial<DemoMatch>) => Promise<{ data: DemoMatch; error: null }>;
  deleteMatch: (id: number) => Promise<{ data: null; error: null }>;
}

const DemoContext = createContext<DemoContextType | null>(null);

interface DemoProviderProps {
  children: ReactNode;
}

export const DemoProvider = ({ children }: DemoProviderProps) => {
  const { players, matches, group } = DEMO_DATA;

  // Simulated async data fetching functions
  const fetchPlayers = async () => {
    // Simulate network delay
    await new Promise(resolve => setTimeout(resolve, 300));
    return { data: players, error: null };
  };

  const fetchMatches = async () => {
    // Simulate network delay
    await new Promise(resolve => setTimeout(resolve, 300));
    return { data: matches, error: null };
  };

  const fetchGroup = async () => {
    // Simulate network delay
    await new Promise(resolve => setTimeout(resolve, 200));
    return { data: group, error: null };
  };

  // Simulated mutation functions (no-ops in demo mode)
  const addPlayer = async (playerData: Omit<DemoPlayer, 'id'>) => {
    await new Promise(resolve => setTimeout(resolve, 500));
    const newPlayer: DemoPlayer = {
      ...playerData,
      id: Math.max(...players.map(p => p.id)) + 1,
    };
    return { data: newPlayer, error: null };
  };

  const updatePlayer = async (id: number, updates: Partial<DemoPlayer>) => {
    await new Promise(resolve => setTimeout(resolve, 500));
    const player = players.find(p => p.id === id);
    if (!player) {
      throw new Error('Player not found');
    }
    const updatedPlayer = { ...player, ...updates };
    return { data: updatedPlayer, error: null };
  };

  const deletePlayer = async (id: number) => {
    await new Promise(resolve => setTimeout(resolve, 500));
    return { data: null, error: null };
  };

  const addMatch = async (matchData: Omit<DemoMatch, 'id'>) => {
    await new Promise(resolve => setTimeout(resolve, 500));
    const newMatch: DemoMatch = {
      ...matchData,
      id: Math.max(...matches.map(m => m.id)) + 1,
    };
    return { data: newMatch, error: null };
  };

  const updateMatch = async (id: number, updates: Partial<DemoMatch>) => {
    await new Promise(resolve => setTimeout(resolve, 500));
    const match = matches.find(m => m.id === id);
    if (!match) {
      throw new Error('Match not found');
    }
    const updatedMatch = { ...match, ...updates };
    return { data: updatedMatch, error: null };
  };

  const deleteMatch = async (id: number) => {
    await new Promise(resolve => setTimeout(resolve, 500));
    return { data: null, error: null };
  };

  const value: DemoContextType = {
    isDemoMode: true,
    players,
    matches,
    group,
    fetchPlayers,
    fetchMatches,
    fetchGroup,
    addPlayer,
    updatePlayer,
    deletePlayer,
    addMatch,
    updateMatch,
    deleteMatch,
  };

  return (
    <DemoContext.Provider value={value}>
      {children}
    </DemoContext.Provider>
  );
};

export const useDemo = () => {
  const context = useContext(DemoContext);
  if (!context) {
    throw new Error('useDemo must be used within a DemoProvider');
  }
  return context;
};

// Hook to check if we're in demo mode
export const useDemoMode = () => {
  const context = useContext(DemoContext);
  return context?.isDemoMode ?? false;
};
