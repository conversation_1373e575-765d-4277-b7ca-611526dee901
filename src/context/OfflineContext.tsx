import React, { createContext, useContext, useEffect, useState } from 'react';
import { 
  initOfflineDetector, 
  isOffline as checkIsOffline,
  subscribeToConnectivityChanges,
  cleanupOfflineDetector
} from '@/lib/offline/offlineDetector';
import {
  initSyncQueue,
  getSyncStatus,
  subscribeToSyncStatus,
  cleanupSyncQueue,
  attemptSync,
  SyncStatus
} from '@/lib/offline/syncQueue';

interface OfflineContextType {
  isOffline: boolean;
  syncStatus: SyncStatus;
  syncNow: () => Promise<void>;
}

const OfflineContext = createContext<OfflineContextType | undefined>(undefined);

export const OfflineProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [isOffline, setIsOffline] = useState<boolean>(checkIsOffline());
  const [syncStatus, setSyncStatus] = useState<SyncStatus>(getSyncStatus());

  useEffect(() => {
    // Initialize offline detector and sync queue
    initOfflineDetector();
    initSyncQueue();

    // Subscribe to connectivity changes
    const unsubscribeConnectivity = subscribeToConnectivityChanges((isOnline) => {
      setIsOffline(!isOnline);
    });

    // Subscribe to sync status changes
    const unsubscribeSyncStatus = subscribeToSyncStatus((status) => {
      setSyncStatus(status);
    });

    // Clean up on unmount
    return () => {
      unsubscribeConnectivity();
      unsubscribeSyncStatus();
      cleanupOfflineDetector();
      cleanupSyncQueue();
    };
  }, []);

  const syncNow = async () => {
    await attemptSync();
  };

  return (
    <OfflineContext.Provider value={{ isOffline, syncStatus, syncNow }}>
      {children}
    </OfflineContext.Provider>
  );
};

export const useOffline = (): OfflineContextType => {
  const context = useContext(OfflineContext);
  if (context === undefined) {
    throw new Error('useOffline must be used within an OfflineProvider');
  }
  return context;
};
