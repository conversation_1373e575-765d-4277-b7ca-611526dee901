import React, { createContext, useContext, useState, useEffect } from 'react';

interface OnboardingContextType {
  hasCompletedOnboarding: boolean;
  startOnboarding: () => void;
  completeOnboarding: () => void;
  skipOnboarding: () => void;
  isOnboardingActive: boolean;
}

const OnboardingContext = createContext<OnboardingContextType | undefined>(undefined);

const ONBOARDING_STORAGE_KEY = 'hasCompletedOnboarding';

export const OnboardingProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [hasCompletedOnboarding, setHasCompletedOnboarding] = useState<boolean>(() => {
    // Check localStorage for onboarding status
    const storedValue = localStorage.getItem(ONBOARDING_STORAGE_KEY);
    return storedValue ? JSON.parse(storedValue) : false;
  });
  
  const [isOnboardingActive, setIsOnboardingActive] = useState(false);

  // Update localStorage when onboarding status changes
  useEffect(() => {
    localStorage.setItem(ONBOARDING_STORAGE_KEY, JSON.stringify(hasCompletedOnboarding));
  }, [hasCompletedOnboarding]);

  const startOnboarding = () => {
    setIsOnboardingActive(true);
  };

  const completeOnboarding = () => {
    setHasCompletedOnboarding(true);
    setIsOnboardingActive(false);
  };

  const skipOnboarding = () => {
    setIsOnboardingActive(false);
  };

  return (
    <OnboardingContext.Provider
      value={{
        hasCompletedOnboarding,
        startOnboarding,
        completeOnboarding,
        skipOnboarding,
        isOnboardingActive,
      }}
    >
      {children}
    </OnboardingContext.Provider>
  );
};

export const useOnboarding = (): OnboardingContextType => {
  const context = useContext(OnboardingContext);
  if (context === undefined) {
    throw new Error('useOnboarding must be used within an OnboardingProvider');
  }
  return context;
};
