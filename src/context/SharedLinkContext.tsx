import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { useParams } from 'react-router-dom';
import { SharedLink } from '@/types/sharing';
import { validateSharedLink } from '@/lib/api/sharing';
import { supabase } from '@/lib/supabase';

interface SharedLinkContextType {
  sharedLink: SharedLink | null;
  isSharedView: boolean;
  groupId: string | null;
  groupName: string | null;
  accessLevel: 'read' | 'comment' | null;
  isLoading: boolean;
  error: string | null;
}

const SharedLinkContext = createContext<SharedLinkContextType>({
  sharedLink: null,
  isSharedView: false,
  groupId: null,
  groupName: null,
  accessLevel: null,
  isLoading: false,
  error: null,
});

export const useSharedLink = () => useContext(SharedLinkContext);

interface SharedLinkProviderProps {
  children: ReactNode;
}

export const SharedLinkProvider = ({ children }: SharedLinkProviderProps) => {
  const { linkId } = useParams<{ linkId?: string }>();
  const { groupId: urlGroupId } = useParams<{ groupId?: string }>();
  const [sharedLink, setSharedLink] = useState<SharedLink | null>(null);
  const [groupId, setGroupId] = useState<string | null>(null);
  const [groupName, setGroupName] = useState<string | null>(null);
  const [accessLevel, setAccessLevel] = useState<'read' | 'comment' | null>(null);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const [isSharedView, setIsSharedView] = useState<boolean>(false);

  // Check if we're in a shared view
  useEffect(() => {
    const checkSharedView = async () => {
      // If we have a linkId, we're in a shared link view
      if (linkId) {
        setIsSharedView(true);
        setIsLoading(true);

        try {
          // Validate the shared link
          const result = await validateSharedLink(linkId);

          if (result.isValid && result.link && result.group) {
            setSharedLink(result.link);
            setGroupId(result.group.id);
            setGroupName(result.group.name);
            setAccessLevel(result.link.access_level);
            console.log('Shared link validated:', result.link.access_level);
          } else {
            setError(result.error || 'Invalid shared link');
          }
        } catch (err) {
          console.error('Error validating shared link:', err);
          setError('Failed to validate shared link');
        } finally {
          setIsLoading(false);
        }
      }
      // If we have a groupId in the URL and it starts with "shared", we're in a shared group view
      else if (urlGroupId && window.location.pathname.includes('/shared/')) {
        setIsSharedView(true);
        setGroupId(urlGroupId);
        setAccessLevel('read'); // Default to read access for direct shared URLs
        // We'll need to fetch the group name separately
      } else {
        setIsSharedView(false);
        setSharedLink(null);
        setGroupId(null);
        setGroupName(null);
        setAccessLevel(null);
      }
    };

    checkSharedView();
  }, [linkId, urlGroupId]);

  // If we're in a shared group view, fetch the group name
  useEffect(() => {
    const fetchGroupName = async () => {
      if (isSharedView && groupId && !groupName && !linkId) {
        try {
          // Fetch the group name from Supabase
          const { data, error } = await supabase
            .from('friend_groups')
            .select('name')
            .eq('id', groupId)
            .single();

          if (error) throw error;
          if (data) {
            setGroupName(data.name);
          } else {
            setGroupName('Shared Group');
          }
        } catch (err) {
          console.error('Error fetching group name:', err);
          setGroupName('Shared Group');
        }
      }
    };

    fetchGroupName();
  }, [isSharedView, groupId, groupName, linkId]);

  const value = {
    sharedLink,
    isSharedView,
    groupId,
    groupName,
    accessLevel,
    isLoading,
    error,
  };

  return (
    <SharedLinkContext.Provider value={value}>
      {children}
    </SharedLinkContext.Provider>
  );
};

export default SharedLinkContext;
