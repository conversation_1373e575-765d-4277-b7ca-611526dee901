import { createContext, useContext, useState, useEffect, ReactNode } from 'react';

type UIMode = 'current' | 'experimental';

interface UIContextType {
  uiMode: UIMode;
  toggleUIMode: () => void;
  setUIMode: (mode: UIMode) => void;
}

const UIContext = createContext<UIContextType | undefined>(undefined);

interface UIProviderProps {
  children: ReactNode;
}

export const UIProvider = ({ children }: UIProviderProps) => {
  const [uiMode, setUIModeState] = useState<UIMode>(() => {
    // Initialize from localStorage if available
    const savedMode = localStorage.getItem('uiMode');
    return (savedMode as UIMode) || 'current';
  });
  
  const setUIMode = (mode: UIMode) => {
    setUIModeState(mode);
    localStorage.setItem('uiMode', mode);
  };

  const toggleUIMode = () => {
    const newMode = uiMode === 'current' ? 'experimental' : 'current';
    setUIMode(newMode);
  };
  
  return (
    <UIContext.Provider value={{ uiMode, toggleUIMode, setUIMode }}>
      {children}
    </UIContext.Provider>
  );
};

export const useUI = () => {
  const context = useContext(UIContext);
  if (context === undefined) {
    throw new Error('useUI must be used within a UIProvider');
  }
  return context;
};
