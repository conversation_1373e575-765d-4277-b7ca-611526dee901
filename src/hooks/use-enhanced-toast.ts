import { useToast } from "@/hooks/use-toast";
import { ToastActionElement } from "@/components/ui/toast";

type ToastProps = {
  title?: string;
  description?: string;
  action?: ToastActionElement;
  variant?: "default" | "destructive" | null;
};

export function useEnhancedToast() {
  const { toast } = useToast();

  const success = ({ title = "Success", description, action }: ToastProps) => {
    return toast({
      title,
      description,
      action,
      variant: "default",
      className: "bg-green-50 border-green-200 text-green-800 dark:bg-green-900/20 dark:border-green-900 dark:text-green-300",
    });
  };

  const error = ({ title = "Error", description, action }: ToastProps) => {
    return toast({
      title,
      description,
      action,
      variant: "destructive",
    });
  };

  const info = ({ title = "Info", description, action }: ToastProps) => {
    return toast({
      title,
      description,
      action,
      variant: "default",
      className: "bg-blue-50 border-blue-200 text-blue-800 dark:bg-blue-900/20 dark:border-blue-900 dark:text-blue-300",
    });
  };

  const warning = ({ title = "Warning", description, action }: ToastProps) => {
    return toast({
      title,
      description,
      action,
      variant: "default",
      className: "bg-yellow-50 border-yellow-200 text-yellow-800 dark:bg-yellow-900/20 dark:border-yellow-900 dark:text-yellow-300",
    });
  };

  return {
    toast,
    success,
    error,
    info,
    warning,
  };
}
