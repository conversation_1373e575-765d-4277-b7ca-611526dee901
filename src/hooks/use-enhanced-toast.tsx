import * as React from "react";
import { EnhancedToast } from "@/components/ui/enhanced-toast";
import { useToast } from "@/hooks/use-toast";

type ToastVariant = "default" | "destructive" | "success" | "warning" | "info";

interface UseEnhancedToastOptions {
  title?: string;
  description?: string;
  variant?: ToastVariant;
  duration?: number;
  action?: React.ReactNode;
}

export function useEnhancedToast() {
  const { toast, dismiss, toasts } = useToast();

  const showToast = React.useCallback(
    ({ title, description, variant = "default", duration, action }: UseEnhancedToastOptions) => {
      return toast({
        title,
        description,
        duration,
        action,
        variant,
        // @ts-ignore - The enhanced toast component accepts the same props
        render: (props) => (
          <EnhancedToast
            title={title}
            description={description}
            variant={variant}
            action={action}
            {...props}
          />
        ),
      });
    },
    [toast]
  );

  const success = React.useCallback(
    (options: Omit<UseEnhancedToastOptions, "variant">) => {
      return showToast({ ...options, variant: "success" });
    },
    [showToast]
  );

  const error = React.useCallback(
    (options: Omit<UseEnhancedToastOptions, "variant">) => {
      return showToast({ ...options, variant: "destructive" });
    },
    [showToast]
  );

  const warning = React.useCallback(
    (options: Omit<UseEnhancedToastOptions, "variant">) => {
      return showToast({ ...options, variant: "warning" });
    },
    [showToast]
  );

  const info = React.useCallback(
    (options: Omit<UseEnhancedToastOptions, "variant">) => {
      return showToast({ ...options, variant: "info" });
    },
    [showToast]
  );

  return {
    toast: showToast,
    success,
    error,
    warning,
    info,
    dismiss,
    toasts,
  };
}
