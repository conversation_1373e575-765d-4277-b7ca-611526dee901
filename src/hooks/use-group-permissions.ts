import { useState, useEffect } from 'react';
import { useGroup } from '@/lib/context/GroupContext';
import { GroupRole } from '@/lib/api/groups';

interface UseGroupPermissionsReturn {
  canView: boolean;
  canEdit: boolean;
  canManageMembers: boolean;
  isCreator: boolean;
  isLoading: boolean;
  userRole: GroupRole | null;
}

/**
 * Hook to check user permissions in the current group
 */
export function useGroupPermissions(): UseGroupPermissionsReturn {
  const { currentGroup, userRole, isLoading, checkPermission } = useGroup();
  const [permissions, setPermissions] = useState<UseGroupPermissionsReturn>({
    canView: false,
    canEdit: false,
    canManageMembers: false,
    isCreator: false,
    isLoading: true,
    userRole: null
  });

  useEffect(() => {
    const checkPermissions = async () => {
      if (!currentGroup || isLoading) {
        setPermissions({
          canView: false,
          canEdit: false,
          canManageMembers: false,
          isCreator: false,
          isLoading: true,
          userRole: null
        });
        return;
      }

      // Check if user is the creator
      const isCreator = await checkPermission('Admin');
      
      // Set permissions based on role
      setPermissions({
        canView: true, // All roles can view
        canEdit: userRole === 'Admin' || userRole === 'Collaborator',
        canManageMembers: userRole === 'Admin',
        isCreator,
        isLoading: false,
        userRole
      });
    };

    checkPermissions();
  }, [currentGroup, userRole, isLoading, checkPermission]);

  return permissions;
}

export default useGroupPermissions;
