import { useState, useCallback, useRef } from 'react';

interface SwipeHandlers {
  onSwipedLeft?: () => void;
  onSwipedRight?: () => void;
  onSwipeStart?: () => void;
  onSwipeEnd?: () => void;
}

interface SwipeState {
  isActive: boolean;
  direction: 'left' | 'right' | null;
  distance: number;
}

export function useSwipe({ onSwipedLeft, onSwipedRight, onSwipeStart, onSwipeEnd }: SwipeHandlers) {
  const [touchStart, setTouchStart] = useState<number | null>(null);
  const [swipeState, setSwipeState] = useState<SwipeState>({
    isActive: false,
    direction: null,
    distance: 0
  });
  const elementRef = useRef<HTMLElement | null>(null);

  const handleTouchStart = useCallback((e: TouchEvent) => {
    setTouchStart(e.touches[0].clientX);
    setSwipeState({
      isActive: true,
      direction: null,
      distance: 0
    });
    onSwipeStart?.();

    // Add visual feedback
    if (elementRef.current) {
      elementRef.current.style.transition = 'none';
    }
  }, [onSwipeStart]);

  const handleTouchMove = useCallback((e: TouchEvent) => {
    if (!touchStart) return;

    const currentTouch = e.touches[0].clientX;
    const diff = touchStart - currentTouch;
    const distance = Math.abs(diff);
    const direction = diff > 0 ? 'left' : 'right';

    setSwipeState({
      isActive: true,
      direction,
      distance
    });

    // Add visual feedback during swipe
    if (elementRef.current && distance > 10) {
      const translateX = Math.min(Math.max(diff * -0.3, -50), 50);
      elementRef.current.style.transform = `translateX(${translateX}px)`;
      elementRef.current.style.opacity = `${Math.max(0.7, 1 - distance / 200)}`;
    }

    if (distance > 50) {
      if (diff > 0 && onSwipedLeft) {
        onSwipedLeft();
        resetSwipe();
      }
      if (diff < 0 && onSwipedRight) {
        onSwipedRight();
        resetSwipe();
      }
    }
  }, [touchStart, onSwipedLeft, onSwipedRight]);

  const handleTouchEnd = useCallback(() => {
    resetSwipe();
  }, []);

  const resetSwipe = useCallback(() => {
    setTouchStart(null);
    setSwipeState({
      isActive: false,
      direction: null,
      distance: 0
    });
    onSwipeEnd?.();

    // Reset visual feedback with animation
    if (elementRef.current) {
      elementRef.current.style.transition = 'transform 0.3s ease-out, opacity 0.3s ease-out';
      elementRef.current.style.transform = 'translateX(0)';
      elementRef.current.style.opacity = '1';
    }
  }, [onSwipeEnd]);

  return {
    onTouchStart: handleTouchStart,
    onTouchMove: handleTouchMove,
    onTouchEnd: handleTouchEnd,
    swipeState,
    elementRef,
  };
}
