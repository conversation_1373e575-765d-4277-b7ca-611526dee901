@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    /* Light mode - using #f4f2ed as the lightest color */
    --background: 43 22% 95%;
    --foreground: 0 0% 6%;

    --card: 43 22% 95%;
    --card-foreground: 0 0% 6%;

    --popover: 43 22% 95%;
    --popover-foreground: 0 0% 6%;

    /* Primary color: #35db71 - Updated to user preference */
    --primary: 145 70% 53%;
    --primary-foreground: 0 0% 6%; /* Changed from white to dark for light mode */

    /* Secondary colors - shades of primary */
    --secondary: 145 70% 45%;
    --secondary-foreground: 0 0% 6%; /* Changed from white to dark for light mode */

    --muted: 43 22% 90%;
    --muted-foreground: 0 0% 25%;

    --accent: 145 70% 93%;
    --accent-foreground: 145 70% 12%;

    --destructive: 0 84% 60%;
    --destructive-foreground: 0 0% 6%; /* Changed from white to dark for light mode */

    --border: 43 22% 85%;
    --input: 43 22% 85%;
    --ring: 145 70% 33%;

    --radius: 0.5rem;

    --sidebar-background: 43 22% 95%;
    --sidebar-foreground: 0 0% 15%;
    --sidebar-primary: 145 70% 33%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 145 70% 93%;
    --sidebar-accent-foreground: 145 70% 12%;
    --sidebar-border: 43 22% 85%;
    --sidebar-ring: 145 70% 33%;
  }

  .dark {
    /* Dark mode - using #0f0f0f as the darkest color */
    --background: 0 0% 6%;
    --foreground: 43 22% 95%;

    --card: 0 0% 9%;
    --card-foreground: 43 22% 95%;

    --popover: 0 0% 9%;
    --popover-foreground: 43 22% 95%;

    /* Primary color: #35db71 - Updated to user preference */
    --primary: 145 70% 53%;
    --primary-foreground: 0 0% 6%;

    /* Secondary colors - darker shades of primary */
    --secondary: 145 70% 45%;
    --secondary-foreground: 0 0% 100%;

    --muted: 0 0% 15%;
    --muted-foreground: 43 22% 80%;

    --accent: 145 70% 15%;
    --accent-foreground: 43 22% 95%;

    --destructive: 0 62% 30%;
    --destructive-foreground: 43 22% 95%;

    --border: 0 0% 20%;
    --input: 0 0% 20%;
    --ring: 145 70% 33%;

    --sidebar-background: 0 0% 9%;
    --sidebar-foreground: 43 22% 95%;
    --sidebar-primary: 145 70% 33%;
    --sidebar-primary-foreground: 0 0% 6%;
    --sidebar-accent: 145 70% 15%;
    --sidebar-accent-foreground: 43 22% 95%;
    --sidebar-border: 0 0% 20%;
    --sidebar-ring: 145 70% 33%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground;
  }
}

/* Halftone Background Optimizations */
@layer utilities {
  .halftone-background {
    /* Enable hardware acceleration for smooth animations */
    transform: translateZ(0);
    will-change: transform, background;
    backface-visibility: hidden;
    perspective: 1000px;
  }

  .halftone-pattern {
    /* Optimize SVG rendering for fine details */
    shape-rendering: geometricPrecision;
    image-rendering: -webkit-optimize-contrast;
    image-rendering: crisp-edges;
    /* Ensure smooth rendering of small circles */
    vector-effect: non-scaling-stroke;
  }

  /* Respect reduced motion preferences */
  @media (prefers-reduced-motion: reduce) {
    .halftone-background,
    .halftone-pattern {
      animation: none !important;
      transition: none !important;
      transform: none !important;
    }
  }

  /* Performance optimizations for mobile */
  @media (max-width: 768px) {
    .halftone-background {
      /* Reduce complexity on mobile for better performance */
      filter: none;
    }
  }
}

@layer components {
  /* Animation CSS Custom Properties */
  :root {
    /* Animation durations */
    --animation-duration-fast: 150ms;
    --animation-duration-normal: 250ms;
    --animation-duration-slow: 350ms;
    --animation-duration-slower: 500ms;

    /* Animation easing functions */
    --animation-ease-out: cubic-bezier(0.16, 1, 0.3, 1);
    --animation-ease-in-out: cubic-bezier(0.4, 0, 0.2, 1);
    --animation-ease-spring: cubic-bezier(0.34, 1.56, 0.64, 1);
    --animation-ease-bounce: cubic-bezier(0.68, -0.55, 0.265, 1.55);

    /* Scale values */
    --scale-hover: 1.02;
    --scale-active: 0.98;
    --scale-focus: 1.05;
  }

  /* Respect user motion preferences */
  @media (prefers-reduced-motion: reduce) {
    :root {
      --animation-duration-fast: 0ms;
      --animation-duration-normal: 0ms;
      --animation-duration-slow: 0ms;
      --animation-duration-slower: 0ms;
      --scale-hover: 1;
      --scale-active: 1;
      --scale-focus: 1;
    }

    * {
      animation-duration: 0.01ms !important;
      animation-iteration-count: 1 !important;
      transition-duration: 0.01ms !important;
      scroll-behavior: auto !important;
    }
  }

  /* Base animation utilities */
  .animate-smooth {
    transition: all var(--animation-duration-normal) var(--animation-ease-out);
  }

  .animate-fast {
    transition: all var(--animation-duration-fast) var(--animation-ease-out);
  }

  .animate-slow {
    transition: all var(--animation-duration-slow) var(--animation-ease-out);
  }

  .animate-spring {
    transition: all var(--animation-duration-normal) var(--animation-ease-spring);
  }

  /* Interactive element animations */
  .interactive-scale {
    transition: transform var(--animation-duration-fast) var(--animation-ease-out);
  }

  .interactive-scale:hover {
    transform: scale(var(--scale-hover));
  }

  .interactive-scale:active {
    transform: scale(var(--scale-active));
  }

  /* Card hover animations */
  .card-hover {
    transition:
      transform var(--animation-duration-normal) var(--animation-ease-out),
      box-shadow var(--animation-duration-normal) var(--animation-ease-out),
      border-color var(--animation-duration-normal) var(--animation-ease-out);
  }

  .card-hover:hover {
    transform: translateY(-2px);
    box-shadow:
      0 10px 25px -3px rgb(0 0 0 / 0.1),
      0 4px 6px -2px rgb(0 0 0 / 0.05),
      0 0 0 1px rgb(53 219 113 / 0.1);
  }

  .dark .card-hover:hover {
    box-shadow:
      0 10px 25px -3px rgb(0 0 0 / 0.3),
      0 4px 6px -2px rgb(0 0 0 / 0.2),
      0 0 0 1px rgb(53 219 113 / 0.2);
  }

  /* Button animations */
  .button-hover {
    transition:
      background-color var(--animation-duration-fast) var(--animation-ease-out),
      border-color var(--animation-duration-fast) var(--animation-ease-out),
      color var(--animation-duration-fast) var(--animation-ease-out),
      transform var(--animation-duration-fast) var(--animation-ease-out),
      box-shadow var(--animation-duration-fast) var(--animation-ease-out);
  }

  .button-hover:hover {
    transform: translateY(-1px);
  }

  .button-hover:active {
    transform: translateY(0);
  }

  /* Smooth focus states */
  .focus-ring {
    @apply focus-visible:ring-2 focus-visible:ring-soccer-primary focus-visible:ring-offset-2 focus-visible:outline-none;
    transition: box-shadow var(--animation-duration-fast) var(--animation-ease-out);
  }

  /* Gradient text utilities */
  .gradient-text-primary {
    @apply bg-gradient-to-r from-soccer-primary to-soccer-primary-light bg-clip-text text-transparent;
  }

  /* Smooth page transitions */
  .page-transition {
    transition: all var(--animation-duration-slower) var(--animation-ease-in-out);
  }

  /* Navigation animations */
  .nav-item {
    transition:
      background-color var(--animation-duration-normal) var(--animation-ease-out),
      color var(--animation-duration-normal) var(--animation-ease-out),
      transform var(--animation-duration-normal) var(--animation-ease-out);
  }

  .nav-item:hover {
    transform: scale(var(--scale-hover));
  }

  .nav-item.active {
    transform: scale(var(--scale-focus));
  }

  /* Dashboard tabs with soccer-primary active states */
  .dashboard-tab {
    @apply relative transition-all duration-200;
  }

  /* Use higher specificity and override all possible conflicting styles */
  [data-state="active"].dashboard-tab,
  .dashboard-tab[data-state="active"] {
    background-color: rgb(53 219 113 / 0.1) !important;
    background: rgb(53 219 113 / 0.1) !important;
    color: rgb(53 219 113) !important; /* Changed from white to green for light mode */
    border: 1px solid rgb(53 219 113 / 0.2) !important;
    box-shadow: 0 1px 2px 0 rgb(0 0 0 / 0.05) !important;
  }

  /* Dark mode adjustments for dashboard tabs */
  .dark [data-state="active"].dashboard-tab,
  .dark .dashboard-tab[data-state="active"] {
    background-color: rgb(53 219 113 / 0.1) !important;
    background: rgb(53 219 113 / 0.1) !important;
    color: rgb(255 255 255) !important; /* Keep white for dark mode */
    border: 1px solid rgb(53 219 113 / 0.2) !important;
  }

  /* Team generator tabs without hover animations */
  .team-generator-tab {
    @apply relative;
  }

  .team-generator-tab[data-state="active"] {
    background-color: rgb(53 219 113 / 0.1) !important;
    color: rgb(53 219 113) !important; /* Green text for light mode */
    border: 1px solid rgb(53 219 113 / 0.2) !important;
    box-shadow: 0 1px 2px 0 rgb(0 0 0 / 0.05) !important;
  }

  /* Dark mode adjustments for team generator tabs */
  .dark .team-generator-tab[data-state="active"] {
    background-color: rgb(53 219 113 / 0.1) !important; /* Changed to match light mode background */
    color: rgb(255 255 255) !important; /* Keep white for dark mode */
    border: 1px solid rgb(53 219 113 / 0.2) !important; /* Changed to match light mode border */
  }

  /* Enhanced shimmer animation for skeletons */
  @keyframes shimmer {
    0% {
      background-position: -200% 0;
    }
    100% {
      background-position: 200% 0;
    }
  }

  @keyframes pulse-glow {
    0%, 100% {
      opacity: 1;
    }
    50% {
      opacity: 0.7;
    }
  }

  @keyframes slide-in-up {
    from {
      opacity: 0;
      transform: translateY(10px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  @keyframes slide-in-down {
    from {
      opacity: 0;
      transform: translateY(-10px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  @keyframes fade-in {
    from {
      opacity: 0;
    }
    to {
      opacity: 1;
    }
  }

  @keyframes scale-in {
    from {
      opacity: 0;
      transform: scale(0.95);
    }
    to {
      opacity: 1;
      transform: scale(1);
    }
  }
}