import { describe, it, expect, vi, beforeEach } from 'vitest';
import { API, APIError } from '../index';

describe('API', () => {
  const mockClient = {
    from: vi.fn(() => ({
      select: vi.fn().mockReturnThis(),
      insert: vi.fn().mockReturnThis(),
      update: vi.fn().mockReturnThis(),
      delete: vi.fn().mockReturnThis(),
      eq: vi.fn().mockReturnThis(),
      single: vi.fn().mockReturnThis(),
      order: vi.fn().mockReturnThis(),
    })),
    rpc: vi.fn(),
    auth: {
      signInWithPassword: vi.fn(),
      signOut: vi.fn(),
    },
  };

  beforeEach(() => {
    API.setClient(mockClient as any);
  });

  describe('getPlayers', () => {
    it('should return players array on success', async () => {
      const mockPlayers = [{ id: 1, name: 'Test' }];
      mockClient.from().select().order.mockResolvedValue({ data: mockPlayers, error: null });

      const result = await API.getPlayers();
      expect(result).toEqual(mockPlayers);
    });

    it('should throw APIError on failure', async () => {
      mockClient.from().select().order.mockResolvedValue({ 
        data: null, 
        error: { message: 'DB Error', code: 'DB_ERROR' } 
      });

      await expect(API.getPlayers()).rejects.toThrow(APIError);
    });
  });

  describe('createPlayer', () => {
    it('should create player successfully', async () => {
      const mockPlayer = { name: 'Test', skills: 80, effort: 75, stamina: 70 };
      mockClient.from().insert().single.mockResolvedValue({ 
        data: { id: 1, ...mockPlayer }, 
        error: null 
      });

      const result = await API.createPlayer(mockPlayer);
      expect(result.id).toBe(1);
      expect(result.name).toBe(mockPlayer.name);
    });

    it('should validate player data before creating', async () => {
      const invalidPlayer = { name: '', skills: 150 };
      await expect(API.createPlayer(invalidPlayer as any)).rejects.toThrow();
    });
  });

  describe('updateMatch', () => {
    it('should validate match score and winner combination', async () => {
      const invalidMatch = {
        scorea: 2,
        scoreb: 1,
        winner: 'B'
      };

      await expect(API.updateMatch(1, invalidMatch)).rejects.toThrow('Winner must match score difference');
    });
  });
});
