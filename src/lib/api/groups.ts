import { supabase } from '@/lib/supabase';
import { ImportData } from '@/lib/validators/importSchema';
import { validatePlayer, validateMatch } from '@/lib/validators';

// Role type definition
export type GroupRole = 'Admin' | 'Collaborator' | 'Guest';

// Group member interface
export interface GroupMember {
  id: string;
  group_id: string;
  user_id: string;
  role: GroupRole;
  created_at: string;
  user?: {
    email: string;
    display_name?: string;
  };
  isCreator?: boolean;
}

// Group interface
export interface Group {
  id: string;
  name: string;
  created_by: string;
  created_at?: string;
}

// Create a new group
export const createGroup = async (name: string, userId: string) => {
  const { data, error } = await supabase
    .from('friend_groups')
    .insert([{ name, created_by: userId }])
    .select();

  if (error) throw error;

  // Add the creator as an Admin
  if (data && data.length > 0) {
    await addUserToGroup(data[0].id, userId, 'Admin');
  }

  return data;
};

// Fetch groups for a user
export const fetchUserGroups = async (userId: string): Promise<Group[]> => {
  // Get groups where user is a member
  const { data: memberGroups, error: memberError } = await supabase
    .from('group_members')
    .select('group_id, friend_groups(id, name, created_by, created_at)')
    .eq('user_id', userId);

  if (memberError) throw memberError;

  // Get groups where user is the creator
  const { data: createdGroups, error: creatorError } = await supabase
    .from('friend_groups')
    .select('id, name, created_by, created_at')
    .eq('created_by', userId);

  if (creatorError) throw creatorError;

  // Combine and deduplicate the results
  const groups: Group[] = [];
  const groupIds = new Set<string>();

  if (memberGroups) {
    for (const item of memberGroups) {
      if (item.friend_groups && !groupIds.has(item.friend_groups.id)) {
        groups.push(item.friend_groups as unknown as Group);
        groupIds.add(item.friend_groups.id);
      }
    }
  }

  if (createdGroups) {
    for (const group of createdGroups) {
      if (!groupIds.has(group.id)) {
        groups.push(group as Group);
        groupIds.add(group.id);
      }
    }
  }

  return groups;
};

// Add a user to a group
export const addUserToGroup = async (groupId: string, userId: string, role: GroupRole) => {
  // Check if user is already a member
  const { data: existingMember, error: checkError } = await supabase
    .from('group_members')
    .select('id, role')
    .eq('group_id', groupId)
    .eq('user_id', userId)
    .maybeSingle();

  if (checkError) throw checkError;

  // If user is already a member, update their role if different
  if (existingMember) {
    if (existingMember.role !== role) {
      const { data, error } = await supabase
        .from('group_members')
        .update({ role })
        .eq('id', existingMember.id)
        .select();

      if (error) throw error;
      return data;
    }
    return [existingMember]; // Return existing member in array format for consistency
  }

  // Otherwise, add the user as a new member
  const { data, error } = await supabase
    .from('group_members')
    .insert([{ group_id: groupId, user_id: userId, role }])
    .select();

  if (error) throw error;
  return data;
};

// Fetch members of a group
export const fetchGroupMembers = async (groupId: string) => {
  const { data, error } = await supabase
    .from('group_members')
    .select(`
      id,
      group_id,
      user_id,
      role,
      created_at
    `)
    .eq('group_id', groupId);

  // Fetch user details separately using the RPC function
  if (data && data.length > 0) {
    for (const member of data) {
      const { data: userData, error: userError } = await supabase
        .rpc('get_user_details_by_id', { user_id_to_find: member.user_id });

      if (!userError && userData) {
        // The function now returns a JSON object with email and display_name
        member.user = {
          email: userData.email,
          display_name: userData.display_name
        };
      }
    }
  }

  if (error) throw error;

  // Also get the group creator
  const { data: groupData, error: groupError } = await supabase
    .from('friend_groups')
    .select('created_by')
    .eq('id', groupId)
    .single();

  if (groupError) throw groupError;

  // Format the data to include user information
  const formattedData = data?.map(member => ({
    ...member,
    isCreator: member.user_id === groupData?.created_by
  })) || [];

  return formattedData;
};

// Update a member's role
export const updateMemberRole = async (memberId: string, role: GroupRole) => {
  const { data, error } = await supabase
    .from('group_members')
    .update({ role })
    .eq('id', memberId)
    .select();

  if (error) throw error;
  return data;
};

// Remove a member from a group
export const removeMember = async (memberId: string) => {
  const { error } = await supabase
    .from('group_members')
    .delete()
    .eq('id', memberId);

  if (error) throw error;
  return true;
};

// Invite a user to a group by email
export const inviteUserByEmail = async (groupId: string, email: string, role: GroupRole) => {
  try {
    // Get the current user
    const { data: { user } } = await supabase.auth.getUser();

    if (!user) {
      throw new Error('You must be logged in to invite users');
    }

    // If the user is trying to invite themselves
    if (user.email === email) {
      return addUserToGroup(groupId, user.id, role);
    }

    // Check if the current user has permission to invite others
    const { data: groupData } = await supabase
      .from('friend_groups')
      .select('created_by, name')
      .eq('id', groupId)
      .single();

    if (!groupData) {
      throw new Error('Group not found');
    }

    // Check if user has admin permission (either creator or admin role)
    const hasPermission = await checkUserPermission(groupId, user.id, 'Admin');
    if (!hasPermission) {
      throw new Error('You need Admin permission to invite users');
    }

    // Check if the user already exists in the system
    try {
      const { data: existingUserId, error: userLookupError } = await supabase
        .rpc('find_user_id_by_email', { email_to_find: email });

      if (!userLookupError && existingUserId) {
        // User exists, add them directly to the group
        return addUserToGroup(groupId, existingUserId, role);
      }
    } catch (error) {
      // If the function doesn't exist or there's another error, log it and continue
      console.warn('Error looking up user by email:', error);
      // We'll continue with the invitation flow
    }

    // Skip checking for existing invitations in user_invitations table
    // since it might not exist yet

    // Check if there's already a pending invitation in group_members
    const { data: existingMember, error: memberCheckError } = await supabase
      .from('group_members')
      .select('id')
      .eq('group_id', groupId)
      .eq('email_invite', email)
      .maybeSingle();

    if (memberCheckError) {
      console.warn('Error checking for existing member invitation:', memberCheckError);
      // Continue anyway, we'll just create a new one
    }

    if (existingMember) {
      // Update the existing member invitation
      const { data: updatedMember, error: updateError } = await supabase
        .from('group_members')
        .update({ role })
        .eq('id', existingMember.id)
        .select();

      if (updateError) throw updateError;

      // Trigger the invitation email via edge function
      await supabase.functions.invoke('send-invitation-email', {
        body: {
          email,
          groupId,
          groupName: groupData.name,
          invitedBy: user.email,
          role
        }
      });

      return updatedMember;
    }

    // Check if an invitation already exists in user_invitations table
    try {
      const { data: existingInvitation, error: inviteCheckError } = await supabase
        .from('user_invitations')
        .select('id, status')
        .eq('group_id', groupId)
        .eq('invited_email', email)
        .maybeSingle();

      if (inviteCheckError) {
        // If there's an error checking (like table doesn't exist), log and continue
        console.warn('Error checking for existing invitation:', inviteCheckError.message);
      } else if (existingInvitation) {
        // If invitation exists, update it instead of creating a new one
        await supabase
          .from('user_invitations')
          .update({
            role,
            status: 'pending', // Reset to pending if it was declined before
            invited_by: user.id
          })
          .eq('id', existingInvitation.id);

        console.log('Updated existing invitation for', email);
      } else {
        // If no invitation exists, create a new one
        await supabase
          .from('user_invitations')
          .insert({
            group_id: groupId,
            invited_email: email,
            invited_by: user.id,
            role,
            status: 'pending'
          });
      }
    } catch (error: any) {
      // If there's any other error, log it and continue
      console.warn('Could not manage user_invitations record:', error.message);
      // Don't throw the error - we'll still try to send the email and create the group_member entry
    }

    // Trigger the invitation email via edge function
    try {
      await supabase.functions.invoke('send-invitation-email', {
        body: {
          email,
          groupId,
          groupName: groupData.name,
          invitedBy: user.email,
          role
        }
      });
    } catch (error: any) {
      console.warn('Error sending invitation email:', error.message);
      // Continue anyway, we'll still create the group member
    }

    // Create a pending group_member entry with email_invite if one doesn't already exist
    let memberData;

    if (!existingMember) {
      try {
        const { data, error: memberError } = await supabase
          .from('group_members')
          .insert({
            group_id: groupId,
            email_invite: email,
            role,
            status: 'pending'
          })
          .select();

        if (memberError) {
          console.warn('Error creating group_member entry:', memberError.message);
          // If this fails but we sent the email, we can still consider it a success
        } else {
          memberData = data;
        }
      } catch (error: any) {
        console.warn('Exception creating group_member entry:', error.message);
        // Don't throw - if we sent the email, we can still consider it a success
      }
    } else {
      // Use the existing member data
      memberData = [existingMember];
    }

    return memberData;
  } catch (error: any) {
    // Create a more user-friendly error message
    console.error('Error in inviteUserByEmail:', error);
    throw new Error(error.message || 'Unable to invite user. Please check if the email is registered in the system.');
  }
};

// Check if a user has a specific permission in a group
export const checkUserPermission = async (groupId: string, userId: string, requiredRole: GroupRole) => {
  // Check if user is the creator of the group
  const { data: groupData, error: groupError } = await supabase
    .from('friend_groups')
    .select('created_by')
    .eq('id', groupId)
    .maybeSingle();

  if (groupError) throw groupError;

  // If user is the creator, they have all permissions
  if (groupData && groupData.created_by === userId) {
    return true;
  }

  // Check user's role in the group
  const { data: memberData, error: memberError } = await supabase
    .from('group_members')
    .select('role')
    .eq('group_id', groupId)
    .eq('user_id', userId)
    .maybeSingle();

  if (memberError) throw memberError;

  if (!memberData) {
    return false; // User is not a member
  }

  // Check if user's role has the required permission
  if (requiredRole === 'Admin') {
    return memberData.role === 'Admin';
  } else if (requiredRole === 'Collaborator') {
    return ['Admin', 'Collaborator'].includes(memberData.role);
  } else if (requiredRole === 'Guest') {
    return ['Admin', 'Collaborator', 'Guest'].includes(memberData.role);
  }

  return false;
};

// Update group details (name)
export const updateGroupName = async (groupId: string, name: string) => {
  // Validate input
  if (!name || name.trim() === '') {
    throw new Error('Group name cannot be empty');
  }

  const { data, error } = await supabase
    .from('friend_groups')
    .update({ name: name.trim() })
    .eq('id', groupId)
    .select();

  if (error) throw error;
  return data;
};

// Create a new group with imported data
export const createGroupWithImport = async (name: string, userId: string, importData: ImportData) => {
  try {
    // Start a transaction by using a single callback
    const { data, error } = await supabase.rpc('create_group_with_import', {
      group_name: name,
      user_id: userId,
      import_data: JSON.stringify(importData)
    });

    // If there's any error with the RPC function, fall back to manual transaction
    if (error) {
      console.warn('RPC function error, falling back to manual transaction:', error.message);
      return createGroupWithImportManual(name, userId, importData);
    }

    return data;
  } catch (error) {
    console.warn('Exception in createGroupWithImport, falling back to manual transaction:', error);
    return createGroupWithImportManual(name, userId, importData);
  }
};

// Manual implementation of creating a group with imported data
const createGroupWithImportManual = async (name: string, userId: string, importData: ImportData) => {
  try {
    console.log('Starting manual import process for group:', name);
    console.log('Import data summary:', {
      playerCount: importData.players.length,
      matchCount: importData.matches.length
    });

    // 1. Create the group
    const { data: groupData, error: groupError } = await supabase
      .from('friend_groups')
      .insert([{ name, created_by: userId }])
      .select();

    if (groupError) {
      console.error('Error creating group:', groupError);
      throw groupError;
    }

    if (!groupData || groupData.length === 0) {
      throw new Error('Failed to create group - no data returned');
    }

    const groupId = groupData[0].id;
    console.log('Group created with ID:', groupId);

    // 2. Add the creator as an Admin
    try {
      await addUserToGroup(groupId, userId, 'Admin');
      console.log('Added creator as Admin to group');
    } catch (error) {
      console.error('Error adding creator as Admin:', error);
      throw new Error('Failed to add creator as Admin: ' + (error as Error).message);
    }

    // 3. Import players
    // Note: The order of players in the import file is preserved in the database
    // This is important for maintaining player references in matches
    console.log('Importing players...');
    try {
      // Store original player indices to ensure correct ordering
      const playerIndexMap = new Map<string, number>();

      // Insert players sequentially to maintain order
      for (let i = 0; i < importData.players.length; i++) {
        const player = importData.players[i];
        const validatedPlayer = validatePlayer({
          ...player,
          group_id: groupId,
          // Store the original index as metadata
          metadata: { original_index: i }
        });

        // Store the mapping of player name to original index
        playerIndexMap.set(player.name, i);

        await supabase
          .from('players')
          .insert(validatedPlayer);
      }

      console.log('All players imported successfully');
    } catch (error) {
      console.error('Error importing players:', error);
      throw new Error('Failed to import players: ' + (error as Error).message);
    }

    // 4. Get the newly created players to map IDs
    console.log('Retrieving created players...');
    const { data: createdPlayers, error: playersError } = await supabase
      .from('players')
      .select('id, name, created_at, metadata')
      .eq('group_id', groupId);

    // Sort players by original_index if available in metadata, otherwise by created_at
    if (createdPlayers) {
      createdPlayers.sort((a, b) => {
        // If both players have metadata with original_index, use that for sorting
        if (a.metadata?.original_index !== undefined && b.metadata?.original_index !== undefined) {
          return a.metadata.original_index - b.metadata.original_index;
        }
        // Otherwise fall back to created_at timestamp
        return new Date(a.created_at).getTime() - new Date(b.created_at).getTime();
      });
    }

    if (playersError) {
      console.error('Error retrieving created players:', playersError);
      throw playersError;
    }

    console.log(`Retrieved ${createdPlayers.length} players`);

    // Create a map of player names to IDs for reference in matches
    const playerNameToIdMap = new Map();
    createdPlayers.forEach(player => {
      playerNameToIdMap.set(player.name, player.id);
    });

    // 5. Import matches
    console.log('Importing matches...');
    try {
      // Create a map of original indices to player IDs for direct lookup
      const playerIndexToIdMap = new Map<number, number>();

      // Log the mapping for debugging
      console.log('Player mapping:');
      importData.players.forEach((player, index) => {
        // Find the corresponding player in createdPlayers by name
        const createdPlayer = createdPlayers.find(p => p.name === player.name);
        if (createdPlayer) {
          playerIndexToIdMap.set(index, createdPlayer.id);
          console.log(`  Index ${index} (${player.name}) -> ID ${createdPlayer.id}`);
        } else {
          console.warn(`  Could not find created player for ${player.name} (index ${index})`);
        }
      });

      const matchPromises = importData.matches.map((match, index) => {
        let teamaIds: number[] = [];
        let teambIds: number[] = [];

        // Handle name-based team references (preferred)
        if (match.teamaByName && match.teamaByName.length > 0) {
          teamaIds = match.teamaByName.map(playerName => {
            const playerId = playerNameToIdMap.get(playerName);
            if (playerId !== undefined) {
              return playerId;
            }
            console.warn(`Player not found: ${playerName} in match ${index} teamA`);
            return null;
          }).filter(id => id !== null) as number[];
        }
        // Fall back to index-based references if name-based not available
        else if (match.teama && match.teama.length > 0) {
          teamaIds = match.teama.map(playerIndex => {
            const playerId = playerIndexToIdMap.get(playerIndex);
            if (playerId !== undefined) {
              return playerId;
            }
            console.warn(`Invalid player index ${playerIndex} in match ${index} teamA`);
            return null;
          }).filter(id => id !== null) as number[];
        }

        // Handle name-based team references for team B (preferred)
        if (match.teambByName && match.teambByName.length > 0) {
          teambIds = match.teambByName.map(playerName => {
            const playerId = playerNameToIdMap.get(playerName);
            if (playerId !== undefined) {
              return playerId;
            }
            console.warn(`Player not found: ${playerName} in match ${index} teamB`);
            return null;
          }).filter(id => id !== null) as number[];
        }
        // Fall back to index-based references if name-based not available
        else if (match.teamb && match.teamb.length > 0) {
          teambIds = match.teamb.map(playerIndex => {
            const playerId = playerIndexToIdMap.get(playerIndex);
            if (playerId !== undefined) {
              return playerId;
            }
            console.warn(`Invalid player index ${playerIndex} in match ${index} teamB`);
            return null;
          }).filter(id => id !== null) as number[];
        }

        // Convert goalscorers to use actual player IDs
        const goalscorers = match.goalscorers?.map((scorer, scorerIndex) => {
          const playerIndex = scorer.playerId;
          const playerId = playerIndexToIdMap.get(playerIndex);
          if (playerId !== undefined) {
            return {
              team: scorer.team,
              playerId: playerId
            };
          }
          console.warn(`Invalid player index ${playerIndex} in match ${index} goalscorer ${scorerIndex}`);
          return null;
        }).filter(scorer => scorer !== null) || [];

        // Create the validated match object
        try {
          const validatedMatch = validateMatch({
            match_date: match.match_date,
            teama: teamaIds,
            teamb: teambIds,
            scorea: match.scorea,
            scoreb: match.scoreb,
            winner: match.winner,
            goalscorers,
            youtubelink: match.youtubelink || '',
            group_id: groupId
          });

          return supabase
            .from('matches')
            .insert(validatedMatch);
        } catch (error) {
          console.error(`Error validating match ${index}:`, error);
          throw new Error(`Failed to validate match ${index}: ${(error as Error).message}`);
        }
      });

      await Promise.all(matchPromises);
      console.log('All matches imported successfully');
    } catch (error) {
      console.error('Error importing matches:', error);
      throw new Error('Failed to import matches: ' + (error as Error).message);
    }

    console.log('Import completed successfully');
    return groupData;
  } catch (error) {
    console.error('Error in createGroupWithImportManual:', error);
    throw error;
  }
};
