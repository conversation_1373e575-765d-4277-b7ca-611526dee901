import { supabase } from '../supabase';
import { rateLimit } from '../middleware/rateLimit';
import { validatePlayer, validateMatch } from '../validators';
import type { Database } from '../database.types';

type Tables = Database['public']['Tables'];
type Player = Tables['players']['Row'];
type Match = Tables['matches']['Row'];
type Chemistry = Tables['chemistry']['Row'];

export class APIError extends Error {
  constructor(
    message: string,
    public code: string,
    public status: number = 500
  ) {
    super(message);
    this.name = 'APIError';
  }
}

export class API {
  private static client = supabase;
  private static cache = new Map<string, { data: any; timestamp: number }>();
  private static pendingRequests = new Map<string, Promise<any>>();
  private static CACHE_TTL = 30000; // 30 seconds

  private static getCacheKey(method: string, params: any[]): string {
    return `${method}:${JSON.stringify(params)}`;
  }

  private static async withCache<T>(
    method: string,
    params: any[],
    fetcher: () => Promise<T>
  ): Promise<T> {
    const cacheKey = this.getCacheKey(method, params);

    // Check cache
    const cached = this.cache.get(cacheKey);
    if (cached && Date.now() - cached.timestamp < this.CACHE_TTL) {
      return cached.data;
    }

    // Check pending requests
    const pending = this.pendingRequests.get(cacheKey);
    if (pending) return pending;

    // Execute new request
    const promise = fetcher().then(result => {
      this.cache.set(cacheKey, { data: result, timestamp: Date.now() });
      this.pendingRequests.delete(cacheKey);
      return result;
    });

    this.pendingRequests.set(cacheKey, promise);
    return promise;
  }

  static setClient(newClient: typeof supabase) {
    this.client = newClient;
  }

  static async getPlayers(groupId?: string): Promise<Player[]> {
    return this.withCache('getPlayers', [groupId], async () => {
      await rateLimit();
      let query = this.client
        .from('players')
        .select('*')
        .order('name');

      if (groupId) {
        query = query.eq('group_id', groupId);
      }

      const { data, error } = await query;

      if (error) throw new APIError(error.message, error.code);
      return data || [];
    });
  }

  static async createPlayer(player: Omit<Player, 'id' | 'created_at'>): Promise<Player> {
    await rateLimit();
    const validatedData = validatePlayer(player);

    // Ensure group_id is included in the player data
    if (!validatedData.group_id) {
      throw new APIError('Group ID is required', 'VALIDATION_ERROR', 400);
    }

    const { data, error } = await this.client
      .from('players')
      .insert(validatedData)
      .select()
      .single();

    if (error) throw new APIError(error.message, error.code);
    return data;
  }

  static async updatePlayer(id: number, player: Partial<Player>): Promise<Player> {
    await rateLimit();
    const validatedData = validatePlayer({ ...await this.getPlayer(id), ...player });

    const { data, error } = await this.client
      .from('players')
      .update(validatedData)
      .eq('id', id)
      .select()
      .single();

    if (error) throw new APIError(error.message, error.code);
    if (!data) throw new APIError('Player not found', 'NOT_FOUND', 404);
    return data;
  }

  static async getPlayer(id: number, groupId?: string): Promise<Player> {
    await rateLimit();
    let query = this.client
      .from('players')
      .select()
      .eq('id', id);

    if (groupId) {
      query = query.eq('group_id', groupId);
    }

    const { data, error } = await query.single();

    if (error) throw new APIError(error.message, error.code);
    if (!data) throw new APIError('Player not found', 'NOT_FOUND', 404);
    return data;
  }

  static async deletePlayer(id: number, groupId?: string): Promise<void> {
    await rateLimit();
    let query = this.client
      .from('players')
      .delete()
      .eq('id', id);

    if (groupId) {
      query = query.eq('group_id', groupId);
    }

    const { error } = await query;

    if (error) throw new APIError(error.message, error.code);
  }

  static async getMatches(groupId?: string): Promise<Match[]> {
    await rateLimit();
    let query = this.client
      .from('matches')
      .select('*')
      .order('match_date', { ascending: false });

    if (groupId) {
      query = query.eq('group_id', groupId);
    }

    const { data, error } = await query;

    if (error) throw new APIError(error.message, error.code);
    return data || [];
  }

  static async createMatch(match: Omit<Match, 'id'>): Promise<Match> {
    await rateLimit();
    const validatedData = validateMatch(match);

    // Ensure group_id is included in the match data
    if (!validatedData.group_id) {
      throw new APIError('Group ID is required', 'VALIDATION_ERROR', 400);
    }

    const { data, error } = await this.client
      .from('matches')
      .insert(validatedData)
      .select()
      .single();

    if (error) throw new APIError(error.message, error.code);
    if (!data) throw new APIError('Failed to create match', 'INSERT_FAILED', 500);
    return data;
  }

  static async updateMatch(id: number, match: Partial<Match>, groupId?: string): Promise<Match> {
    await rateLimit();
    const currentMatch = await this.getMatch(id, groupId);
    const validatedData = validateMatch({ ...currentMatch, ...match });

    // Ensure group_id is preserved or included
    if (!validatedData.group_id) {
      validatedData.group_id = groupId || currentMatch.group_id;
    }

    let query = this.client
      .from('matches')
      .update(validatedData)
      .eq('id', id);

    if (groupId) {
      query = query.eq('group_id', groupId);
    }

    const { data, error } = await query.select().single();

    if (error) throw new APIError(error.message, error.code);
    if (!data) throw new APIError('Match not found', 'NOT_FOUND', 404);
    return data;
  }

  static async getMatch(id: number, groupId?: string): Promise<Match> {
    await rateLimit();
    let query = this.client
      .from('matches')
      .select()
      .eq('id', id);

    if (groupId) {
      query = query.eq('group_id', groupId);
    }

    const { data, error } = await query.single();

    if (error) throw new APIError(error.message, error.code);
    if (!data) throw new APIError('Match not found', 'NOT_FOUND', 404);
    return data;
  }

  static async deleteMatch(id: number, groupId?: string): Promise<void> {
    await rateLimit();
    let query = this.client
      .from('matches')
      .delete()
      .eq('id', id);

    if (groupId) {
      query = query.eq('group_id', groupId);
    }

    const { error } = await query;

    if (error) throw new APIError(error.message, error.code);
  }

  static async updateChemistry(player1Id: number, player2Id: number, won: boolean, groupId?: string): Promise<void> {
    await rateLimit();
    const { error } = await this.client.rpc('update_chemistry', {
      p1_id: player1Id,
      p2_id: player2Id,
      did_win: won,
      group_id: groupId
    });

    if (error) throw new APIError(error.message, error.code);
  }

  static async signIn(email: string, password: string): Promise<void> {
    const { error } = await this.client.auth.signInWithPassword({
      email,
      password,
    });

    if (error) throw new APIError(error.message, 'AUTH_ERROR', 401);
  }

  static async signOut(): Promise<void> {
    const { error } = await this.client.auth.signOut();
    if (error) throw new APIError(error.message, 'AUTH_ERROR', 401);
  }

  static async resetTestData(): Promise<void> {
    if (process.env.NODE_ENV !== 'test') {
      throw new Error('resetTestData can only be called in test environment');
    }
    await this.client.from('players').delete().neq('id', 0);
    await this.client.from('matches').delete().neq('id', 0);
    await this.client.from('chemistry').delete().neq('id', 0);
  }
}
