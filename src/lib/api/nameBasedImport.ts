import { supabase } from '@/lib/supabase';
import { validatePlayer, validateMatch } from '@/lib/validators';

// Define the structure of the name-based import data
export interface NameBasedPlayer {
  name: string;
  skills: number;
  effort: number;
  stamina: number;
}

export interface NameBasedMatch {
  match_date: string;
  teamaByName: string[];
  teambByName: string[];
  scorea: number | null;
  scoreb: number | null;
  winner: 'A' | 'B' | 'Draw' | null;
  goalscorers?: any[];
  youtubelink?: string;
}

export interface NameBasedImportData {
  players: NameBasedPlayer[];
  matches: NameBasedMatch[];
}

/**
 * Creates a new group with imported data using player names for references
 * @param name The name of the group
 * @param userId The ID of the user creating the group
 * @param importData The data to import
 * @returns The created group
 */
export const createGroupWithNameBasedImport = async (
  name: string,
  userId: string,
  importData: NameBasedImportData
) => {
  try {
    console.log('Starting name-based import process for group:', name);
    console.log('Import data summary:', {
      playerCount: importData.players.length,
      matchCount: importData.matches.length
    });

    // 1. Create the group
    const { data: groupData, error: groupError } = await supabase
      .from('friend_groups')
      .insert([{ name, created_by: userId }])
      .select();

    if (groupError) {
      console.error('Error creating group:', groupError);
      throw groupError;
    }

    if (!groupData || groupData.length === 0) {
      throw new Error('Failed to create group - no data returned');
    }

    const groupId = groupData[0].id;
    console.log('Group created with ID:', groupId);

    // 2. Add the creator as an Admin
    try {
      await addUserToGroup(groupId, userId, 'Admin');
      console.log('Added creator as Admin to group');
    } catch (error) {
      console.error('Error adding creator as Admin:', error);
      throw new Error('Failed to add creator as Admin: ' + (error as Error).message);
    }

    // 3. Import players
    console.log('Importing players...');
    const createdPlayers: { id: number; name: string }[] = [];
    
    try {
      // Insert players sequentially to maintain order
      for (const player of importData.players) {
        const validatedPlayer = validatePlayer({
          ...player,
          group_id: groupId
        });

        const { data, error } = await supabase
          .from('players')
          .insert(validatedPlayer)
          .select('id, name');

        if (error) {
          throw error;
        }

        if (data && data.length > 0) {
          createdPlayers.push(data[0]);
        }
      }
      
      console.log(`Imported ${createdPlayers.length} players successfully`);
    } catch (error) {
      console.error('Error importing players:', error);
      throw new Error('Failed to import players: ' + (error as Error).message);
    }

    // Create a map of player names to IDs for reference in matches
    const playerNameToIdMap = new Map<string, number>();
    createdPlayers.forEach(player => {
      playerNameToIdMap.set(player.name, player.id);
    });

    // 5. Import matches
    console.log('Importing matches...');
    try {
      const matchPromises = importData.matches.map((match, index) => {
        // Get the actual player IDs using player names
        const teamaIds = match.teamaByName.map(playerName => {
          const playerId = playerNameToIdMap.get(playerName);
          if (playerId !== undefined) {
            return playerId;
          }
          console.warn(`Player not found: ${playerName} in match ${index} teamA`);
          return null;
        }).filter(id => id !== null) as number[];

        const teambIds = match.teambByName.map(playerName => {
          const playerId = playerNameToIdMap.get(playerName);
          if (playerId !== undefined) {
            return playerId;
          }
          console.warn(`Player not found: ${playerName} in match ${index} teamB`);
          return null;
        }).filter(id => id !== null) as number[];

        // Convert goalscorers to use actual player IDs
        // Note: This assumes goalscorers still use indices, modify if needed
        const goalscorers = match.goalscorers?.map((scorer, scorerIndex) => {
          if (typeof scorer.playerId === 'number' && scorer.playerId >= 0 && scorer.playerId < importData.players.length) {
            const playerName = importData.players[scorer.playerId].name;
            const playerId = playerNameToIdMap.get(playerName);
            if (playerId !== undefined) {
              return {
                team: scorer.team,
                playerId: playerId
              };
            }
          }
          console.warn(`Invalid player reference in match ${index} goalscorer ${scorerIndex}`);
          return null;
        }).filter(scorer => scorer !== null) || [];

        // Create the validated match object
        try {
          const validatedMatch = validateMatch({
            match_date: match.match_date,
            teama: teamaIds,
            teamb: teambIds,
            scorea: match.scorea,
            scoreb: match.scoreb,
            winner: match.winner,
            goalscorers,
            youtubelink: match.youtubelink || '',
            group_id: groupId
          });

          return supabase
            .from('matches')
            .insert(validatedMatch);
        } catch (error) {
          console.error(`Error validating match ${index}:`, error);
          throw new Error(`Failed to validate match ${index}: ${(error as Error).message}`);
        }
      });

      await Promise.all(matchPromises);
      console.log('All matches imported successfully');
    } catch (error) {
      console.error('Error importing matches:', error);
      throw new Error('Failed to import matches: ' + (error as Error).message);
    }

    console.log('Import completed successfully');
    return groupData;
  } catch (error) {
    console.error('Error in createGroupWithNameBasedImport:', error);
    throw error;
  }
};

// Helper function to add a user to a group (copied from groups.ts)
async function addUserToGroup(groupId: string, userId: string, role: 'Admin' | 'Collaborator' | 'Guest') {
  // Check if user is already a member
  const { data: existingMember, error: checkError } = await supabase
    .from('group_members')
    .select('id, role')
    .eq('group_id', groupId)
    .eq('user_id', userId)
    .maybeSingle();

  if (checkError) throw checkError;

  // If user is already a member, update their role if different
  if (existingMember) {
    if (existingMember.role !== role) {
      const { data, error } = await supabase
        .from('group_members')
        .update({ role })
        .eq('id', existingMember.id)
        .select();

      if (error) throw error;
      return data;
    }
    return [existingMember]; // Return existing member in array format for consistency
  }

  // Otherwise, add the user as a new member
  const { data, error } = await supabase
    .from('group_members')
    .insert([{ group_id: groupId, user_id: userId, role }])
    .select();

  if (error) throw error;
  return data;
}
