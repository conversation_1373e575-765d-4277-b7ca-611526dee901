/**
 * Enhanced API service with offline support
 */

import { supabase } from '../supabase';
import { rateLimit } from '../middleware/rateLimit';
import { isOffline } from '../offline/offlineDetector';
import { 
  addToSyncQueue, 
  OperationType, 
  EntityType,
  SyncOperation
} from '../offline/syncQueue';
import {
  getCache,
  setCache,
  CACHE_KEYS,
  CACHE_EXPIRY,
  removeCache
} from '../cache/storage';

// Error class
export class APIError extends Error {
  constructor(
    message: string,
    public code: string,
    public status: number = 500
  ) {
    super(message);
    this.name = 'APIError';
  }
}

// Entity interfaces
export interface Player {
  id: number;
  created_at: string;
  name: string;
  skills: number;
  effort: number;
  stamina: number;
  group_id?: string;
  rating?: number;
  avatar_url?: string | null;
}

export interface Match {
  id: number;
  created_at: string;
  match_date: string;
  team_a: number[];
  team_b: number[];
  score_a: number;
  score_b: number;
  group_id?: string;
  location?: string;
  notes?: string;
  video_url?: string;
}

export interface Chemistry {
  id: number;
  created_at: string;
  player1_id: number;
  player2_id: number;
  wins: number;
  losses: number;
  group_id?: string;
}

export interface Comment {
  id: string;
  created_at: string;
  match_id: number;
  user_id: string;
  content: string;
  group_id?: string;
}

// Offline API implementation
export class OfflineAPI {
  private static client = supabase;
  private static pendingRequests = new Map<string, Promise<any>>();

  // Cache TTL values
  private static CACHE_TTL = {
    PLAYERS: CACHE_EXPIRY.LONG,
    MATCHES: CACHE_EXPIRY.MEDIUM,
    CHEMISTRY: CACHE_EXPIRY.MEDIUM,
    COMMENTS: CACHE_EXPIRY.SHORT,
  };

  /**
   * Get a cache key for a specific entity and group
   * @param entity Entity type
   * @param groupId Optional group ID
   * @returns Cache key
   */
  private static getCacheKey(entity: string, groupId?: string): string {
    return groupId ? `${entity}_${groupId}` : entity;
  }

  /**
   * Get a unique request key
   * @param method Method name
   * @param params Method parameters
   * @returns Request key
   */
  private static getRequestKey(method: string, params: any[]): string {
    return `${method}:${JSON.stringify(params)}`;
  }

  /**
   * Execute a method with caching and offline support
   * @param method Method name
   * @param params Method parameters
   * @param fetcher Function to fetch data
   * @param cacheKey Cache key
   * @param cacheTTL Cache TTL
   * @returns Promise with result
   */
  private static async executeMethod<T>(
    method: string,
    params: any[],
    fetcher: () => Promise<T>,
    cacheKey: string,
    cacheTTL: number | null = CACHE_EXPIRY.MEDIUM
  ): Promise<T> {
    const requestKey = this.getRequestKey(method, params);

    // Check for pending request
    const pendingRequest = this.pendingRequests.get(requestKey);
    if (pendingRequest) {
      return pendingRequest;
    }

    // Check cache first
    const cachedData = getCache<T>(cacheKey);
    
    // If offline, return cached data or throw error
    if (isOffline()) {
      if (cachedData !== null) {
        return cachedData;
      }
      throw new APIError('You are offline and no cached data is available', 'OFFLINE_NO_CACHE', 503);
    }

    // If online, try to fetch fresh data
    try {
      const promise = fetcher().then(result => {
        // Update cache with fresh data
        if (result) {
          setCache(cacheKey, result, cacheTTL);
        }
        this.pendingRequests.delete(requestKey);
        return result;
      }).catch(error => {
        this.pendingRequests.delete(requestKey);
        
        // If network error and we have cached data, return that
        if (error.message.includes('network') && cachedData !== null) {
          console.warn('Network error, using cached data:', error);
          return cachedData;
        }
        
        throw error;
      });

      this.pendingRequests.set(requestKey, promise);
      return promise;
    } catch (error) {
      // If any error occurs and we have cached data, return that
      if (cachedData !== null) {
        console.warn('Error fetching data, using cached data:', error);
        return cachedData;
      }
      throw error;
    }
  }

  /**
   * Process a sync operation
   * @param operation Operation to process
   */
  static async processOperation(operation: SyncOperation): Promise<void> {
    const { type, entity, data, groupId } = operation;

    switch (entity) {
      case EntityType.PLAYER:
        if (type === OperationType.CREATE) {
          await this.createPlayer(data);
        } else if (type === OperationType.UPDATE) {
          await this.updatePlayer(data.id, data);
        } else if (type === OperationType.DELETE) {
          await this.deletePlayer(data.id);
        }
        break;

      case EntityType.MATCH:
        if (type === OperationType.CREATE) {
          await this.createMatch(data);
        } else if (type === OperationType.UPDATE) {
          await this.updateMatch(data.id, data);
        } else if (type === OperationType.DELETE) {
          await this.deleteMatch(data.id);
        }
        break;

      case EntityType.CHEMISTRY:
        if (type === OperationType.UPDATE) {
          await this.updateChemistry(data.player1Id, data.player2Id, data.won, groupId);
        }
        break;

      case EntityType.COMMENT:
        if (type === OperationType.CREATE) {
          await this.createComment(data);
        } else if (type === OperationType.DELETE) {
          await this.deleteComment(data.id);
        }
        break;

      default:
        throw new Error(`Unknown entity type: ${entity}`);
    }
  }

  // Player methods
  static async getPlayers(groupId?: string): Promise<Player[]> {
    const cacheKey = this.getCacheKey(CACHE_KEYS.PLAYERS, groupId);
    
    return this.executeMethod(
      'getPlayers',
      [groupId],
      async () => {
        await rateLimit();
        let query = this.client
          .from('players')
          .select('*')
          .order('name');

        if (groupId) {
          query = query.eq('group_id', groupId);
        }

        const { data, error } = await query;

        if (error) throw new APIError(error.message, error.code);
        return data || [];
      },
      cacheKey,
      this.CACHE_TTL.PLAYERS
    );
  }

  static async getPlayer(id: number, groupId?: string): Promise<Player> {
    const cacheKey = `${CACHE_KEYS.PLAYERS}_${id}`;
    
    return this.executeMethod(
      'getPlayer',
      [id, groupId],
      async () => {
        await rateLimit();
        let query = this.client
          .from('players')
          .select()
          .eq('id', id);

        if (groupId) {
          query = query.eq('group_id', groupId);
        }

        const { data, error } = await query.single();

        if (error) throw new APIError(error.message, error.code);
        if (!data) throw new APIError('Player not found', 'NOT_FOUND', 404);
        return data;
      },
      cacheKey,
      this.CACHE_TTL.PLAYERS
    );
  }

  static async createPlayer(player: Omit<Player, 'id' | 'created_at'>): Promise<Player> {
    // If offline, add to sync queue and return optimistic response
    if (isOffline()) {
      // Generate a temporary negative ID for optimistic UI
      const tempId = -Math.floor(Math.random() * 1000000);
      
      const optimisticPlayer: Player = {
        id: tempId,
        created_at: new Date().toISOString(),
        ...player,
      };
      
      // Add to sync queue
      addToSyncQueue(
        OperationType.CREATE,
        EntityType.PLAYER,
        player,
        player.group_id
      );
      
      // Update cache with optimistic data
      const groupId = player.group_id;
      const cacheKey = this.getCacheKey(CACHE_KEYS.PLAYERS, groupId);
      const cachedPlayers = getCache<Player[]>(cacheKey) || [];
      setCache(cacheKey, [...cachedPlayers, optimisticPlayer], this.CACHE_TTL.PLAYERS);
      
      return optimisticPlayer;
    }
    
    // If online, proceed with normal API call
    await rateLimit();
    
    // Ensure group_id is included
    if (!player.group_id) {
      throw new APIError('Group ID is required', 'VALIDATION_ERROR', 400);
    }
    
    const { data, error } = await this.client
      .from('players')
      .insert(player)
      .select()
      .single();
    
    if (error) throw new APIError(error.message, error.code);
    if (!data) throw new APIError('Failed to create player', 'INSERT_FAILED', 500);
    
    // Update cache
    const groupId = player.group_id;
    const cacheKey = this.getCacheKey(CACHE_KEYS.PLAYERS, groupId);
    const cachedPlayers = getCache<Player[]>(cacheKey) || [];
    setCache(cacheKey, [...cachedPlayers, data], this.CACHE_TTL.PLAYERS);
    
    return data;
  }

  static async updatePlayer(id: number, player: Partial<Player>): Promise<Player> {
    // If offline, add to sync queue and return optimistic response
    if (isOffline()) {
      // Get the current player from cache
      const cacheKey = `${CACHE_KEYS.PLAYERS}_${id}`;
      const currentPlayer = getCache<Player>(cacheKey);
      
      if (!currentPlayer) {
        throw new APIError('Cannot update player while offline: player not in cache', 'OFFLINE_NO_CACHE', 503);
      }
      
      const updatedPlayer = { ...currentPlayer, ...player };
      
      // Add to sync queue
      addToSyncQueue(
        OperationType.UPDATE,
        EntityType.PLAYER,
        updatedPlayer,
        updatedPlayer.group_id
      );
      
      // Update individual player cache
      setCache(cacheKey, updatedPlayer, this.CACHE_TTL.PLAYERS);
      
      // Update players list cache
      const groupId = updatedPlayer.group_id;
      const listCacheKey = this.getCacheKey(CACHE_KEYS.PLAYERS, groupId);
      const cachedPlayers = getCache<Player[]>(listCacheKey) || [];
      const updatedPlayers = cachedPlayers.map(p => 
        p.id === id ? updatedPlayer : p
      );
      setCache(listCacheKey, updatedPlayers, this.CACHE_TTL.PLAYERS);
      
      return updatedPlayer;
    }
    
    // If online, proceed with normal API call
    await rateLimit();
    
    // Get current player to merge with updates
    const currentPlayer = await this.getPlayer(id);
    const updatedPlayer = { ...currentPlayer, ...player };
    
    const { data, error } = await this.client
      .from('players')
      .update(updatedPlayer)
      .eq('id', id)
      .select()
      .single();
    
    if (error) throw new APIError(error.message, error.code);
    if (!data) throw new APIError('Player not found', 'NOT_FOUND', 404);
    
    // Update caches
    const cacheKey = `${CACHE_KEYS.PLAYERS}_${id}`;
    setCache(cacheKey, data, this.CACHE_TTL.PLAYERS);
    
    const groupId = data.group_id;
    const listCacheKey = this.getCacheKey(CACHE_KEYS.PLAYERS, groupId);
    const cachedPlayers = getCache<Player[]>(listCacheKey) || [];
    const updatedPlayers = cachedPlayers.map(p => 
      p.id === id ? data : p
    );
    setCache(listCacheKey, updatedPlayers, this.CACHE_TTL.PLAYERS);
    
    return data;
  }

  static async deletePlayer(id: number): Promise<void> {
    // If offline, add to sync queue and update cache
    if (isOffline()) {
      // Get the current player from cache
      const cacheKey = `${CACHE_KEYS.PLAYERS}_${id}`;
      const currentPlayer = getCache<Player>(cacheKey);
      
      if (!currentPlayer) {
        throw new APIError('Cannot delete player while offline: player not in cache', 'OFFLINE_NO_CACHE', 503);
      }
      
      // Add to sync queue
      addToSyncQueue(
        OperationType.DELETE,
        EntityType.PLAYER,
        { id },
        currentPlayer.group_id
      );
      
      // Remove from individual cache
      removeCache(cacheKey);
      
      // Update players list cache
      const groupId = currentPlayer.group_id;
      const listCacheKey = this.getCacheKey(CACHE_KEYS.PLAYERS, groupId);
      const cachedPlayers = getCache<Player[]>(listCacheKey) || [];
      const updatedPlayers = cachedPlayers.filter(p => p.id !== id);
      setCache(listCacheKey, updatedPlayers, this.CACHE_TTL.PLAYERS);
      
      return;
    }
    
    // If online, proceed with normal API call
    await rateLimit();
    
    // Get the player first to know its group_id
    const player = await this.getPlayer(id);
    
    const { error } = await this.client
      .from('players')
      .delete()
      .eq('id', id);
    
    if (error) throw new APIError(error.message, error.code);
    
    // Update caches
    const cacheKey = `${CACHE_KEYS.PLAYERS}_${id}`;
    removeCache(cacheKey);
    
    const groupId = player.group_id;
    const listCacheKey = this.getCacheKey(CACHE_KEYS.PLAYERS, groupId);
    const cachedPlayers = getCache<Player[]>(listCacheKey) || [];
    const updatedPlayers = cachedPlayers.filter(p => p.id !== id);
    setCache(listCacheKey, updatedPlayers, this.CACHE_TTL.PLAYERS);
  }

  // Match methods
  static async getMatches(groupId?: string): Promise<Match[]> {
    const cacheKey = this.getCacheKey(CACHE_KEYS.MATCHES, groupId);
    
    return this.executeMethod(
      'getMatches',
      [groupId],
      async () => {
        await rateLimit();
        let query = this.client
          .from('matches')
          .select('*')
          .order('match_date', { ascending: false });

        if (groupId) {
          query = query.eq('group_id', groupId);
        }

        const { data, error } = await query;

        if (error) throw new APIError(error.message, error.code);
        return data || [];
      },
      cacheKey,
      this.CACHE_TTL.MATCHES
    );
  }

  static async getMatch(id: number, groupId?: string): Promise<Match> {
    const cacheKey = `${CACHE_KEYS.MATCHES}_${id}`;
    
    return this.executeMethod(
      'getMatch',
      [id, groupId],
      async () => {
        await rateLimit();
        let query = this.client
          .from('matches')
          .select()
          .eq('id', id);

        if (groupId) {
          query = query.eq('group_id', groupId);
        }

        const { data, error } = await query.single();

        if (error) throw new APIError(error.message, error.code);
        if (!data) throw new APIError('Match not found', 'NOT_FOUND', 404);
        return data;
      },
      cacheKey,
      this.CACHE_TTL.MATCHES
    );
  }

  static async createMatch(match: Omit<Match, 'id' | 'created_at'>): Promise<Match> {
    // If offline, add to sync queue and return optimistic response
    if (isOffline()) {
      // Generate a temporary negative ID for optimistic UI
      const tempId = -Math.floor(Math.random() * 1000000);
      
      const optimisticMatch: Match = {
        id: tempId,
        created_at: new Date().toISOString(),
        ...match,
      };
      
      // Add to sync queue
      addToSyncQueue(
        OperationType.CREATE,
        EntityType.MATCH,
        match,
        match.group_id
      );
      
      // Update cache with optimistic data
      const groupId = match.group_id;
      const cacheKey = this.getCacheKey(CACHE_KEYS.MATCHES, groupId);
      const cachedMatches = getCache<Match[]>(cacheKey) || [];
      setCache(cacheKey, [optimisticMatch, ...cachedMatches], this.CACHE_TTL.MATCHES);
      
      return optimisticMatch;
    }
    
    // If online, proceed with normal API call
    await rateLimit();
    
    // Ensure group_id is included
    if (!match.group_id) {
      throw new APIError('Group ID is required', 'VALIDATION_ERROR', 400);
    }
    
    const { data, error } = await this.client
      .from('matches')
      .insert(match)
      .select()
      .single();
    
    if (error) throw new APIError(error.message, error.code);
    if (!data) throw new APIError('Failed to create match', 'INSERT_FAILED', 500);
    
    // Update cache
    const groupId = match.group_id;
    const cacheKey = this.getCacheKey(CACHE_KEYS.MATCHES, groupId);
    const cachedMatches = getCache<Match[]>(cacheKey) || [];
    setCache(cacheKey, [data, ...cachedMatches], this.CACHE_TTL.MATCHES);
    
    return data;
  }

  static async updateMatch(id: number, match: Partial<Match>, groupId?: string): Promise<Match> {
    // If offline, add to sync queue and return optimistic response
    if (isOffline()) {
      // Get the current match from cache
      const cacheKey = `${CACHE_KEYS.MATCHES}_${id}`;
      const currentMatch = getCache<Match>(cacheKey);
      
      if (!currentMatch) {
        throw new APIError('Cannot update match while offline: match not in cache', 'OFFLINE_NO_CACHE', 503);
      }
      
      const updatedMatch = { ...currentMatch, ...match };
      
      // Add to sync queue
      addToSyncQueue(
        OperationType.UPDATE,
        EntityType.MATCH,
        updatedMatch,
        updatedMatch.group_id
      );
      
      // Update individual match cache
      setCache(cacheKey, updatedMatch, this.CACHE_TTL.MATCHES);
      
      // Update matches list cache
      const effectiveGroupId = updatedMatch.group_id || groupId;
      const listCacheKey = this.getCacheKey(CACHE_KEYS.MATCHES, effectiveGroupId);
      const cachedMatches = getCache<Match[]>(listCacheKey) || [];
      const updatedMatches = cachedMatches.map(m => 
        m.id === id ? updatedMatch : m
      );
      setCache(listCacheKey, updatedMatches, this.CACHE_TTL.MATCHES);
      
      return updatedMatch;
    }
    
    // If online, proceed with normal API call
    await rateLimit();
    
    // Get current match to merge with updates
    const currentMatch = await this.getMatch(id, groupId);
    const updatedMatch = { ...currentMatch, ...match };
    
    // Ensure group_id is preserved or included
    if (!updatedMatch.group_id) {
      updatedMatch.group_id = groupId || currentMatch.group_id;
    }
    
    let query = this.client
      .from('matches')
      .update(updatedMatch)
      .eq('id', id);
    
    if (groupId) {
      query = query.eq('group_id', groupId);
    }
    
    const { data, error } = await query.select().single();
    
    if (error) throw new APIError(error.message, error.code);
    if (!data) throw new APIError('Match not found', 'NOT_FOUND', 404);
    
    // Update caches
    const cacheKey = `${CACHE_KEYS.MATCHES}_${id}`;
    setCache(cacheKey, data, this.CACHE_TTL.MATCHES);
    
    const effectiveGroupId = data.group_id || groupId;
    const listCacheKey = this.getCacheKey(CACHE_KEYS.MATCHES, effectiveGroupId);
    const cachedMatches = getCache<Match[]>(listCacheKey) || [];
    const updatedMatches = cachedMatches.map(m => 
      m.id === id ? data : m
    );
    setCache(listCacheKey, updatedMatches, this.CACHE_TTL.MATCHES);
    
    return data;
  }

  static async deleteMatch(id: number, groupId?: string): Promise<void> {
    // If offline, add to sync queue and update cache
    if (isOffline()) {
      // Get the current match from cache
      const cacheKey = `${CACHE_KEYS.MATCHES}_${id}`;
      const currentMatch = getCache<Match>(cacheKey);
      
      if (!currentMatch) {
        throw new APIError('Cannot delete match while offline: match not in cache', 'OFFLINE_NO_CACHE', 503);
      }
      
      // Add to sync queue
      addToSyncQueue(
        OperationType.DELETE,
        EntityType.MATCH,
        { id },
        currentMatch.group_id || groupId
      );
      
      // Remove from individual cache
      removeCache(cacheKey);
      
      // Update matches list cache
      const effectiveGroupId = currentMatch.group_id || groupId;
      const listCacheKey = this.getCacheKey(CACHE_KEYS.MATCHES, effectiveGroupId);
      const cachedMatches = getCache<Match[]>(listCacheKey) || [];
      const updatedMatches = cachedMatches.filter(m => m.id !== id);
      setCache(listCacheKey, updatedMatches, this.CACHE_TTL.MATCHES);
      
      return;
    }
    
    // If online, proceed with normal API call
    await rateLimit();
    
    // Get the match first to know its group_id
    const match = await this.getMatch(id, groupId);
    
    let query = this.client
      .from('matches')
      .delete()
      .eq('id', id);
    
    if (groupId) {
      query = query.eq('group_id', groupId);
    }
    
    const { error } = await query;
    
    if (error) throw new APIError(error.message, error.code);
    
    // Update caches
    const cacheKey = `${CACHE_KEYS.MATCHES}_${id}`;
    removeCache(cacheKey);
    
    const effectiveGroupId = match.group_id || groupId;
    const listCacheKey = this.getCacheKey(CACHE_KEYS.MATCHES, effectiveGroupId);
    const cachedMatches = getCache<Match[]>(listCacheKey) || [];
    const updatedMatches = cachedMatches.filter(m => m.id !== id);
    setCache(listCacheKey, updatedMatches, this.CACHE_TTL.MATCHES);
  }

  // Chemistry methods
  static async updateChemistry(player1Id: number, player2Id: number, won: boolean, groupId?: string): Promise<void> {
    // If offline, add to sync queue
    if (isOffline()) {
      addToSyncQueue(
        OperationType.UPDATE,
        EntityType.CHEMISTRY,
        { player1Id, player2Id, won },
        groupId
      );
      return;
    }
    
    // If online, proceed with normal API call
    await rateLimit();
    
    const { error } = await this.client.rpc('update_chemistry', {
      p1_id: player1Id,
      p2_id: player2Id,
      did_win: won,
      group_id: groupId
    });
    
    if (error) throw new APIError(error.message, error.code);
    
    // Invalidate chemistry cache
    const cacheKey = this.getCacheKey(CACHE_KEYS.CHEMISTRY, groupId);
    removeCache(cacheKey);
  }

  // Comment methods
  static async getComments(matchId: number, groupId?: string): Promise<Comment[]> {
    const cacheKey = `comments_match_${matchId}`;
    
    return this.executeMethod(
      'getComments',
      [matchId, groupId],
      async () => {
        await rateLimit();
        let query = this.client
          .from('match_comments')
          .select('*')
          .eq('match_id', matchId)
          .order('created_at');
        
        if (groupId) {
          query = query.eq('group_id', groupId);
        }
        
        const { data, error } = await query;
        
        if (error) throw new APIError(error.message, error.code);
        return data || [];
      },
      cacheKey,
      this.CACHE_TTL.COMMENTS
    );
  }

  static async createComment(comment: Omit<Comment, 'id' | 'created_at'>): Promise<Comment> {
    // If offline, add to sync queue and return optimistic response
    if (isOffline()) {
      // Generate a temporary ID for optimistic UI
      const tempId = `temp_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`;
      
      const optimisticComment: Comment = {
        id: tempId,
        created_at: new Date().toISOString(),
        ...comment,
      };
      
      // Add to sync queue
      addToSyncQueue(
        OperationType.CREATE,
        EntityType.COMMENT,
        comment,
        comment.group_id
      );
      
      // Update cache with optimistic data
      const cacheKey = `comments_match_${comment.match_id}`;
      const cachedComments = getCache<Comment[]>(cacheKey) || [];
      setCache(cacheKey, [...cachedComments, optimisticComment], this.CACHE_TTL.COMMENTS);
      
      return optimisticComment;
    }
    
    // If online, proceed with normal API call
    await rateLimit();
    
    const { data, error } = await this.client
      .from('match_comments')
      .insert(comment)
      .select()
      .single();
    
    if (error) throw new APIError(error.message, error.code);
    if (!data) throw new APIError('Failed to create comment', 'INSERT_FAILED', 500);
    
    // Update cache
    const cacheKey = `comments_match_${comment.match_id}`;
    const cachedComments = getCache<Comment[]>(cacheKey) || [];
    setCache(cacheKey, [...cachedComments, data], this.CACHE_TTL.COMMENTS);
    
    return data;
  }

  static async deleteComment(id: string): Promise<void> {
    // If offline, add to sync queue and update cache
    if (isOffline()) {
      // We need to find the comment in cache to know its match_id
      // This is a bit tricky since we don't know which match it belongs to
      // We'll need to search through all cached comments
      
      // For now, we'll just queue the delete operation
      // The actual implementation would need to search through cached comments
      
      addToSyncQueue(
        OperationType.DELETE,
        EntityType.COMMENT,
        { id }
      );
      
      return;
    }
    
    // If online, proceed with normal API call
    await rateLimit();
    
    // Get the comment first to know its match_id
    const { data: comment, error: getError } = await this.client
      .from('match_comments')
      .select('match_id')
      .eq('id', id)
      .single();
    
    if (getError) throw new APIError(getError.message, getError.code);
    if (!comment) throw new APIError('Comment not found', 'NOT_FOUND', 404);
    
    const { error } = await this.client
      .from('match_comments')
      .delete()
      .eq('id', id);
    
    if (error) throw new APIError(error.message, error.code);
    
    // Update cache
    const cacheKey = `comments_match_${comment.match_id}`;
    const cachedComments = getCache<Comment[]>(cacheKey) || [];
    const updatedComments = cachedComments.filter(c => c.id !== id);
    setCache(cacheKey, updatedComments, this.CACHE_TTL.COMMENTS);
  }
}
