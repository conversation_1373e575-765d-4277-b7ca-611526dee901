import { supabase } from '@/lib/supabase';
import { SharedLink, AccessLevel, CreateSharedLinkParams, UpdateSharedLinkParams, SharedLinkValidationResponse } from '@/types/sharing';

/**
 * Fetch all shared links for a group
 * @param groupId The ID of the group
 * @returns Array of shared links
 */
export const fetchSharedLinks = async (groupId: string): Promise<SharedLink[]> => {
  try {
    // Check if shared_links table exists
    const { error: tableCheckError } = await supabase
      .from('shared_links')
      .select('count', { count: 'exact', head: true });

    if (tableCheckError) {
      // Table doesn't exist
      if (tableCheckError.code === '42P01') {
        console.log('Shared links table does not exist yet');
        return [];
      } else {
        throw tableCheckError;
      }
    }

    // If table exists, fetch the data
    const { data, error } = await supabase
      .from('shared_links')
      .select('*')
      .eq('group_id', groupId)
      .order('created_at', { ascending: false });

    if (error) throw error;

    return data || [];
  } catch (error) {
    console.error('Error fetching shared links:', error);
    return [];
  }
};

/**
 * Create a new shared link
 * @param linkParams The parameters for the new link
 * @returns The created shared link
 */
export const createSharedLink = async (linkParams: CreateSharedLinkParams): Promise<SharedLink | null> => {
  try {
    // Check if shared_links table exists
    const { error: tableCheckError } = await supabase
      .from('shared_links')
      .select('count', { count: 'exact', head: true });

    if (tableCheckError && tableCheckError.code === '42P01') {
      // Table doesn't exist
      console.log('Shared links table does not exist yet');
      return null;
    }

    const { data, error } = await supabase
      .from('shared_links')
      .insert(linkParams)
      .select()
      .single();

    if (error) {
      if (error.code === '42P01') {
        // Table doesn't exist
        console.log('Shared links table does not exist yet');
        return null;
      }
      throw error;
    }

    return data;
  } catch (error) {
    console.error('Error creating shared link:', error);
    return null;
  }
};

/**
 * Update a shared link
 * @param linkId The ID of the link to update
 * @param updateParams The parameters to update
 * @returns The updated shared link
 */
export const updateSharedLink = async (linkId: string, updateParams: UpdateSharedLinkParams): Promise<SharedLink | null> => {
  try {
    const { data, error } = await supabase
      .from('shared_links')
      .update(updateParams)
      .eq('id', linkId)
      .select()
      .single();

    if (error) {
      if (error.code === '42P01') {
        // Table doesn't exist
        console.log('Shared links table does not exist yet');
        return null;
      }
      throw error;
    }

    return data;
  } catch (error) {
    console.error('Error updating shared link:', error);
    return null;
  }
};

/**
 * Delete a shared link
 * @param linkId The ID of the link to delete
 * @returns True if successful, false otherwise
 */
export const deleteSharedLink = async (linkId: string): Promise<boolean> => {
  try {
    const { error } = await supabase
      .from('shared_links')
      .delete()
      .eq('id', linkId);

    if (error) {
      if (error.code === '42P01') {
        // Table doesn't exist
        console.log('Shared links table does not exist yet');
        return false;
      }
      throw error;
    }

    return true;
  } catch (error) {
    console.error('Error deleting shared link:', error);
    return false;
  }
};

/**
 * Validate a shared link
 * @param linkId The ID of the link to validate
 * @returns Validation response with link and group info if valid
 */
export const validateSharedLink = async (linkId: string): Promise<SharedLinkValidationResponse> => {
  try {
    // Use anon key for public access
    const anonSupabase = supabase;

    // Check if the shared_links table exists
    try {
      // Try to use the check_table_exists function
      try {
        const { data: tableExists, error: funcError } = await anonSupabase
          .rpc('check_table_exists', { table_name: 'shared_links' });

        if (funcError) {
          console.error('Error calling check_table_exists:', funcError);
          // Fall back to direct query
        } else if (!tableExists) {
          console.error('Shared links table does not exist (confirmed by function)');
          return {
            isValid: false,
            error: 'Sharing feature is not available',
          };
        }
      } catch (funcError) {
        console.log('check_table_exists function not available, falling back to direct query');
      }

      // Fall back to direct query if the function isn't available
      const { error: tableCheckError } = await anonSupabase
        .from('shared_links')
        .select('count', { count: 'exact', head: true });

      if (tableCheckError) {
        // Table doesn't exist
        if (tableCheckError.code === '42P01') {
          console.error('Shared links table does not exist');
          return {
            isValid: false,
            error: 'Sharing feature is not available',
          };
        } else {
          throw tableCheckError;
        }
      }
    } catch (error) {
      console.error('Error checking shared_links table:', error);
      return {
        isValid: false,
        error: 'Error validating shared link',
      };
    }

    // Fetch the shared link
    console.log('Validating shared link with ID:', linkId);
    let linkData;

    // First try with a direct SQL query to bypass RLS for diagnostic purposes
    try {
      // This is just for diagnostic purposes to see if the link exists at all
      const { data: diagnosticData, error: diagnosticError } = await anonSupabase
        .rpc('check_shared_link_exists', { link_id: linkId });

      console.log('Diagnostic check result:', { diagnosticData, diagnosticError });

      // If we get an error about the function not existing, that's expected
      // We'll continue with the normal flow
      if (diagnosticError && diagnosticError.code !== '42883') {
        console.error('Diagnostic check error:', diagnosticError);
      }
    } catch (diagnosticErr) {
      console.log('Diagnostic check exception:', diagnosticErr);
      // Continue with normal flow
    }

    // Now try the normal way
    try {
      console.log('Attempting to fetch shared link with normal query');
      const { data, error: linkError } = await anonSupabase
        .from('shared_links')
        .select('*')
        .eq('id', linkId);

      console.log('Shared link query result:', { data, linkError });

      if (linkError) {
        console.error('Error fetching shared link:', linkError);
        return {
          isValid: false,
          error: `Invalid or expired link: ${linkError.message}`,
        };
      }

      if (!data || data.length === 0) {
        console.error('No link data found for ID:', linkId);
        return {
          isValid: false,
          error: 'Invalid or expired link: No link found',
        };
      }

      // Use the first result since we're not using .single() anymore
      linkData = data[0];
      console.log('Found link data:', linkData);

      // Check if the link is active
      if (!linkData.is_active) {
        console.error('Link is not active:', linkId);
        return {
          isValid: false,
          error: 'This link has been deactivated',
        };
      }

      // Check if the link has expired
      if (linkData.expires_at && new Date(linkData.expires_at) < new Date()) {
        console.error('Link has expired:', linkId);
        return {
          isValid: false,
          error: 'This link has expired',
        };
      }
    } catch (error) {
      console.error('Error fetching shared link data:', error);
      return {
        isValid: false,
        error: 'Error validating shared link',
      };
    }

    // We already checked these in the try block above

    // Fetch the group name
    let groupData;
    try {
      console.log('Attempting to fetch group with ID:', linkData.group_id);
      const { data, error: groupError } = await anonSupabase
        .from('friend_groups')
        .select('id, name')
        .eq('id', linkData.group_id);

      console.log('Group query result:', { data, groupError });

      if (groupError) {
        console.error('Error fetching group:', groupError);
        return {
          isValid: false,
          error: `Group not found: ${groupError.message}`,
        };
      }

      if (!data || data.length === 0) {
        console.error('No group data found for ID:', linkData.group_id);
        return {
          isValid: false,
          error: 'Group not found',
        };
      }

      // Use the first result since we're not using .single() anymore
      groupData = data[0];
      console.log('Found group data:', groupData);
    } catch (error) {
      console.error('Error fetching group data:', error);
      return {
        isValid: false,
        error: 'Error validating group',
      };
    }

    // Return success with link and group info
    return {
      isValid: true,
      link: linkData,
      group: groupData,
    };
  } catch (error) {
    console.error('Error validating shared link:', error);
    return {
      isValid: false,
      error: 'Failed to validate link',
    };
  }
};

/**
 * Refresh a shared link (create a new one with the same settings)
 * @param linkId The ID of the link to refresh
 * @returns The new shared link
 */
export const refreshSharedLink = async (linkId: string): Promise<SharedLink | null> => {
  try {
    // First, get the current link data
    const { data: currentLink, error: fetchError } = await supabase
      .from('shared_links')
      .select('*')
      .eq('id', linkId)
      .single();

    if (fetchError) {
      if (fetchError.code === '42P01') {
        // Table doesn't exist
        console.log('Shared links table does not exist yet');
        return null;
      }
      throw fetchError;
    }

    if (!currentLink) {
      return null;
    }

    // Delete the old link
    const { error: deleteError } = await supabase
      .from('shared_links')
      .delete()
      .eq('id', linkId);

    if (deleteError) {
      throw deleteError;
    }

    // Create a new link with the same settings
    const newLink: CreateSharedLinkParams = {
      group_id: currentLink.group_id,
      created_by: currentLink.created_by,
      name: currentLink.name,
      access_level: currentLink.access_level,
      expires_at: currentLink.expires_at,
      is_active: currentLink.is_active,
    };

    const { data: newLinkData, error: createError } = await supabase
      .from('shared_links')
      .insert(newLink)
      .select()
      .single();

    if (createError) {
      throw createError;
    }

    return newLinkData;
  } catch (error) {
    console.error('Error refreshing shared link:', error);
    return null;
  }
};
