import { describe, it, expect, vi } from 'vitest';
import { render, screen, waitFor } from '@testing-library/react';
import { useAuth, requireAuth } from './auth';
import { renderWithRouter } from '@/test/test-utils';

describe('useAuth', () => {
  it('should handle authentication state changes', async () => {
    const TestComponent = () => {
      const { isAuthenticated, isLoading } = useAuth();
      return (
        <div>
          <span>Authenticated: {String(isAuthenticated)}</span>
          <span>Loading: {String(isLoading)}</span>
        </div>
      );
    };

    const { rerender } = render(<TestComponent />);
    
    expect(screen.getByText('Loading: true')).toBeInTheDocument();
    
    await waitFor(() => {
      expect(screen.getByText('Loading: false')).toBeInTheDocument();
    });
  });
});

describe('requireAuth', () => {
  const DummyComponent = () => <div>Protected Content</div>;
  const ProtectedComponent = requireAuth(DummyComponent);

  it('should redirect to login when not authenticated', async () => {
    const { router } = renderWithRouter(<ProtectedComponent />);
    
    await waitFor(() => {
      expect(router.state.location.pathname).toBe('/login');
    });
  });
});
