/**
 * Storage service for caching data locally
 * Provides methods for storing, retrieving, and managing cached data
 */

// Cache version - increment when schema changes
const CACHE_VERSION = 1;
const VERSION_KEY = 'cache_version';

// Cache keys
export const CACHE_KEYS = {
  PLAYERS: 'players',
  MATCHES: 'matches',
  CHEMISTRY: 'chemistry',
  USER: 'user',
  GROUP: 'group',
  LAST_SYNC: 'last_sync',
  // TAGS key is being kept for backward compatibility
  // but will be removed in a future version
  TAGS: 'tags',
};

// Cache expiration times (in milliseconds)
export const CACHE_EXPIRY = {
  SHORT: 5 * 60 * 1000, // 5 minutes
  MEDIUM: 30 * 60 * 1000, // 30 minutes
  LONG: 24 * 60 * 60 * 1000, // 1 day
  NEVER: null, // Never expires
};

// Cache item interface
interface CacheItem<T> {
  data: T;
  timestamp: number;
  expiry: number | null; // null means no expiry
  version: number;
}

/**
 * Set an item in the cache
 * @param key Cache key
 * @param data Data to cache
 * @param expiry Expiration time in milliseconds (null for no expiry)
 */
export function setCache<T>(key: string, data: T, expiry: number | null = CACHE_EXPIRY.MEDIUM): void {
  try {
    // Check if we need to initialize or update the cache version
    const storedVersion = localStorage.getItem(VERSION_KEY);
    if (!storedVersion || parseInt(storedVersion) < CACHE_VERSION) {
      // Clear all cache if version changes
      clearAllCache();
      localStorage.setItem(VERSION_KEY, CACHE_VERSION.toString());
    }

    const cacheItem: CacheItem<T> = {
      data,
      timestamp: Date.now(),
      expiry,
      version: CACHE_VERSION,
    };

    localStorage.setItem(key, JSON.stringify(cacheItem));
  } catch (error) {
    console.error('Error setting cache:', error);
    // If localStorage is full, clear old items and try again
    if (error instanceof DOMException && error.name === 'QuotaExceededError') {
      pruneCache();
      try {
        const cacheItem: CacheItem<T> = {
          data,
          timestamp: Date.now(),
          expiry,
          version: CACHE_VERSION,
        };
        localStorage.setItem(key, JSON.stringify(cacheItem));
      } catch (retryError) {
        console.error('Failed to set cache after pruning:', retryError);
      }
    }
  }
}

/**
 * Get an item from the cache
 * @param key Cache key
 * @returns Cached data or null if not found or expired
 */
export function getCache<T>(key: string): T | null {
  try {
    const item = localStorage.getItem(key);
    if (!item) return null;

    const cacheItem: CacheItem<T> = JSON.parse(item);

    // Check version
    if (cacheItem.version !== CACHE_VERSION) {
      removeCache(key);
      return null;
    }

    // Check expiry
    if (cacheItem.expiry !== null) {
      const now = Date.now();
      if (now - cacheItem.timestamp > cacheItem.expiry) {
        removeCache(key);
        return null;
      }
    }

    return cacheItem.data;
  } catch (error) {
    console.error('Error getting cache:', error);
    return null;
  }
}

/**
 * Remove an item from the cache
 * @param key Cache key
 */
export function removeCache(key: string): void {
  try {
    localStorage.removeItem(key);
  } catch (error) {
    console.error('Error removing cache:', error);
  }
}

/**
 * Clear all cached data
 */
export function clearAllCache(): void {
  try {
    Object.values(CACHE_KEYS).forEach(key => {
      localStorage.removeItem(key);
    });
  } catch (error) {
    console.error('Error clearing cache:', error);
  }
}

/**
 * Prune old cache items to free up space
 */
function pruneCache(): void {
  try {
    // Get all keys
    const keys = Object.values(CACHE_KEYS);

    // Sort cache items by age (oldest first)
    const cacheItems = keys
      .map(key => {
        const item = localStorage.getItem(key);
        if (!item) return { key, timestamp: 0 };

        try {
          const parsed = JSON.parse(item);
          return { key, timestamp: parsed.timestamp || 0 };
        } catch {
          return { key, timestamp: 0 };
        }
      })
      .sort((a, b) => a.timestamp - b.timestamp);

    // Remove oldest 50% of items
    const itemsToRemove = Math.ceil(cacheItems.length / 2);
    cacheItems.slice(0, itemsToRemove).forEach(item => {
      localStorage.removeItem(item.key);
    });
  } catch (error) {
    console.error('Error pruning cache:', error);
  }
}

/**
 * Get the timestamp of the last cache update
 * @param key Cache key
 * @returns Timestamp or null if not found
 */
export function getCacheTimestamp(key: string): number | null {
  try {
    const item = localStorage.getItem(key);
    if (!item) return null;

    const cacheItem = JSON.parse(item);
    return cacheItem.timestamp || null;
  } catch {
    return null;
  }
}

/**
 * Check if the cache is valid (not expired)
 * @param key Cache key
 * @returns True if valid, false otherwise
 */
export function isCacheValid(key: string): boolean {
  return getCache(key) !== null;
}

/**
 * Update a specific field in a cached object
 * @param key Cache key
 * @param field Field to update
 * @param value New value
 */
export function updateCacheField<T, K extends keyof T>(
  key: string,
  field: K,
  value: T[K]
): void {
  const cached = getCache<T>(key);
  if (cached) {
    const updated = { ...cached, [field]: value };
    setCache(key, updated);
  }
}

/**
 * Get the size of the cache in bytes
 * @returns Size in bytes
 */
export function getCacheSize(): number {
  try {
    let size = 0;
    for (const key in localStorage) {
      if (localStorage.hasOwnProperty(key)) {
        size += localStorage.getItem(key)?.length || 0;
      }
    }
    return size;
  } catch {
    return 0;
  }
}
