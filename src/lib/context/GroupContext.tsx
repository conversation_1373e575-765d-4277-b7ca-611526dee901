import React, { createContext, useContext, useState, useEffect, ReactNode, useRef, useCallback } from 'react';
import { supabase } from '@/lib/supabase';
import { fetchUserGroups, Group, checkUserPermission, GroupRole } from '@/lib/api/groups';
import { useLocation } from 'react-router-dom';

interface GroupContextProps {
  groups: Group[];
  currentGroup: Group | null;
  setCurrentGroup: (group: Group | null) => void;
  fetchGroups: () => Promise<void>;
  userRole: GroupRole | null;
  checkPermission: (requiredRole: GroupRole) => Promise<boolean>;
  isLoading: boolean;
  hasLoadedGroups: boolean;
}

const GroupContext = createContext<GroupContextProps | undefined>(undefined);

// Default group definition
const DEFAULT_GROUP: Group = {
  id: '00000000-0000-0000-0000-000000000000',
  name: 'Default Group',
  created_by: 'system'
};

export const GroupProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  const [groups, setGroups] = useState<Group[]>([]);
  const [currentGroup, setCurrentGroup] = useState<Group | null>(null);
  const [userRole, setUserRole] = useState<GroupRole | null>(null);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [hasLoadedGroups, setHasLoadedGroups] = useState<boolean>(false);
  const isInitialized = useRef(false);
  const isMounted = useRef(true);
  const location = useLocation();

  // Check if we're in a shared route
  const isSharedRoute = location.pathname.startsWith('/shared/') || location.pathname.startsWith('/s/');

  // Get the current user ID from Supabase auth
  const getCurrentUserId = async (): Promise<string | null> => {
    const { data } = await supabase.auth.getSession();
    return data?.session?.user?.id || null;
  };

  // Check if the current user has a specific permission in the current group
  const checkPermission = async (requiredRole: GroupRole): Promise<boolean> => {
    if (!currentGroup) return false;

    // If we're in a shared route, permissions are limited
    if (isSharedRoute) {
      // For shared routes, only allow read access
      return requiredRole === 'Guest';
    }

    const userId = await getCurrentUserId();
    if (!userId) return false;

    return checkUserPermission(currentGroup.id, userId, requiredRole);
  };

  // Update user role when current group changes
  useEffect(() => {
    const updateUserRole = async () => {
      if (!currentGroup) {
        setUserRole(null);
        return;
      }

      // If we're in a shared route, set role to Guest
      if (isSharedRoute) {
        console.log('In shared route, setting role to Guest');
        setUserRole('Guest');
        return;
      }

      const userId = await getCurrentUserId();
      if (!userId) {
        setUserRole(null);
        return;
      }

      // If user is the creator, they're automatically an Admin
      if (currentGroup.created_by === userId) {
        setUserRole('Admin');
        return;
      }

      // Check the user's role in the group
      try {
        const { data } = await supabase
          .from('group_members')
          .select('role')
          .eq('group_id', currentGroup.id)
          .eq('user_id', userId)
          .maybeSingle();

        setUserRole(data?.role as GroupRole || null);
      } catch (err) {
        console.error('Error fetching user role:', err);
        setUserRole(null);
      }
    };

    updateUserRole();
  }, [currentGroup, isSharedRoute]);

  // Memoize the fetchGroups function to prevent it from changing on every render
  const fetchGroups = useCallback(async () => {
    if (!isMounted.current) return Promise.resolve();

    console.log('Fetching groups...');
    setIsLoading(true);
    setHasLoadedGroups(false);

    try {
      // If we're in a shared route, we don't need to fetch groups
      // Just use the default group
      if (isSharedRoute) {
        console.log('In shared route, using default group');
        setGroups([DEFAULT_GROUP]);
        setCurrentGroup(DEFAULT_GROUP);
        setHasLoadedGroups(true);
        setIsLoading(false);
        return;
      }

      // Get current user ID
      const userId = await getCurrentUserId();
      if (!userId) {
        throw new Error('User not authenticated');
      }

      // Use the enhanced API function to fetch groups
      const data = await fetchUserGroups(userId);

      if (data && data.length > 0) {
        // Success - use data from API
        console.log('Groups fetched:', data.length);
        localStorage.setItem('cachedGroups', JSON.stringify(data));
        setGroups(data);

        // Set current group if needed
        if (!currentGroup) {
          const selectedId = localStorage.getItem('selectedGroupId');
          const selectedGroup = selectedId ? data.find(g => g.id === selectedId) : null;

          if (selectedGroup) {
            setCurrentGroup(selectedGroup);
          } else {
            setCurrentGroup(data[0]);
            localStorage.setItem('selectedGroupId', data[0].id);
          }
        }
        setIsLoading(false);
        setHasLoadedGroups(true);
        return;
      }

      // If API failed, try cached data
      const cachedData = localStorage.getItem('cachedGroups');
      if (cachedData) {
        const parsedData = JSON.parse(cachedData) as Group[];
        if (parsedData.length > 0) {
          console.log('Using cached groups:', parsedData.length);
          setGroups(parsedData);

          // Set current group if needed
          if (!currentGroup) {
            const selectedId = localStorage.getItem('selectedGroupId');
            const selectedGroup = selectedId ? parsedData.find(g => g.id === selectedId) : null;

            if (selectedGroup) {
              setCurrentGroup(selectedGroup);
            } else {
              setCurrentGroup(parsedData[0]);
              localStorage.setItem('selectedGroupId', parsedData[0].id);
            }
          }
          setIsLoading(false);
          setHasLoadedGroups(true);
          return;
        }
      }

      // Last resort - use default group
      console.log('Using default group');
      setGroups([DEFAULT_GROUP]);
      setCurrentGroup(DEFAULT_GROUP);
      localStorage.setItem('selectedGroupId', DEFAULT_GROUP.id);
      setHasLoadedGroups(true);

    } catch (err) {
      console.error('Error in fetchGroups:', err);

      // Fallback to default group
      setGroups([DEFAULT_GROUP]);
      setCurrentGroup(DEFAULT_GROUP);
      localStorage.setItem('selectedGroupId', DEFAULT_GROUP.id);
      setHasLoadedGroups(true);
    } finally {
      setIsLoading(false);
    }
  }, [isSharedRoute]); // Include isSharedRoute to update when route changes

  // Initialize on mount and when shared route status changes
  useEffect(() => {
    // Reset initialization flag when shared route status changes
    if (isSharedRoute) {
      isInitialized.current = false;
    }

    if (isInitialized.current) return;

    console.log('Initializing GroupContext, isSharedRoute:', isSharedRoute);
    isInitialized.current = true;
    fetchGroups().catch(err => {
      console.error('Error initializing groups:', err);
    });

    return () => {
      isMounted.current = false;
    };
  }, [fetchGroups, isSharedRoute]); // Re-initialize when shared route status changes

  // Create a stable context value object
  const contextValue = React.useMemo(() => ({
    groups,
    currentGroup,
    setCurrentGroup,
    fetchGroups,
    userRole,
    checkPermission,
    isLoading,
    hasLoadedGroups
  }), [groups, currentGroup, fetchGroups, userRole, isLoading, hasLoadedGroups]);

  return (
    <GroupContext.Provider value={contextValue}>
      {children}
    </GroupContext.Provider>
  );
};

export const useGroup = (): GroupContextProps => {
  const context = useContext(GroupContext);
  if (!context) {
    throw new Error('useGroup must be used within a GroupProvider');
  }
  return context;
};

// For backward compatibility
export const useGroupContext = useGroup;