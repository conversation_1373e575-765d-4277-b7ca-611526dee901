export interface Database {
  public: {
    Tables: {
      players: {
        Row: {
          id: number
          created_at: string
          name: string
          skills: number
          effort: number
          stamina: number
          group_id: string | null
        }
        Insert: {
          name: string
          skills: number
          effort: number
          stamina: number
          group_id: string
        }
        Update: {
          name?: string
          skills?: number
          effort?: number
          stamina?: number
          group_id?: string
        }
      }
      matches: {
        Row: {
          id: number
          match_date: string
          teama: number[]
          teamb: number[]
          scorea: number | null
          scoreb: number | null
          winner: 'A' | 'B' | 'Draw' | null
          goalscorers: Array<{ team: 'A' | 'B', playerId: number }>
          youtubelink: string | null
          group_id: string | null
        }
        Insert: {
          match_date: string
          teama: number[]
          teamb: number[]
          scorea?: number | null
          scoreb?: number | null
          winner?: 'A' | 'B' | 'Draw' | null
          goalscorers?: Array<{ team: 'A' | 'B', playerId: number }>
          youtubelink?: string | null
          group_id: string
        }
        Update: {
          match_date?: string
          teama?: number[]
          teamb?: number[]
          scorea?: number | null
          scoreb?: number | null
          winner?: 'A' | 'B' | 'Draw' | null
          goalscorers?: Array<{ team: 'A' | 'B', playerId: number }>
          youtubelink?: string | null
          group_id?: string
        }
      }
      chemistry: {
        Row: {
          id: number
          player1_id: number
          player2_id: number
          games_together: number
          wins_together: number
          group_id: string | null
        }
        Insert: {
          player1_id: number
          player2_id: number
          games_together?: number
          wins_together?: number
          group_id: string
        }
        Update: {
          player1_id?: number
          player2_id?: number
          games_together?: number
          wins_together?: number
          group_id?: string
        }
      }
      friend_groups: {
        Row: {
          id: string
          name: string
          created_by: string
          created_at: string
        }
        Insert: {
          name: string
          created_by: string
          id?: string
          created_at?: string
        }
        Update: {
          name?: string
          created_by?: string
          id?: string
          created_at?: string
        }
      }
      group_members: {
        Row: {
          id: string
          group_id: string
          user_id: string
          role: string
          created_at: string
        }
        Insert: {
          group_id: string
          user_id: string
          role: string
          id?: string
          created_at?: string
        }
        Update: {
          group_id?: string
          user_id?: string
          role?: string
          id?: string
          created_at?: string
        }
      }
      match_comments: {
        Row: {
          id: string
          match_id: number
          user_id: string
          content: string
          created_at: string
          group_id: string | null
        }
        Insert: {
          match_id: number
          user_id: string
          content: string
          group_id: string
          id?: string
          created_at?: string
        }
        Update: {
          match_id?: number
          user_id?: string
          content?: string
          group_id?: string
          id?: string
          created_at?: string
        }
      }
    }
  }
}
