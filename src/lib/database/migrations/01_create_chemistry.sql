create table if not exists public.chemistry (
  id bigint generated by default as identity primary key,
  player1_id bigint references public.players(id) not null,
  player2_id bigint references public.players(id) not null,
  games_together integer default 0,
  wins_together integer default 0,
  created_at timestamp with time zone default timezone('utc'::text, now()) not null,
  unique(player1_id, player2_id)
);

-- Create indexes for better performance
create index if not exists chemistry_player1_id_idx on public.chemistry(player1_id);
create index if not exists chemistry_player2_id_idx on public.chemistry(player2_id);
