import { addDays, subDays, format } from 'date-fns';

// Types for demo data
export interface DemoPlayer {
  id: number;
  name: string;
  skills: number;
  effort: number;
  stamina: number;
  group_id: string;
  avatar_url?: string;
}

export interface DemoGoalscorer {
  team: 'A' | 'B';
  playerId: number;
}

export interface DemoMatch {
  id: number;
  created_at: string;
  match_date: string | Date;
  teama: number[];
  teamb: number[];
  scorea: number | null;
  scoreb: number | null;
  winner?: 'A' | 'B' | 'Draw';
  goalscorers?: DemoGoalscorer[];
  youtubelink?: string;
  group_id: string;
}

export interface DemoGroup {
  id: string;
  name: string;
  created_by: string;
  created_at: string;
}

// Demo group data
export const DEMO_GROUP: DemoGroup = {
  id: 'demo-group-id',
  name: 'Fulbito Stats Demo',
  created_by: 'demo-user',
  created_at: new Date().toISOString(),
};

// Sample player names with varied backgrounds
const PLAYER_NAMES = [
  '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>',
  '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON><PERSON>',
  '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>',
  '<PERSON>', '<PERSON>', 'Cristian <PERSON>'
];

// Generate realistic player stats
const generatePlayerStats = () => {
  // Create varied skill distributions
  const skillTypes = ['balanced', 'technical', 'physical', 'energetic'];
  const skillType = skillTypes[Math.floor(Math.random() * skillTypes.length)];
  
  let skills, effort, stamina;
  
  switch (skillType) {
    case 'technical':
      skills = 7 + Math.floor(Math.random() * 3); // 7-9
      effort = 5 + Math.floor(Math.random() * 4); // 5-8
      stamina = 4 + Math.floor(Math.random() * 4); // 4-7
      break;
    case 'physical':
      skills = 4 + Math.floor(Math.random() * 4); // 4-7
      effort = 7 + Math.floor(Math.random() * 3); // 7-9
      stamina = 6 + Math.floor(Math.random() * 4); // 6-9
      break;
    case 'energetic':
      skills = 5 + Math.floor(Math.random() * 3); // 5-7
      effort = 6 + Math.floor(Math.random() * 3); // 6-8
      stamina = 7 + Math.floor(Math.random() * 3); // 7-9
      break;
    default: // balanced
      skills = 5 + Math.floor(Math.random() * 4); // 5-8
      effort = 5 + Math.floor(Math.random() * 4); // 5-8
      stamina = 5 + Math.floor(Math.random() * 4); // 5-8
      break;
  }
  
  return { skills, effort, stamina };
};

// Generate demo players
export const generateDemoPlayers = (): DemoPlayer[] => {
  return PLAYER_NAMES.map((name, index) => ({
    id: index + 1,
    name,
    ...generatePlayerStats(),
    group_id: DEMO_GROUP.id,
  }));
};

// Generate realistic match results
const generateMatchResult = (teamA: number[], teamB: number[], players: DemoPlayer[]) => {
  // Calculate team strengths
  const getTeamStrength = (team: number[]) => {
    return team.reduce((total, playerId) => {
      const player = players.find(p => p.id === playerId);
      if (!player) return total;
      return total + (player.skills + player.effort + player.stamina) / 3;
    }, 0) / team.length;
  };
  
  const teamAStrength = getTeamStrength(teamA);
  const teamBStrength = getTeamStrength(teamB);
  
  // Add some randomness to make matches more realistic
  const randomFactor = 0.3;
  const adjustedTeamA = teamAStrength + (Math.random() - 0.5) * randomFactor * 10;
  const adjustedTeamB = teamBStrength + (Math.random() - 0.5) * randomFactor * 10;
  
  // Determine winner and scores
  let scoreA, scoreB, winner;
  
  if (Math.abs(adjustedTeamA - adjustedTeamB) < 0.5) {
    // Close match - could be a draw
    if (Math.random() < 0.2) {
      // Draw
      const drawScore = 1 + Math.floor(Math.random() * 3); // 1-3
      scoreA = scoreB = drawScore;
      winner = 'Draw';
    } else {
      // Close win
      scoreA = 1 + Math.floor(Math.random() * 3);
      scoreB = scoreA + (Math.random() < 0.5 ? -1 : 1);
      if (scoreB < 0) scoreB = 0;
      winner = scoreA > scoreB ? 'A' : 'B';
    }
  } else {
    // Clear winner
    const strongerTeam = adjustedTeamA > adjustedTeamB ? 'A' : 'B';
    if (strongerTeam === 'A') {
      scoreA = 2 + Math.floor(Math.random() * 4); // 2-5
      scoreB = Math.floor(Math.random() * scoreA); // 0 to scoreA-1
      winner = 'A';
    } else {
      scoreB = 2 + Math.floor(Math.random() * 4); // 2-5
      scoreA = Math.floor(Math.random() * scoreB); // 0 to scoreB-1
      winner = 'B';
    }
  }
  
  // Generate goalscorers
  const goalscorers: DemoGoalscorer[] = [];
  
  // Add goalscorers for team A
  for (let i = 0; i < scoreA; i++) {
    const randomPlayer = teamA[Math.floor(Math.random() * teamA.length)];
    goalscorers.push({ team: 'A', playerId: randomPlayer });
  }
  
  // Add goalscorers for team B
  for (let i = 0; i < scoreB; i++) {
    const randomPlayer = teamB[Math.floor(Math.random() * teamB.length)];
    goalscorers.push({ team: 'B', playerId: randomPlayer });
  }
  
  return { scoreA, scoreB, winner, goalscorers };
};

// Generate demo matches
export const generateDemoMatches = (players: DemoPlayer[]): DemoMatch[] => {
  const matches: DemoMatch[] = [];
  const today = new Date();
  
  // Generate 30 matches over the past 6 months
  for (let i = 0; i < 30; i++) {
    // Random date in the past 6 months
    const daysAgo = Math.floor(Math.random() * 180);
    const matchDate = subDays(today, daysAgo);
    
    // Randomly select players for teams (5v5 to 7v7)
    const playersPerTeam = 5 + Math.floor(Math.random() * 3); // 5-7 players per team
    const totalPlayers = playersPerTeam * 2;
    
    // Shuffle players and select
    const shuffledPlayers = [...players].sort(() => Math.random() - 0.5);
    const selectedPlayers = shuffledPlayers.slice(0, Math.min(totalPlayers, players.length));
    
    const teamA = selectedPlayers.slice(0, playersPerTeam).map(p => p.id);
    const teamB = selectedPlayers.slice(playersPerTeam, playersPerTeam * 2).map(p => p.id);
    
    // Generate match result
    const result = generateMatchResult(teamA, teamB, players);
    
    matches.push({
      id: i + 1,
      created_at: matchDate.toISOString(),
      match_date: matchDate,
      teama: teamA,
      teamb: teamB,
      scorea: result.scoreA,
      scoreb: result.scoreB,
      winner: result.winner as 'A' | 'B' | 'Draw',
      goalscorers: result.goalscorers,
      group_id: DEMO_GROUP.id,
    });
  }
  
  // Sort matches by date (most recent first)
  return matches.sort((a, b) => new Date(b.match_date).getTime() - new Date(a.match_date).getTime());
};

// Generate all demo data
export const generateDemoData = () => {
  const players = generateDemoPlayers();
  const matches = generateDemoMatches(players);
  
  return {
    group: DEMO_GROUP,
    players,
    matches,
  };
};

// Export demo data instance
export const DEMO_DATA = generateDemoData();
