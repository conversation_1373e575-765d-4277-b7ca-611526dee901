import { useState, useEffect } from 'react';
import { supabase } from '../supabase';
import { useToast } from '@/hooks/use-toast';

interface QueryOptions {
  select?: string;
  orderBy?: {
    column: string;
    ascending?: boolean;
  };
}

interface QueryResult<T> {
  data: T[];
  loading: boolean;
  error: string | null;
  setData: (data: T[]) => void;
}

function useSupabaseQuery<T>(
  tableName: string,
  options?: QueryOptions
): QueryResult<T> {
  const [data, setData] = useState<T[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const { toast } = useToast();

  useEffect(() => {
    const fetchData = async () => {
      setLoading(true);
      try {
        let query = supabase.from(tableName).select(options?.select || '*');
        
        if (options?.orderBy) {
          query = query.order(
            options.orderBy.column === 'date' ? 'match_date' : options.orderBy.column, // Use 'match_date'
            { ascending: options.orderBy.ascending }
          );
        }

        const { data: result, error: queryError } = await query;

        if (queryError) throw queryError;
        setData((result as T[]) || []);
        
      } catch (err: any) {
        console.error(`Error fetching ${tableName}:`, err);
        setError(err.message);
        toast({
          title: `Error fetching ${tableName}`,
          description: err.message,
          variant: "destructive"
        });
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [tableName, options?.select, options?.orderBy?.column, options?.orderBy?.ascending, toast]);

  return { data, loading, error, setData };
};

export { useSupabaseQuery };
export default useSupabaseQuery;
