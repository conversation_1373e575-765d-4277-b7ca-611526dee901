import { APIError } from '../api';

const WINDOW_SIZE = 60000; // 1 minute
const requests: number[] = [];
const limit = parseInt(import.meta.env.VITE_API_RATE_LIMIT || '100', 10);

export async function rateLimit(): Promise<void> {
  const now = Date.now();
  requests.push(now);
  
  // Remove requests outside the window
  while (requests.length > 0 && requests[0] < now - WINDOW_SIZE) {
    requests.shift();
  }

  if (requests.length > limit) {
    throw new APIError(
      'Rate limit exceeded. Please try again later.',
      'RATE_LIMIT_EXCEEDED',
      429
    );
  }
}
