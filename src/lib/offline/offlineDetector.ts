/**
 * Offline detection service
 * Provides methods for detecting and monitoring network connectivity
 */

// Event names
const ONLINE_EVENT = 'online';
const OFFLINE_EVENT = 'offline';

// Offline status storage key
const OFFLINE_STATUS_KEY = 'is_offline';

// Callback types
type ConnectivityCallback = (isOnline: boolean) => void;

// Store for callbacks
const callbacks: ConnectivityCallback[] = [];

/**
 * Initialize the offline detector
 * Sets up event listeners for online/offline events
 */
export function initOfflineDetector(): void {
  // Set initial state
  updateOfflineStatus();
  
  // Add event listeners
  window.addEventListener(ONLINE_EVENT, handleOnline);
  window.addEventListener(OFFLINE_EVENT, handleOffline);
  
  // Additional check with fetch to handle cases where the browser
  // doesn't correctly detect network status
  checkConnectivityWithFetch();
}

/**
 * Clean up the offline detector
 * Removes event listeners
 */
export function cleanupOfflineDetector(): void {
  window.removeEventListener(ONLINE_EVENT, handleOnline);
  window.removeEventListener(OFFLINE_EVENT, handleOffline);
}

/**
 * Check if the application is currently offline
 * @returns True if offline, false otherwise
 */
export function isOffline(): boolean {
  // First check localStorage (for persistence across page loads)
  const storedStatus = localStorage.getItem(OFFLINE_STATUS_KEY);
  if (storedStatus !== null) {
    return storedStatus === 'true';
  }
  
  // Then check navigator.onLine
  return !navigator.onLine;
}

/**
 * Subscribe to connectivity changes
 * @param callback Function to call when connectivity changes
 * @returns Unsubscribe function
 */
export function subscribeToConnectivityChanges(callback: ConnectivityCallback): () => void {
  callbacks.push(callback);
  
  // Immediately call with current status
  callback(!isOffline());
  
  // Return unsubscribe function
  return () => {
    const index = callbacks.indexOf(callback);
    if (index !== -1) {
      callbacks.splice(index, 1);
    }
  };
}

/**
 * Handle online event
 */
function handleOnline(): void {
  updateOfflineStatus(false);
  notifyCallbacks(true);
}

/**
 * Handle offline event
 */
function handleOffline(): void {
  updateOfflineStatus(true);
  notifyCallbacks(false);
}

/**
 * Update the offline status in localStorage
 * @param offline Optional offline status to set
 */
function updateOfflineStatus(offline?: boolean): void {
  const isCurrentlyOffline = offline !== undefined ? offline : !navigator.onLine;
  localStorage.setItem(OFFLINE_STATUS_KEY, isCurrentlyOffline.toString());
}

/**
 * Notify all callbacks of a connectivity change
 * @param isOnline True if online, false if offline
 */
function notifyCallbacks(isOnline: boolean): void {
  callbacks.forEach(callback => {
    try {
      callback(isOnline);
    } catch (error) {
      console.error('Error in connectivity callback:', error);
    }
  });
}

/**
 * Check connectivity by making a fetch request
 * More reliable than just using navigator.onLine
 */
function checkConnectivityWithFetch(): void {
  // Only run this check periodically
  setInterval(async () => {
    try {
      // Try to fetch a small resource
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 5000);
      
      const response = await fetch('/favicon.ico', {
        method: 'HEAD',
        cache: 'no-store',
        signal: controller.signal,
      });
      
      clearTimeout(timeoutId);
      
      if (response.ok && isOffline()) {
        // We're actually online but marked as offline
        updateOfflineStatus(false);
        notifyCallbacks(true);
      }
    } catch (error) {
      // If fetch fails, we're offline
      if (!isOffline()) {
        updateOfflineStatus(true);
        notifyCallbacks(false);
      }
    }
  }, 30000); // Check every 30 seconds
}
