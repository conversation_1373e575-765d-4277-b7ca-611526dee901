/**
 * Sync queue system for offline operations
 * Stores operations to be performed when back online
 */

import { v4 as uuidv4 } from 'uuid';
import { isOffline, subscribeToConnectivityChanges } from './offlineDetector';

// Queue storage key
const SYNC_QUEUE_KEY = 'sync_queue';

// Operation types
export enum OperationType {
  CREATE = 'create',
  UPDATE = 'update',
  DELETE = 'delete',
}

// Entity types
export enum EntityType {
  PLAYER = 'player',
  MATCH = 'match',
  CHEMISTRY = 'chemistry',
  COMMENT = 'comment',
}

// Operation interface
export interface SyncOperation {
  id: string;
  type: OperationType;
  entity: EntityType;
  data: any;
  timestamp: number;
  retryCount: number;
  groupId?: string;
}

// Sync status
export interface SyncStatus {
  pending: number;
  syncing: boolean;
  lastSync: number | null;
}

// Callback types
type SyncCallback = (status: SyncStatus) => void;
type SyncErrorCallback = (operation: SyncOperation, error: any) => void;
type SyncSuccessCallback = (operation: SyncOperation) => void;

// Store for callbacks
const statusCallbacks: SyncCallback[] = [];
const errorCallbacks: SyncErrorCallback[] = [];
const successCallbacks: SyncSuccessCallback[] = [];

// Sync status
let syncStatus: SyncStatus = {
  pending: 0,
  syncing: false,
  lastSync: null,
};

// Sync interval ID
let syncIntervalId: number | null = null;

/**
 * Initialize the sync queue
 * Sets up connectivity listener and periodic sync attempts
 */
export function initSyncQueue(): void {
  // Load queue from storage
  loadQueue();
  
  // Update sync status
  updateSyncStatus();
  
  // Subscribe to connectivity changes
  subscribeToConnectivityChanges(handleConnectivityChange);
  
  // Set up periodic sync attempts
  syncIntervalId = window.setInterval(attemptSync, 60000); // Try every minute
}

/**
 * Clean up the sync queue
 */
export function cleanupSyncQueue(): void {
  if (syncIntervalId !== null) {
    clearInterval(syncIntervalId);
    syncIntervalId = null;
  }
}

/**
 * Add an operation to the sync queue
 * @param type Operation type
 * @param entity Entity type
 * @param data Operation data
 * @param groupId Optional group ID
 * @returns Operation ID
 */
export function addToSyncQueue(
  type: OperationType,
  entity: EntityType,
  data: any,
  groupId?: string
): string {
  const queue = getQueue();
  
  const operation: SyncOperation = {
    id: uuidv4(),
    type,
    entity,
    data,
    timestamp: Date.now(),
    retryCount: 0,
    groupId,
  };
  
  queue.push(operation);
  saveQueue(queue);
  
  // Update sync status
  updateSyncStatus();
  
  // Try to sync immediately if online
  if (!isOffline()) {
    attemptSync();
  }
  
  return operation.id;
}

/**
 * Get the current sync queue
 * @returns Array of sync operations
 */
export function getQueue(): SyncOperation[] {
  try {
    const queueJson = localStorage.getItem(SYNC_QUEUE_KEY);
    return queueJson ? JSON.parse(queueJson) : [];
  } catch (error) {
    console.error('Error loading sync queue:', error);
    return [];
  }
}

/**
 * Save the sync queue to storage
 * @param queue Sync queue
 */
function saveQueue(queue: SyncOperation[]): void {
  try {
    localStorage.setItem(SYNC_QUEUE_KEY, JSON.stringify(queue));
  } catch (error) {
    console.error('Error saving sync queue:', error);
  }
}

/**
 * Load the sync queue from storage
 */
function loadQueue(): void {
  getQueue();
}

/**
 * Clear the sync queue
 */
export function clearQueue(): void {
  saveQueue([]);
  updateSyncStatus();
}

/**
 * Get the current sync status
 * @returns Sync status
 */
export function getSyncStatus(): SyncStatus {
  return { ...syncStatus };
}

/**
 * Update the sync status
 */
function updateSyncStatus(): void {
  const queue = getQueue();
  
  syncStatus = {
    ...syncStatus,
    pending: queue.length,
  };
  
  // Notify callbacks
  notifyStatusCallbacks();
}

/**
 * Subscribe to sync status changes
 * @param callback Function to call when sync status changes
 * @returns Unsubscribe function
 */
export function subscribeToSyncStatus(callback: SyncCallback): () => void {
  statusCallbacks.push(callback);
  
  // Immediately call with current status
  callback(getSyncStatus());
  
  // Return unsubscribe function
  return () => {
    const index = statusCallbacks.indexOf(callback);
    if (index !== -1) {
      statusCallbacks.splice(index, 1);
    }
  };
}

/**
 * Subscribe to sync errors
 * @param callback Function to call when a sync operation fails
 * @returns Unsubscribe function
 */
export function subscribeToSyncErrors(callback: SyncErrorCallback): () => void {
  errorCallbacks.push(callback);
  
  // Return unsubscribe function
  return () => {
    const index = errorCallbacks.indexOf(callback);
    if (index !== -1) {
      errorCallbacks.splice(index, 1);
    }
  };
}

/**
 * Subscribe to sync successes
 * @param callback Function to call when a sync operation succeeds
 * @returns Unsubscribe function
 */
export function subscribeToSyncSuccess(callback: SyncSuccessCallback): () => void {
  successCallbacks.push(callback);
  
  // Return unsubscribe function
  return () => {
    const index = successCallbacks.indexOf(callback);
    if (index !== -1) {
      successCallbacks.splice(index, 1);
    }
  };
}

/**
 * Notify status callbacks of a change
 */
function notifyStatusCallbacks(): void {
  statusCallbacks.forEach(callback => {
    try {
      callback(getSyncStatus());
    } catch (error) {
      console.error('Error in sync status callback:', error);
    }
  });
}

/**
 * Notify error callbacks of a sync error
 * @param operation Failed operation
 * @param error Error
 */
function notifyErrorCallbacks(operation: SyncOperation, error: any): void {
  errorCallbacks.forEach(callback => {
    try {
      callback(operation, error);
    } catch (callbackError) {
      console.error('Error in sync error callback:', callbackError);
    }
  });
}

/**
 * Notify success callbacks of a successful sync
 * @param operation Successful operation
 */
function notifySuccessCallbacks(operation: SyncOperation): void {
  successCallbacks.forEach(callback => {
    try {
      callback(operation);
    } catch (error) {
      console.error('Error in sync success callback:', error);
    }
  });
}

/**
 * Handle connectivity change
 * @param isOnline True if online, false if offline
 */
function handleConnectivityChange(isOnline: boolean): void {
  if (isOnline) {
    // Try to sync when we come back online
    attemptSync();
  }
}

/**
 * Attempt to sync the queue
 */
export async function attemptSync(): Promise<void> {
  // Don't sync if offline or already syncing
  if (isOffline() || syncStatus.syncing) {
    return;
  }
  
  const queue = getQueue();
  if (queue.length === 0) {
    return;
  }
  
  // Update status
  syncStatus.syncing = true;
  notifyStatusCallbacks();
  
  try {
    // Process queue in order (oldest first)
    const sortedQueue = [...queue].sort((a, b) => a.timestamp - b.timestamp);
    
    for (const operation of sortedQueue) {
      try {
        // Process operation
        await processOperation(operation);
        
        // Remove from queue on success
        removeFromQueue(operation.id);
        
        // Notify success
        notifySuccessCallbacks(operation);
      } catch (error) {
        // Increment retry count
        incrementRetryCount(operation.id);
        
        // Notify error
        notifyErrorCallbacks(operation, error);
        
        // Stop processing if we go offline
        if (isOffline()) {
          break;
        }
      }
    }
  } finally {
    // Update status
    syncStatus.syncing = false;
    syncStatus.lastSync = Date.now();
    updateSyncStatus();
  }
}

/**
 * Process a sync operation
 * This is a placeholder - the actual implementation will be in the API service
 * @param operation Operation to process
 */
async function processOperation(operation: SyncOperation): Promise<void> {
  // This will be implemented in the API service
  // For now, just log the operation
  console.log('Processing operation:', operation);
  
  // Simulate API call
  await new Promise(resolve => setTimeout(resolve, 500));
  
  // Simulate random failure
  if (Math.random() < 0.2) {
    throw new Error('Simulated sync failure');
  }
}

/**
 * Remove an operation from the queue
 * @param id Operation ID
 */
function removeFromQueue(id: string): void {
  const queue = getQueue();
  const newQueue = queue.filter(op => op.id !== id);
  saveQueue(newQueue);
  updateSyncStatus();
}

/**
 * Increment the retry count for an operation
 * @param id Operation ID
 */
function incrementRetryCount(id: string): void {
  const queue = getQueue();
  const newQueue = queue.map(op => {
    if (op.id === id) {
      return { ...op, retryCount: op.retryCount + 1 };
    }
    return op;
  });
  saveQueue(newQueue);
}
