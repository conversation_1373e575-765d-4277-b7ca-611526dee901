import { useToast } from "@/hooks/use-toast";
import { useEffect, useState } from "react";

// Google Form URL
export const FEEDBACK_FORM_URL = "https://docs.google.com/forms/d/e/1FAIpQLSdtZuMIn1UVQIqCdjC7cJOk7DYoqIMeYv-HtK9o-vPZ6UIrAg/viewform?usp=sharing";

// Constants for notification timing
const NOTIFICATION_INTERVAL = 7 * 24 * 60 * 60 * 1000; // 7 days in milliseconds
const INITIAL_DELAY = 3 * 24 * 60 * 60 * 1000; // 3 days in milliseconds

// Local storage keys
const LAST_NOTIFICATION_KEY = 'feedback_last_notification';
const FEEDBACK_COMPLETED_KEY = 'feedback_completed';

/**
 * Opens the feedback form in a new tab
 */
export const openFeedbackForm = () => {
  window.open(FEEDBACK_FORM_URL, '_blank');
};

/**
 * Hook to manage feedback notifications
 */
export const useFeedbackNotification = () => {
  const { toast } = useToast();
  const [initialized, setInitialized] = useState(false);

  // Check if it's time to show a notification
  const shouldShowNotification = () => {
    // Don't show if user has completed feedback
    const feedbackCompleted = localStorage.getItem(FEEDBACK_COMPLETED_KEY) === 'true';
    if (feedbackCompleted) return false;

    // Check when the last notification was shown
    const lastNotification = localStorage.getItem(LAST_NOTIFICATION_KEY);
    if (!lastNotification) {
      // First time user - set initial timestamp but don't show yet
      const initialTimestamp = Date.now() - INITIAL_DELAY + (Math.random() * INITIAL_DELAY / 2);
      localStorage.setItem(LAST_NOTIFICATION_KEY, initialTimestamp.toString());
      return false;
    }

    // Check if enough time has passed since the last notification
    const timeSinceLastNotification = Date.now() - parseInt(lastNotification);
    return timeSinceLastNotification >= NOTIFICATION_INTERVAL;
  };

  // Show the feedback notification
  const showFeedbackNotification = () => {
    toast({
      title: "We'd love your feedback!",
      description: "Help us improve Fulbito Stats by sharing your thoughts.",
      variant: "info",
      duration: 10000, // 10 seconds
      action: {
        label: "Give Feedback",
        onClick: handleFeedbackClick
      },
    });

    // Update the last notification timestamp
    localStorage.setItem(LAST_NOTIFICATION_KEY, Date.now().toString());
  };

  // Handle feedback button click
  const handleFeedbackClick = () => {
    openFeedbackForm();
  };

  // Mark feedback as completed (can be called when user submits feedback)
  const markFeedbackCompleted = () => {
    localStorage.setItem(FEEDBACK_COMPLETED_KEY, 'true');
  };

  // Reset feedback status (for testing or if you want to allow multiple feedbacks)
  const resetFeedbackStatus = () => {
    localStorage.removeItem(FEEDBACK_COMPLETED_KEY);
    localStorage.removeItem(LAST_NOTIFICATION_KEY);
  };

  // Initialize the notification system
  useEffect(() => {
    if (!initialized) {
      // Check if we should show a notification on initial load
      if (shouldShowNotification()) {
        showFeedbackNotification();
      }

      setInitialized(true);
    }

    // Set up interval to check periodically
    const checkInterval = setInterval(() => {
      if (shouldShowNotification()) {
        showFeedbackNotification();
      }
    }, 60 * 60 * 1000); // Check every hour

    return () => clearInterval(checkInterval);
  }, [initialized]);

  return {
    openFeedbackForm,
    markFeedbackCompleted,
    resetFeedbackStatus,
    showFeedbackNotification
  };
};
