import { createClient } from '@supabase/supabase-js';
import { Database } from '@/types/supabase';

const supabaseUrl = import.meta.env.VITE_SUPABASE_URL;
const supabaseAnonKey = import.meta.env.VITE_SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseAnonKey) {
  throw new Error('Missing Supabase environment variables');
}

export const supabase = createClient<Database>(supabaseUrl, supabaseAnonKey, {
  auth: {
    autoRefreshToken: true,
    persistSession: true,
    detectSessionInUrl: true // Changed to true to handle OAuth redirects properly
  }
});

// Auth types
export type AuthUser = {
  id: string;
  email?: string;
  user_metadata: {
    full_name?: string;
    avatar_url?: string;
  };
};

export type AuthSession = {
  user: AuthUser;
  access_token: string;
  refresh_token: string;
};

// Auth helper functions
export const getCurrentUser = async () => {
  const { data: { user }, error } = await supabase.auth.getUser();
  if (error) throw error;
  return user;
};

export const getCurrentSession = async () => {
  const { data: { session }, error } = await supabase.auth.getSession();
  if (error) throw error;
  return session;
};

// Helper function to handle session from URL (for OAuth redirects)
export const handleAuthRedirect = async () => {
  try {
    // Check if we have a hash in the URL (OAuth redirect)
    if (window.location.hash) {
      console.log('Detected hash in URL, checking for OAuth redirect...');

      // Let Supabase handle the OAuth redirect
      const { data, error } = await supabase.auth.getSession();

      if (error) {
        console.error('Error getting session after OAuth redirect:', error);
        throw error;
      }

      if (data.session) {
        console.log('Successfully retrieved session from OAuth redirect');
        return data.session;
      }
    }
    return null;
  } catch (err) {
    console.error('Error processing auth redirect:', err);
    return null;
  }
};

// Add methods for group creation, member management, and role-based access control

export async function createGroup(name: string, createdBy: string) {
  console.log('createGroup called with:', { name, createdBy });
  const { data, error } = await supabase
    .from('friend_groups')
    .insert([{ name, created_by: createdBy }]);

  if (error) throw error;
  return data;
}

export async function inviteToGroup(groupId: string, userId: string, role: 'Admin' | 'Collaborator' | 'Guest') {
  const { data, error } = await supabase
    .from('group_members')
    .insert([{ group_id: groupId, user_id: userId, role }]);

  if (error) throw error;
  return data;
}

export async function getGroupDetails(groupId: string) {
  const { data, error } = await supabase
    .from('friend_groups')
    .select('*, group_members(*)')
    .eq('id', groupId);

  if (error) throw error;
  return data;
}

export async function updateGroupDetails(groupId: string, updates: Partial<{ name: string }>) {
  const { data, error } = await supabase
    .from('friend_groups')
    .update(updates)
    .eq('id', groupId);

  if (error) throw error;
  return data;
}
