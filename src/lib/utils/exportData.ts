import { supabase } from '@/lib/supabase';
import { format } from 'date-fns';

// Types for export data
export interface ExportPlayer {
  name: string;
  skills: number;
  effort: number;
  stamina: number;
}

export interface ExportGoalscorer {
  team: 'A' | 'B';
  playerId: number; // This will be an index in the players array
}

export interface ExportMatch {
  match_date: string;
  teama: number[]; // These will be indices in the players array
  teamb: number[];
  // Add name-based team references for better reliability
  teamaByName: string[];
  teambByName: string[];
  scorea: number | null;
  scoreb: number | null;
  winner: 'A' | 'B' | 'Draw' | null;
  goalscorers?: ExportGoalscorer[];
  youtubelink?: string;
}

export interface ExportData {
  players: ExportPlayer[];
  matches: ExportMatch[];
}

/**
 * Fetches and formats group data for export
 * @param groupId The ID of the group to export
 * @returns Promise resolving to the formatted export data
 */
export const fetchGroupDataForExport = async (groupId: string): Promise<ExportData> => {
  try {
    // Fetch players for the group
    const { data: players, error: playersError } = await supabase
      .from('players')
      .select('id, name, skills, effort, stamina, created_at, metadata')
      .eq('group_id', groupId);

    // Sort players by original_index if available in metadata, otherwise by created_at
    if (players) {
      players.sort((a, b) => {
        // If both players have metadata with original_index, use that for sorting
        if (a.metadata?.original_index !== undefined && b.metadata?.original_index !== undefined) {
          return a.metadata.original_index - b.metadata.original_index;
        }
        // Otherwise fall back to created_at timestamp
        return new Date(a.created_at).getTime() - new Date(b.created_at).getTime();
      });

      // Log the player order for debugging
      console.log('Player export order:');
      players.forEach((player, index) => {
        console.log(`  Export index ${index}: ${player.name} (DB ID: ${player.id}, Original index: ${player.metadata?.original_index})`);
      });
    }

    if (playersError) throw playersError;
    if (!players) throw new Error('No players found');

    // Create a map of player IDs to their indices in the export array
    const playerIdToIndexMap = new Map<number, number>();
    players.forEach((player, index) => {
      playerIdToIndexMap.set(player.id, index);
    });

    // Fetch matches for the group
    const { data: matches, error: matchesError } = await supabase
      .from('matches')
      .select('id, match_date, teama, teamb, scorea, scoreb, winner, goalscorers, youtubelink')
      .eq('group_id', groupId)
      .order('match_date', { ascending: false });

    if (matchesError) throw matchesError;
    if (!matches) throw new Error('No matches found');

    // Format players for export
    const exportPlayers: ExportPlayer[] = players.map(player => ({
      name: player.name,
      skills: player.skills,
      effort: player.effort,
      stamina: player.stamina
    }));

    // Format matches for export
    const exportMatches: ExportMatch[] = matches.map(match => {
      // Map player IDs to their indices in the export array
      const teamaIndices = match.teama
        .map(playerId => playerIdToIndexMap.get(playerId))
        .filter((index): index is number => index !== undefined);

      const teambIndices = match.teamb
        .map(playerId => playerIdToIndexMap.get(playerId))
        .filter((index): index is number => index !== undefined);

      // Map goalscorers player IDs to their indices
      const goalscorers = match.goalscorers
        ? match.goalscorers.map(scorer => {
            const playerIndex = playerIdToIndexMap.get(scorer.playerId);
            if (playerIndex !== undefined) {
              return {
                team: scorer.team,
                playerId: playerIndex
              };
            }
            return null;
          }).filter((scorer): scorer is ExportGoalscorer => scorer !== null)
        : undefined;

      // Format the date to ensure it's in the correct format for import
      // Remove timezone offset to match the expected format
      const formattedDate = match.match_date.replace(/\+00:00$/, 'Z').replace(/\+\d{2}:\d{2}$/, 'Z');

      // Get player names for each team
      const teamaNames = match.teama
        .map(playerId => {
          const playerIndex = playerIdToIndexMap.get(playerId);
          return playerIndex !== undefined ? players[playerIndex]?.name : null;
        })
        .filter((name): name is string => name !== null);

      const teambNames = match.teamb
        .map(playerId => {
          const playerIndex = playerIdToIndexMap.get(playerId);
          return playerIndex !== undefined ? players[playerIndex]?.name : null;
        })
        .filter((name): name is string => name !== null);

      return {
        match_date: formattedDate,
        teama: teamaIndices,
        teamb: teambIndices,
        // Add name-based team references
        teamaByName: teamaNames,
        teambByName: teambNames,
        scorea: match.scorea,
        scoreb: match.scoreb,
        winner: match.winner,
        goalscorers,
        youtubelink: match.youtubelink || ''
      };
    });

    return {
      players: exportPlayers,
      matches: exportMatches
    };
  } catch (error) {
    console.error('Error fetching group data for export:', error);
    throw error;
  }
};

/**
 * Generates a downloadable JSON file from the export data
 * @param data The data to export
 * @param groupName The name of the group (used for the filename)
 */
export const generateExportFile = (data: ExportData, groupName: string): void => {
  try {
    // Create a JSON string from the data
    const jsonString = JSON.stringify(data, null, 2);

    // Create a blob from the JSON string
    const blob = new Blob([jsonString], { type: 'application/json' });

    // Create a URL for the blob
    const url = URL.createObjectURL(blob);

    // Create a link element
    const link = document.createElement('a');

    // Set the link's attributes
    link.href = url;
    link.download = `${groupName.replace(/[^a-z0-9]/gi, '_').toLowerCase()}_export_${format(new Date(), 'yyyy-MM-dd')}.json`;

    // Append the link to the document
    document.body.appendChild(link);

    // Click the link to trigger the download
    link.click();

    // Clean up
    document.body.removeChild(link);
    URL.revokeObjectURL(url);
  } catch (error) {
    console.error('Error generating export file:', error);
    throw error;
  }
};
