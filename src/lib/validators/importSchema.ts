import { z } from 'zod';

// Define the import data schema directly instead of using omit
export const importPlayerSchema = z.object({
  name: z.string().min(2).max(50),
  skills: z.number().min(0).max(100),
  effort: z.number().min(0).max(100),
  stamina: z.number().min(0).max(100)
});

export const importMatchSchema = z.object({
  match_date: z.string().refine(
    (date) => {
      try {
        // Try to parse the date string
        return !isNaN(new Date(date).getTime());
      } catch (e) {
        return false;
      }
    },
    { message: "Invalid datetime format. Use ISO 8601 format (YYYY-MM-DDTHH:MM:SSZ)" }
  ),
  // Support both index-based and name-based team references
  teama: z.array(z.number()).min(1).max(11).optional(),
  teamb: z.array(z.number()).min(1).max(11).optional(),
  teamaByName: z.array(z.string()).min(1).max(11).optional(),
  teambByName: z.array(z.string()).min(1).max(11).optional(),
  scorea: z.number().int().min(0).max(99).nullable(),
  scoreb: z.number().int().min(0).max(99).nullable(),
  winner: z.enum(['A', 'B', 'Draw']).nullable(),
  goalscorers: z.array(
    z.object({
      team: z.enum(['A', 'B']),
      playerId: z.number().min(0) // Allow 0-based indices
    })
  ).max(99).optional().default([]),
  youtubelink: z.string().url().nullable().or(z.literal('')).optional().default('')
}).refine(
  data => {
    if (data.scorea !== null && data.scoreb !== null) {
      if (data.scorea > data.scoreb) return data.winner === 'A';
      if (data.scoreb > data.scorea) return data.winner === 'B';
      return data.winner === 'Draw';
    }
    return true;
  },
  { message: 'Winner must match score difference' }
).refine(
  data => {
    // Ensure at least one team format is provided
    return (
      (Array.isArray(data.teama) && data.teama.length > 0) ||
      (Array.isArray(data.teamaByName) && data.teamaByName.length > 0)
    ) && (
      (Array.isArray(data.teamb) && data.teamb.length > 0) ||
      (Array.isArray(data.teambByName) && data.teambByName.length > 0)
    );
  },
  { message: 'Each match must have teams specified either by index (teama/teamb) or by name (teamaByName/teambByName)' }
);

// Define the import file schema
export const importFileSchema = z.object({
  players: z.array(importPlayerSchema),
  matches: z.array(importMatchSchema),
});

export type ImportPlayer = z.infer<typeof importPlayerSchema>;
export type ImportMatch = z.infer<typeof importMatchSchema> & {
  // Add optional name-based fields to the type
  teamaByName?: string[];
  teambByName?: string[];
};
export type ImportData = z.infer<typeof importFileSchema>;

// Validation function for the import file
export const validateImportData = (data: unknown): ImportData => {
  try {
    return importFileSchema.parse(data);
  } catch (error) {
    if (error instanceof z.ZodError) {
      // Format the error message for better readability
      const formattedErrors = error.errors.map(err => {
        return `${err.path.join('.')}: ${err.message}`;
      }).join('\n');

      throw new Error(`Invalid import data format:\n${formattedErrors}`);
    }
    throw error;
  }
};

// Helper function to get import summary
export const getImportSummary = (data: ImportData) => {
  return {
    playerCount: data.players.length,
    matchCount: data.matches.length,
    samplePlayers: data.players.slice(0, 3),
    sampleMatches: data.matches.slice(0, 3),
  };
};
