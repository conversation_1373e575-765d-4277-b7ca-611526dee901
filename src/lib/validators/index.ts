import { z } from 'zod';

export const playerSchema = z.object({
  name: z.string().min(2).max(50),
  skills: z.number().min(0).max(100),
  effort: z.number().min(0).max(100),
  stamina: z.number().min(0).max(100),
  group_id: z.string().uuid().optional(),
  metadata: z.record(z.any()).optional(), // Allow storing metadata like original_index
});

export const matchSchema = z.object({
  match_date: z.string().datetime(),
  teama: z.array(z.number()).min(1).max(11),
  teamb: z.array(z.number()).min(1).max(11),
  scorea: z.number().int().min(0).max(99).nullable(),
  scoreb: z.number().int().min(0).max(99).nullable(),
  winner: z.enum(['A', 'B', 'Draw']).nullable(),
  goalscorers: z.array(
    z.object({
      team: z.enum(['A', 'B']),
      playerId: z.number().positive()
    })
  ).max(99),
  youtubelink: z.string().url().nullable().or(z.literal('')),
  group_id: z.string().uuid().optional()
}).refine(
  data => {
    if (data.scorea !== null && data.scoreb !== null) {
      if (data.scorea > data.scoreb) return data.winner === 'A';
      if (data.scoreb > data.scorea) return data.winner === 'B';
      return data.winner === 'Draw';
    }
    return true;
  },
  { message: 'Winner must match score difference' }
);

export const validatePlayer = (data: unknown) => playerSchema.parse(data);
export const validateMatch = (data: unknown) => matchSchema.parse(data);
