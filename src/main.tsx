import { createRoot } from 'react-dom/client'
import App from './App'
import './index.css'
import './i18n/i18n' // Import i18n configuration
import * as serviceWorkerRegistration from './serviceWorkerRegistration'

createRoot(document.getElementById("root")!).render(
  <App />
);

// If you want your app to work offline and load faster, you can change
// unregister() to register() below. Note this comes with some pitfalls.
// Learn more about service workers: https://cra.link/PWA
serviceWorkerRegistration.register({
  onUpdate: (registration) => {
    // Show a notification to the user about the update
    const shouldUpdate = window.confirm(
      'A new version of the app is available. Update now?'
    );
    if (shouldUpdate && registration.waiting) {
      // Send a message to the waiting service worker to skip waiting
      registration.waiting.postMessage({ type: 'SKIP_WAITING' });
      // Reload the page to load the new version
      window.location.reload();
    }
  },
});
