import { useState, useRef, useEffect } from "react";
import { Link } from "react-router-dom";
import { useTranslation } from "react-i18next";
import { But<PERSON> } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { Play, Users, Shuffle, Calendar, Handshake, Trophy, BarChart3, Target, Zap } from "lucide-react";
import LogoComponent from "@/components/LogoComponent";
import { HalftoneBackground } from "@/components/ui/halftone-background";
import AuthThemeToggle from "@/components/auth/AuthThemeToggle";
import { useTheme } from "@/context/ThemeContext";

const LandingPage = () => {
  const { t } = useTranslation();
  const { theme } = useTheme();

  // Set page title
  useEffect(() => {
    document.title = t('app.name');
  }, [t]);

  const textColor = theme === 'dark' ? 'text-white' : 'text-foreground';
  const cardBg = theme === 'dark' ? 'bg-card/80 backdrop-blur-sm' : 'bg-card/80 backdrop-blur-sm';
  const cardHover = theme === 'dark' ? 'hover:bg-card/90' : 'hover:bg-card/90';

  return (
    <div className="min-h-screen relative">
      <HalftoneBackground />
      <AuthThemeToggle />
      <div className="relative z-10 container mx-auto px-4 py-12">
        {/* Hero Section */}
        <div className="flex flex-col lg:flex-row items-center justify-between gap-8 lg:gap-12 mb-16 lg:mb-20">
          <div className="lg:w-1/2 text-center lg:text-left">
            <div className="flex flex-col sm:flex-row items-center justify-center lg:justify-start gap-4 mb-6">
              <div className="h-20 w-20 sm:h-24 sm:w-24 transition-transform duration-300 hover:scale-110">
                <LogoComponent to="/" height="100%" width="100%" />
              </div>
              <h1 className={`text-3xl sm:text-4xl lg:text-5xl xl:text-6xl font-bold ${textColor} transition-all duration-300`}>
                {t('app.name')}
              </h1>
            </div>
            <h2 className="text-xl sm:text-2xl lg:text-3xl font-semibold mb-4 lg:mb-6 text-soccer-primary animate-fade-in">
              {t('landing.subtitle')}
            </h2>
            <p className={`text-base sm:text-lg lg:text-xl mb-8 lg:mb-10 ${textColor} max-w-2xl mx-auto lg:mx-0 leading-relaxed animate-fade-in`}>
              {t('landing.description')}
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center lg:justify-start">
              <Button
                asChild
                className="bg-soccer-primary hover:bg-soccer-primary/90 text-white px-6 sm:px-8 py-4 sm:py-6 text-base sm:text-lg font-semibold shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105 animate-fade-in"
              >
                <Link to="/login">{t('landing.login')}</Link>
              </Button>
              <Button
                asChild
                variant="outline"
                className="border-soccer-primary text-soccer-primary hover:bg-soccer-primary/10 px-6 sm:px-8 py-4 sm:py-6 text-base sm:text-lg transition-all duration-300 hover:scale-105 animate-fade-in"
              >
                <Link to="/signup">{t('landing.signup')}</Link>
              </Button>
            </div>
          </div>
          <div className="lg:w-1/2 w-full max-w-2xl">
            <div className="aspect-video bg-black rounded-lg overflow-hidden shadow-xl hover:shadow-2xl transition-all duration-300 hover:scale-105">
              <iframe
                className="w-full h-full"
                src="https://www.youtube.com/embed/hodwwh5FwZo?rel=0&modestbranding=1&showinfo=0"
                title="Fulbito Stats Showcase"
                frameBorder="0"
                allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share"
                allowFullScreen
              />
            </div>
          </div>
        </div>

        {/* Features Section */}
        <div className="mb-16 lg:mb-20">
          <h2 className={`text-2xl sm:text-3xl lg:text-4xl font-bold mb-8 lg:mb-12 text-center ${textColor} animate-fade-in`}>
            {t('landing.features')}
          </h2>
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-6 lg:gap-8 mb-8">
            <Card className={`p-6 transition-all duration-300 ${cardBg} ${cardHover} border-0 shadow-lg hover:shadow-xl hover:scale-105`}>
              <div className="flex flex-col items-center text-center">
                <div className="bg-soccer-primary/10 p-4 rounded-full mb-4">
                  <BarChart3 className="h-8 w-8 text-soccer-primary" />
                </div>
                <h3 className={`text-xl font-semibold mb-2 ${textColor}`}>
                  {t('landing.feature1Title')}
                </h3>
                <p className={`text-sm ${theme === 'dark' ? 'text-muted-foreground' : 'text-muted-foreground'}`}>
                  {t('landing.feature1Description')}
                </p>
              </div>
            </Card>

            <Card className={`p-6 transition-all duration-300 ${cardBg} ${cardHover} border-0 shadow-lg hover:shadow-xl hover:scale-105`}>
              <div className="flex flex-col items-center text-center">
                <div className="bg-soccer-primary/10 p-4 rounded-full mb-4">
                  <Shuffle className="h-8 w-8 text-soccer-primary" />
                </div>
                <h3 className={`text-xl font-semibold mb-2 ${textColor}`}>
                  {t('landing.feature2Title')}
                </h3>
                <p className={`text-sm ${theme === 'dark' ? 'text-muted-foreground' : 'text-muted-foreground'}`}>
                  {t('landing.feature2Description')}
                </p>
              </div>
            </Card>

            <Card className={`p-6 transition-all duration-300 ${cardBg} ${cardHover} border-0 shadow-lg hover:shadow-xl hover:scale-105`}>
              <div className="flex flex-col items-center text-center">
                <div className="bg-soccer-primary/10 p-4 rounded-full mb-4">
                  <Calendar className="h-8 w-8 text-soccer-primary" />
                </div>
                <h3 className={`text-xl font-semibold mb-2 ${textColor}`}>
                  {t('landing.feature3Title')}
                </h3>
                <p className={`text-sm ${theme === 'dark' ? 'text-muted-foreground' : 'text-muted-foreground'}`}>
                  {t('landing.feature3Description')}
                </p>
              </div>
            </Card>

            <Card className={`p-6 transition-all duration-300 ${cardBg} ${cardHover} border-0 shadow-lg hover:shadow-xl hover:scale-105`}>
              <div className="flex flex-col items-center text-center">
                <div className="bg-soccer-primary/10 p-4 rounded-full mb-4">
                  <Handshake className="h-8 w-8 text-soccer-primary" />
                </div>
                <h3 className={`text-xl font-semibold mb-2 ${textColor}`}>
                  {t('landing.feature4Title')}
                </h3>
                <p className={`text-sm ${theme === 'dark' ? 'text-muted-foreground' : 'text-muted-foreground'}`}>
                  {t('landing.feature4Description')}
                </p>
              </div>
            </Card>

            <Card className={`p-6 transition-all duration-300 ${cardBg} ${cardHover} border-0 shadow-lg hover:shadow-xl hover:scale-105`}>
              <div className="flex flex-col items-center text-center">
                <div className="bg-soccer-primary/10 p-4 rounded-full mb-4">
                  <Trophy className="h-8 w-8 text-soccer-primary" />
                </div>
                <h3 className={`text-xl font-semibold mb-2 ${textColor}`}>
                  {t('landing.feature5Title')}
                </h3>
                <p className={`text-sm ${theme === 'dark' ? 'text-muted-foreground' : 'text-muted-foreground'}`}>
                  {t('landing.feature5Description')}
                </p>
              </div>
            </Card>

            <Card className={`p-6 transition-all duration-300 ${cardBg} ${cardHover} border-0 shadow-lg hover:shadow-xl hover:scale-105`}>
              <div className="flex flex-col items-center text-center">
                <div className="bg-soccer-primary/10 p-4 rounded-full mb-4">
                  <Target className="h-8 w-8 text-soccer-primary" />
                </div>
                <h3 className={`text-xl font-semibold mb-2 ${textColor}`}>
                  {t('landing.feature6Title')}
                </h3>
                <p className={`text-sm ${theme === 'dark' ? 'text-muted-foreground' : 'text-muted-foreground'}`}>
                  {t('landing.feature6Description')}
                </p>
              </div>
            </Card>
          </div>
        </div>

        {/* CTA Section */}
        <div className="text-center max-w-4xl mx-auto">
          <h2 className={`text-2xl sm:text-3xl lg:text-4xl font-bold mb-6 lg:mb-8 ${textColor} animate-fade-in`}>
            {t('landing.getStarted')}
          </h2>
          <div className="flex flex-col lg:flex-row justify-center gap-4 lg:gap-6 items-center">
            <Button
              className="w-full sm:w-auto bg-soccer-primary hover:bg-soccer-primary/90 text-white px-6 sm:px-8 py-4 sm:py-6 text-base sm:text-lg font-semibold shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105 animate-fade-in"
              onClick={() => {
                // Future implementation for live demo
                console.log('Live Demo clicked - Feature coming soon!');
              }}
            >
              <Play className="mr-2 h-4 sm:h-5 w-4 sm:w-5" />
              {t('landing.liveDemo')}
            </Button>
            <div className="flex flex-col sm:flex-row gap-4 w-full sm:w-auto">
              <Button
                asChild
                variant="outline"
                className="w-full sm:w-auto border-soccer-primary text-soccer-primary hover:bg-soccer-primary/10 px-6 sm:px-8 py-4 sm:py-6 text-base sm:text-lg transition-all duration-300 hover:scale-105 animate-fade-in"
              >
                <Link to="/login">{t('landing.login')}</Link>
              </Button>
              <Button
                asChild
                variant="outline"
                className="w-full sm:w-auto border-soccer-primary text-soccer-primary hover:bg-soccer-primary/10 px-6 sm:px-8 py-4 sm:py-6 text-base sm:text-lg transition-all duration-300 hover:scale-105 animate-fade-in"
              >
                <Link to="/signup">{t('landing.signup')}</Link>
              </Button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default LandingPage;
