import { useState, useMemo, useEffect } from "react";
import { useTranslation } from "react-i18next";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import UnifiedLayout from "@/components/layout/UnifiedLayout";
import ConfirmationModal from "@/components/ConfirmationModal";
import { MatchComments } from "@/components/matches/MatchComments";
import { MatchFiltersComponent, MatchFilters as MatchFiltersType } from "@/components/matches/MatchFilters";
import {
  Dialog,
  DialogContent,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Badge } from "@/components/ui/badge";
import { format, parseISO } from "date-fns";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import { Checkbox } from "@/components/ui/checkbox"
import { useToast } from "@/hooks/use-toast";
import { useEnhancedToast } from "@/hooks/use-enhanced-toast";
import { PostgrestError } from '@supabase/supabase-js';
import { supabase } from "@/lib/supabase";
import { useAuth } from "@/context/AuthContext";
import { useGroup } from "@/lib/context/GroupContext";
import { EmptyState } from "@/components/ui/empty-state";
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";
import {
  Trophy,
  Calendar,
  Youtube,
  Users,
  ChevronRight,
  Plus,
  Edit,
  Trash2,
  CalendarDays,
  Search,
  Loader2,
  Goal,
  ArrowRight,
  ArrowLeft,
  Save,
  X,
  CalendarPlus,
  Info,
  Filter
} from "lucide-react";

// Enhanced components
import { EnhancedCard } from "@/components/ui/enhanced-card";
import { ResponsiveTable } from "@/components/ui/responsive-table";
import { EnhancedInput } from "@/components/ui/enhanced-input";
import { EnhancedDatePicker } from "@/components/ui/enhanced-date-picker";
import { AnimatedButton } from "@/components/ui/animated-button";

// localStorage Keys (Keep PLAYERS_STORAGE_KEY for now, remove MATCHES_STORAGE_KEY)
// const PLAYERS_STORAGE_KEY = 'soccerPlayersData';
// const MATCHES_STORAGE_KEY = 'soccerMatchesData'; // No longer needed for matches

// --- Interfaces ---
interface Player { id: number; name: string; skills?: number; effort?: number; stamina?: number; }
interface Goalscorer { team: 'A' | 'B'; playerId: number; }
interface Match {
  id: number;
  created_at?: string;
  match_date: string | Date;
  teama: number[];
  teamb: number[];
  scorea: number | null;
  scoreb: number | null;
  winner?: 'A' | 'B' | 'Draw';
  goalscorers?: Goalscorer[];
  youtubelink?: string;
  group_id?: string;
}

// Add this interface near the top with other interfaces
interface PlayerWithGames extends Player {
  gamesPlayed: number;
}

// Function to load player data (still needed for names)
// function loadPlayers(key: string, fallback: Player[]): Player[] {
//     try {
//       const storedData = typeof window !== 'undefined' ? localStorage.getItem(key) : null;
//       return storedData ? JSON.parse(storedData) as Player[] : fallback;
//     } catch (error) {
//       console.error(`Error reading ${key} from localStorage:`, error);
//     }
//     return fallback;
// }
// const playersData = loadPlayers(PLAYERS_STORAGE_KEY, []);

const defaultFormData = {
  date: format(new Date(), "yyyy-MM-dd"),
  teamA: [] as number[],
  teamB: [] as number[],
  scoreA: "" as string | number,
  scoreB: "" as string | number,
  winner: undefined as 'A' | 'B' | 'Draw' | undefined,
  goalscorers: [] as Goalscorer[],
  youtubeLink: "",
  autoScore: false,
};

const MatchesPage = () => {
  const { t } = useTranslation();
  // Add new state for player search
  const [playerSearch, setPlayerSearch] = useState("");
  const [matches, setMatches] = useState<Match[]>([]);
  const [loading, setLoading] = useState(true);
  const [isProcessing, setIsProcessing] = useState(false);
  const [players, setPlayers] = useState<PlayerWithGames[]>([]);
  const [isAddModalOpen, setIsAddModalOpen] = useState(false);
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  const [selectedMatch, setSelectedMatch] = useState<Match | null>(null);
  const [selectedMatchIds, setSelectedMatchIds] = useState<number[]>([]);
  const [isBulkDeleteModalOpen, setIsBulkDeleteModalOpen] = useState(false);
  const [formData, setFormData] = useState(defaultFormData);
  const [filters, setFilters] = useState<MatchFiltersType>({
    search: "",
    dateRange: { from: undefined, to: undefined },
    players: [],
    resultFilter: 'all',
    sortBy: 'date-desc'
  });
  const { toast } = useToast();

  const selectedGroupId = localStorage.getItem('selectedGroupId');

  // Fetch matches from Supabase on mount
  useEffect(() => {
    const fetchMatches = async () => {
      if (!selectedGroupId) {
        console.warn("No group selected. Please select a group first.");
        setMatches([]);
        setLoading(false);
        return;
      }

      setLoading(true);
      const { data, error } = await supabase
        .from('matches')
        .select('*')
        .eq('group_id', selectedGroupId)
        .order('match_date', { ascending: false }); // Order by date

      if (error) {
        console.error("Error fetching matches:", error);
        toast({ title: t('matches.fetchError', 'Error fetching matches'), description: error.message, variant: "destructive" });
        setMatches([]);
      } else if (data) {
        // Parse match_date string to Date object
        const formattedData = data.map(m => ({ ...m, match_date: parseISO(m.match_date as string) }));
        setMatches(formattedData);
      }
      setLoading(false);
    };
    fetchMatches();
  }, [toast, selectedGroupId]);

  // Add new useEffect to fetch players
  useEffect(() => {
    const fetchPlayersAndCalculateGames = async () => {
      if (!selectedGroupId) {
        console.warn("No group selected. Please select a group first.");
        setPlayers([]);
        return;
      }

      try {
        // First fetch all matches for this group
        const { data: matchesData, error: matchesError } = await supabase
          .from('matches')
          .select('teama, teamb')
          .eq('group_id', selectedGroupId);

        if (matchesError) throw matchesError;

        // Then fetch all players for this group
        const { data: playersData, error: playersError } = await supabase
          .from('players')
          .select('*')
          .eq('group_id', selectedGroupId)
          .order('name') as { data: Player[] | null; error: PostgrestError | null };

        if (playersError) throw playersError;

        if (playersData) {
          // Calculate games played for each player
          const playersWithGames = playersData.map(player => {
            const gamesPlayed = (matchesData || []).reduce((count, match) => {
              if (match.teama.includes(player.id) || match.teamb.includes(player.id)) {
                return count + 1;
              }
              return count;
            }, 0);
            return { ...player, gamesPlayed };
          });

          // Sort by games played (descending) and then by name
          const sortedPlayers = playersWithGames.sort((a, b) => {
            if (b.gamesPlayed === a.gamesPlayed) {
              return a.name.localeCompare(b.name);
            }
            return b.gamesPlayed - a.gamesPlayed;
          });

          setPlayers(sortedPlayers);
        }
      } catch (error) {
        const e = error as PostgrestError;
        console.error("Error fetching players:", e);
        toast({
          title: t('players.fetchError', 'Error fetching players'),
          description: e.message,
          variant: "destructive",
        });
        setPlayers([]);
      }
    };

    fetchPlayersAndCalculateGames();
  }, [toast, selectedGroupId]);

  const getPlayerName = (id: number): string => {
    const player = players.find((p) => p.id === id);
    return player ? player.name : `Player ${id}`;
  };

  const handleAddMatch = () => {
    setFormData(defaultFormData);
    setSelectedMatch(null);
    setIsAddModalOpen(true);
  };

  const handleEditMatch = (match: Match) => {
    setSelectedMatchIds([]);
    setFormData({
      date: format(match.match_date instanceof Date ? match.match_date : parseISO(match.match_date as string), "yyyy-MM-dd"),
      teamA: match.teama,
      teamB: match.teamb,
      scoreA: match.scorea === null ? "" : match.scorea,
      scoreB: match.scoreb === null ? "" : match.scoreb,
      winner: match.winner,
      goalscorers: match.goalscorers || [],
      youtubeLink: match.youtubelink || "",
    });
    setSelectedMatch(match);
    setIsAddModalOpen(true);
  };

  const handleDeleteClick = (match: Match) => {
    setSelectedMatchIds([]);
    setSelectedMatch(match);
    setIsDeleteModalOpen(true);
  };

  // Single Delete Confirmation
  const handleDeleteConfirm = async () => {
    if (selectedMatch) {
      setIsProcessing(true);
      const { error } = await supabase
        .from('matches')
        .delete()
        .eq('id', selectedMatch.id);

      setIsProcessing(false);
      if (error) {
        console.error('Error deleting match:', error);
        toast({ title: t('matches.deleteError', 'Error deleting match'), description: error.message, variant: "destructive" });
      } else {
        setMatches(prevMatches => prevMatches.filter((m) => m.id !== selectedMatch.id));
        toast({ title: t('matches.deleteSuccess', 'Match deleted successfully!') });
        setSelectedMatch(null);
      }
      setIsDeleteModalOpen(false);
    }
  };

  // Bulk Delete Confirmation
  const handleBulkDeleteConfirm = async () => {
      if (selectedMatchIds.length === 0) return;
      setIsProcessing(true);
      const { error } = await supabase
        .from('matches')
        .delete()
        .in('id', selectedMatchIds); // Use .in() for multiple IDs

      setIsProcessing(false);
      if (error) {
        console.error('Error deleting matches:', error);
        toast({ title: t('matches.bulkDeleteError', 'Error deleting selected matches'), description: error.message, variant: "destructive" });
      } else {
        setMatches(prevMatches => prevMatches.filter(match => !selectedMatchIds.includes(match.id)));
        toast({ title: t('matches.bulkDeleteSuccess', '{{count}} match(es) deleted successfully!', { count: selectedMatchIds.length }) });
        setSelectedMatchIds([]);
      }
      setIsBulkDeleteModalOpen(false);
  };

  const isFormValid = useMemo(() => {
    // Date is required
    if (!formData.date) return false;

    // At least one way to determine winner must be present
    const hasScore = formData.scoreA !== "" && formData.scoreB !== "";
    const hasWinner = formData.winner !== undefined;
    const hasGoalscorers = (formData.goalscorers?.length || 0) > 0;

    return formData.date && (hasScore || hasWinner || hasGoalscorers);
  }, [formData]);

  const handleFormSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!isFormValid) {
      toast({
        title: t('matches.invalidForm', 'Invalid form'),
        description: t('matches.formRequirements', 'Please enter a date and either scores, a winner, or goalscorers.'),
        variant: "destructive"
      });
      return;
    }

    setIsProcessing(true);

    const scorea = formData.scoreA === "" ? null : parseInt(String(formData.scoreA), 10);
    const scoreb = formData.scoreB === "" ? null : parseInt(String(formData.scoreB), 10);
    let winner = formData.winner;

    if (winner === undefined && scorea !== null && scoreb !== null) {
      if (scorea > scoreb) winner = 'A';
      else if (scoreb > scorea) winner = 'B';
      else winner = 'Draw';
    }

    // Make sure we have a selected group
    if (!selectedGroupId) {
      toast({ title: t('errors.error'), description: t('groups.noGroupSelected'), variant: "destructive" });
      setIsProcessing(false);
      return;
    }

    // Match Supabase column names
    // Create a date with time set to noon UTC to avoid timezone issues
    const dateObj = new Date(`${formData.date}T12:00:00Z`);

    const matchData = {
      match_date: dateObj.toISOString(), // Use ISO string with time component
      teama: formData.teamA,
      teamb: formData.teamB,
      scorea: scorea,
      scoreb: scoreb,
      winner: winner,
      goalscorers: formData.goalscorers,
      youtubelink: formData.youtubeLink || null,
      group_id: selectedGroupId,
    };

    let error = null;
    let data: Match | null = null;

    try {
      if (selectedMatch) {
        // Edit existing match
        const { data: updateData, error: updateError } = await supabase
          .from('matches')
          .update(matchData)
          .eq('id', selectedMatch.id)
          .select()
          .single();
        data = updateData ? { ...updateData, match_date: parseISO(updateData.match_date as string) } : null;
        error = updateError;
      } else {
        // Add new match
        const { data: insertData, error: insertError } = await supabase
          .from('matches')
          .insert(matchData)
          .select()
          .single();
        data = insertData ? { ...insertData, match_date: parseISO(insertData.match_date as string) } : null;
        error = insertError;
      }
    } catch (e) {
       error = e;
    }

    setIsProcessing(false);

    if (error) {
      console.error('Error saving match:', error);
      toast({
        title: selectedMatch ? t('matches.updateError', 'Error updating match') : t('matches.addError', 'Error adding match'),
        description: (error as Error).message,
        variant: "destructive"
      });
    } else if (data) {
      if (selectedMatch) {
        setMatches(prevMatches =>
          prevMatches.map((m) => (m.id === data!.id ? data! : m))
        );
        toast({ title: t('matches.updateSuccess', 'Match updated successfully!') });
      } else {
        setMatches(prevMatches => [data!, ...prevMatches]); // Add new match to the top
        toast({ title: t('matches.addSuccess', 'Match added successfully!') });
      }
      setIsAddModalOpen(false);
    }
  };

  const getResultDetails = (match: Match): { text: string; class: string } => {
    if (match.winner === 'A') return { text: t('matches.win', 'Win'), class: "bg-green-100 text-green-800" };
    if (match.winner === 'B') return { text: t('matches.loss', 'Loss'), class: "bg-red-100 text-red-800" };
    if (match.winner === 'Draw') return { text: t('matches.draw', 'Draw'), class: "bg-yellow-100 text-yellow-800" };
    return { text: t('common.na', 'N/A'), class: "bg-gray-100 text-gray-800" };
  };

  const handleTogglePlayerSelection = (teamKey: "teamA" | "teamB", playerId: number) => {
      const currentTeam = formData[teamKey];
      const otherTeamKey = teamKey === 'teamA' ? 'teamB' : 'teamA';
      const otherTeam = formData[otherTeamKey];
      let newTeam;
      let newOtherTeam = [...otherTeam];
      let newGoalscorers = [...formData.goalscorers];

      if (currentTeam.includes(playerId)) {
        newTeam = currentTeam.filter(id => id !== playerId);
        newGoalscorers = newGoalscorers.filter(g => !(g.team === teamKey.slice(-1).toUpperCase() && g.playerId === playerId));
      } else {
        newTeam = [...currentTeam, playerId];
        if (otherTeam.includes(playerId)) {
           newOtherTeam = otherTeam.filter(id => id !== playerId);
           newGoalscorers = newGoalscorers.filter(g => !(g.team === otherTeamKey.slice(-1).toUpperCase() && g.playerId === playerId));
        }
      }

      setFormData({
        ...formData,
        [teamKey]: newTeam,
        [otherTeamKey]: newOtherTeam,
        goalscorers: newGoalscorers
      });
  };

  const handleGoalscorerChange = (team: 'A' | 'B', playerId: number, checked: boolean) => {
      let currentGoalscorers = formData.goalscorers || [];
      if (checked) {
        setFormData({
          ...formData,
          goalscorers: [...currentGoalscorers, { team, playerId }],
          // Only update scores if in auto-scoring mode
          ...(formData.autoScore ? {
            scoreA: team === 'A' ? (Number(formData.scoreA) || 0) + 1 : formData.scoreA,
            scoreB: team === 'B' ? (Number(formData.scoreB) || 0) + 1 : formData.scoreB,
          } : {})
        });
      } else {
        const indexToRemove = currentGoalscorers.findIndex(g => g.team === team && g.playerId === playerId);
        if (indexToRemove > -1) {
          const updatedGoalscorers = [...currentGoalscorers];
          updatedGoalscorers.splice(indexToRemove, 1);
          setFormData({
            ...formData,
            goalscorers: updatedGoalscorers,
            // Only update scores if in auto-scoring mode
            ...(formData.autoScore ? {
              scoreA: team === 'A' ? Math.max(0, (Number(formData.scoreA) || 0) - 1) : formData.scoreA,
              scoreB: team === 'B' ? Math.max(0, (Number(formData.scoreB) || 0) - 1) : formData.scoreB,
            } : {})
          });
        }
      }
  };

  const getGoalscorerCount = (team: 'A' | 'B', playerId: number): number => {
      return formData.goalscorers.filter(g => g.team === team && g.playerId === playerId).length;
  }

  // Enhanced filtering with multiple criteria
  const filteredMatches = useMemo(() => {
    let result = [...matches];

    // Text search filter
    if (filters.search) {
      const lowerCaseSearch = filters.search.toLowerCase();
      result = result.filter(match => {
        if (!match || typeof match !== 'object') return false;
        const teamAPlayers = (match.teama || []).map(getPlayerName).join(' ').toLowerCase();
        const teamBPlayers = (match.teamb || []).map(getPlayerName).join(' ').toLowerCase();
        return teamAPlayers.includes(lowerCaseSearch) || teamBPlayers.includes(lowerCaseSearch);
      });
    }

    // Date range filter
    if (filters.dateRange.from || filters.dateRange.to) {
      result = result.filter(match => {
        const matchDate = match.match_date instanceof Date
          ? match.match_date
          : typeof match.match_date === 'string'
            ? new Date(match.match_date)
            : new Date();

        if (filters.dateRange.from && matchDate < filters.dateRange.from) return false;
        if (filters.dateRange.to) {
          // Set the end date to the end of the day for inclusive filtering
          const endDate = new Date(filters.dateRange.to);
          endDate.setHours(23, 59, 59, 999);
          if (matchDate > endDate) return false;
        }
        return true;
      });
    }

    // Player filter
    if (filters.players.length > 0) {
      result = result.filter(match => {
        return filters.players.some(playerId =>
          match.teama?.includes(playerId) || match.teamb?.includes(playerId)
        );
      });
    }

    // Result filter
    if (filters.resultFilter !== 'all') {
      result = result.filter(match => {
        if (filters.resultFilter === 'wins') return match.winner === 'A';
        if (filters.resultFilter === 'losses') return match.winner === 'B';
        if (filters.resultFilter === 'draws') return match.winner === 'Draw';
        return true;
      });
    }

    // Sorting
    result.sort((a, b) => {
      const dateA = a.match_date instanceof Date ? a.match_date : new Date(a.match_date);
      const dateB = b.match_date instanceof Date ? b.match_date : new Date(b.match_date);

      if (filters.sortBy === 'date-desc') return dateB.getTime() - dateA.getTime();
      if (filters.sortBy === 'date-asc') return dateA.getTime() - dateB.getTime();

      if (filters.sortBy === 'score-desc' || filters.sortBy === 'score-asc') {
        const scoreA = (a.scorea || 0) + (a.scoreb || 0);
        const scoreB = (b.scorea || 0) + (b.scoreb || 0);
        return filters.sortBy === 'score-desc' ? scoreB - scoreA : scoreA - scoreB;
      }

      return 0;
    });

    // Add debugging logs
    console.log('Filtering matches:', {
      total: matches.length,
      filtered: result.length,
      searchFilter: filters.search,
      dateRange: filters.dateRange,
      playerFilter: filters.players,
      resultFilter: filters.resultFilter,
      sortBy: filters.sortBy
    });

    return result;
  }, [matches, filters, players, getPlayerName]);

  const renderScoreDisplay = (match: Match) => {
    if (match.scorea !== null && match.scoreb !== null) {
      return <>{match.scorea} - {match.scoreb}</>;
    }
    if (match.winner === 'A') return <span className="text-green-600">TEAM A WIN</span>;
    if (match.winner === 'B') return <span className="text-green-600">TEAM B WIN</span>;
    if (match.winner === 'Draw') return <span className="text-yellow-600">DRAW</span>;
    return null;
  };

  const handleCheckboxChange = (matchId: number, checked: boolean | string) => {
    setSelectedMatchIds(prev => {
        if (checked) return [...prev, matchId];
        else return prev.filter(id => id !== matchId);
    });
  };

  const handleBulkDeleteClick = () => {
      if (selectedMatchIds.length > 0) setIsBulkDeleteModalOpen(true);
  };

  // Add new function to filter players
  const filteredModalPlayers = useMemo(() => {
    if (!playerSearch.trim()) return players;

    // Split the search input by commas, trim whitespace, and convert to lowercase
    const searchTerms = playerSearch
      .split(',')
      .map(term => term.trim().toLowerCase())
      .filter(term => term.length > 0);

    // Filter players whose names match any of the search terms
    return players.filter(player =>
      searchTerms.some(term => player.name.toLowerCase().includes(term))
    );
  }, [players, playerSearch]);

  // Group matches by month/year
  const groupedMatches = useMemo(() => {
    const groups = filteredMatches.reduce((acc, match) => {
      // Ensure we have a valid date
      const matchDate = match.match_date instanceof Date
        ? match.match_date
        : typeof match.match_date === 'string'
          ? new Date(match.match_date)
          : new Date();

      // Only proceed if we have a valid date
      if (!isNaN(matchDate.getTime())) {
        // Use the current i18n language for date formatting
        const monthYear = format(matchDate, 'MMMM yyyy');
        if (!acc[monthYear]) {
          acc[monthYear] = [];
        }
        acc[monthYear].push({
          ...match,
          date: matchDate // Store the parsed date
        });
      }
      return acc;
    }, {} as Record<string, Match[]>);

    // Sort groups by date (newest first)
    const sortedGroups = Object.entries(groups).sort((a, b) => {
      const dateA = new Date(a[1][0].date);
      const dateB = new Date(b[1][0].date);
      return dateB.getTime() - dateA.getTime();
    });

    // Add debugging log for grouped matches
    console.log('Grouped matches:', {
      filteredCount: filteredMatches.length,
      groupCount: sortedGroups.length,
      groups: sortedGroups.map(([month, matches]) => ({ month, count: matches.length }))
    });

    return sortedGroups;
  }, [filteredMatches]);

  return (
    <>
      <div className="space-y-4">
        <div className="flex justify-between items-center">
          <h1 className="text-2xl font-bold">{t('matches.title')}</h1>
          <div>
          <div className="flex gap-2">
            {selectedMatchIds.length > 0 && (
                <Button variant="destructive" onClick={handleBulkDeleteClick} disabled={isProcessing || selectedMatchIds.length === 0}>
                    {isProcessing ? <Loader2 className="mr-2 h-4 w-4 animate-spin"/> : <Trash2 className="mr-2 h-4 w-4" />}
                    {t('matches.deleteSelected', 'Delete ({{count}}) Selected', { count: selectedMatchIds.length })}
                </Button>
            )}
            <Button onClick={handleAddMatch} className="bg-soccer-primary hover:bg-soccer-primary/90" disabled={loading}>
              <Plus className="mr-2 h-4 w-4" /> {t('matches.addMatch')}
            </Button>
          </div>
          </div>
        </div>
        {/* Advanced Filtering */}
        <MatchFiltersComponent
          filters={filters}
          onFiltersChange={setFilters}
          players={players}
          onReset={() => setFilters({
            search: "",
            dateRange: { from: undefined, to: undefined },
            players: [],
            resultFilter: 'all',
            sortBy: 'date-desc'
          })}
        />

        {/* Match List */}
        {loading ? (
          <div className="flex items-center justify-center py-12" aria-live="polite" aria-busy="true">
            <Loader2 className="h-8 w-8 animate-spin text-primary" />
            <span className="sr-only">{t('matches.loadingMatches', 'Loading matches...')}</span>
          </div>
        ) : filteredMatches.length === 0 ? (
          filters.search || filters.dateRange.from || filters.dateRange.to || filters.players.length > 0 || filters.resultFilter !== 'all' ? (
            <EmptyState
              icon={Filter}
              title={t('matches.noMatchesMatchFilters', 'No Matches Match Your Filters')}
              description={t('matches.adjustFilters', 'Try adjusting your filter criteria to see more results.')}
              actionLabel={t('matches.clearFilters', 'Clear Filters')}
              actionIcon={X}
              onAction={() => setFilters({
                search: "",
                dateRange: { from: undefined, to: undefined },
                players: [],
                resultFilter: 'all',
                sortBy: 'date-desc'
              })}
              className="bg-card border-border py-8"
            />
          ) : (
            <EmptyState
              icon={CalendarPlus}
              title={t('matches.noMatchesRecorded', 'No Matches Recorded Yet')}
              description={t('matches.noMatchesDescription')}
              actionLabel={t('matches.addFirstMatch')}
              actionIcon={Plus}
              onAction={handleAddMatch}
              className="bg-card border-border py-12"
            />
          )
        ) : (
          <div className="space-y-4">
            {groupedMatches.map(([monthYear, monthMatches]) => (
            <div key={monthYear}>
              <h2 className="text-base font-semibold mb-2">{t(`common.months.${monthYear.split(' ')[0].toLowerCase()}`, monthYear.split(' ')[0])} {monthYear.split(' ')[1]}</h2>
              <Accordion type="single" collapsible className="space-y-2">
                {monthMatches.map((match) => (
                  <AccordionItem
                    key={match.id}
                    value={match.id.toString()}
                    className="border rounded-lg shadow-sm"
                  >
                    <AccordionTrigger className="px-4 hover:no-underline [&[data-state=open]>div]:bg-muted">
                      <div className="flex items-center justify-between w-full py-1">
                        <div className="flex items-center gap-3">
                          <Calendar className="h-4 w-4 text-muted-foreground" />
                          <span>{format(new Date(match.date), 'd MMM yyyy').replace(/^(\d+) ([A-Za-z]+)/, (_, day, month) => `${day} ${t(`common.months.${month.toLowerCase()}`, month)}`)}</span>
                        </div>
                        <div className="flex items-center gap-3">
                          {(match.scorea !== null && match.scoreb !== null) && (
                            <>
                              <span className="text-xl font-semibold">
                                {match.scorea} - {match.scoreb}
                              </span>
                              <Badge
                                variant={match.winner === 'Draw' ? 'secondary' : 'default'}
                                className="ml-1"
                              >
                                {match.winner === 'A' ? t('matches.teamAWon') :
                                 match.winner === 'B' ? t('matches.teamBWon') : t('matches.draw')}
                              </Badge>
                            </>
                          )}
                          {!(match.scorea !== null && match.scoreb !== null) && match.winner && (
                            <Badge variant={match.winner === 'Draw' ? 'secondary' : 'default'}>
                              {match.winner === 'A' ? t('matches.teamAWon') :
                               match.winner === 'B' ? t('matches.teamBWon') : t('matches.draw')}
                            </Badge>
                          )}
                        </div>
                      </div>
                    </AccordionTrigger>
                    <AccordionContent className="p-3 pt-1">
                      <div className="grid md:grid-cols-2 gap-3">
                        {/* Team A */}
                        <div className={`${match.winner === 'A' ? 'bg-green-50 dark:bg-green-900/30 p-2 rounded-md' : match.winner === 'B' ? 'bg-red-50 dark:bg-red-900/30 p-2 rounded-md' : ''}`}>
                          <h3 className="font-medium mb-1 flex items-center gap-1 text-sm">
                            <Users className={`h-3.5 w-3.5 ${match.winner === 'A' ? 'text-green-600 dark:text-green-400' : match.winner === 'B' ? 'text-red-600 dark:text-red-400' : ''}`} />
                            {t('matches.teamA')}
                            {match.winner === 'A' && <span className="text-xs text-green-600 dark:text-green-400 ml-1">(Winner)</span>}
                          </h3>
                          <ul className="space-y-0.5">
                            {match.teama?.map((playerId) => {
                              const goals = match.goalscorers?.filter(
                                g => g.team === 'A' && g.playerId === playerId
                              ).length || 0;
                              return (
                                <li key={playerId} className="text-xs flex items-center gap-1">
                                  <span>{getPlayerName(playerId)}</span>
                                  {goals > 0 && (
                                    <span className="text-xs bg-yellow-100 text-yellow-800 px-1 py-0.5 rounded-full">
                                      {goals} {goals === 1 ? t('matches.goal', 'goal') : t('matches.goals', 'goals')}
                                    </span>
                                  )}
                                </li>
                              );
                            })}
                          </ul>
                        </div>
                        {/* Team B */}
                        <div className={`${match.winner === 'B' ? 'bg-green-50 dark:bg-green-900/30 p-2 rounded-md' : match.winner === 'A' ? 'bg-red-50 dark:bg-red-900/30 p-2 rounded-md' : ''}`}>
                          <h3 className="font-medium mb-1 flex items-center gap-1 text-sm">
                            <Users className={`h-3.5 w-3.5 ${match.winner === 'B' ? 'text-green-600 dark:text-green-400' : match.winner === 'A' ? 'text-red-600 dark:text-red-400' : ''}`} />
                            {t('matches.teamB')}
                            {match.winner === 'B' && <span className="text-xs text-green-600 dark:text-green-400 ml-1">(Winner)</span>}
                          </h3>
                          <ul className="space-y-0.5">
                            {match.teamb?.map((playerId) => {
                              const goals = match.goalscorers?.filter(
                                g => g.team === 'B' && g.playerId === playerId
                              ).length || 0;
                              return (
                                <li key={playerId} className="text-xs flex items-center gap-1">
                                  <span>{getPlayerName(playerId)}</span>
                                  {goals > 0 && (
                                    <span className="text-xs bg-yellow-100 text-yellow-800 px-1 py-0.5 rounded-full">
                                      {goals} {goals === 1 ? t('matches.goal', 'goal') : t('matches.goals', 'goals')}
                                    </span>
                                  )}
                                </li>
                              );
                            })}
                          </ul>
                        </div>
                      </div>

                      {/* Add action buttons */}
                      <div className="flex justify-end gap-2 mt-2 pt-2 border-t">
                        <Button
                          variant="outline"
                          size="sm"
                          className="h-7 px-2 text-xs"
                          onClick={(e) => {
                            e.stopPropagation();
                            handleEditMatch(match);
                          }}
                        >
                          <Edit className="h-3.5 w-3.5 mr-1" />
                          {t('common.edit')}
                        </Button>
                        <Button
                          variant="destructive"
                          size="sm"
                          className="h-7 px-2 text-xs"
                          onClick={(e) => {
                            e.stopPropagation();
                            handleDeleteClick(match);
                          }}
                        >
                          <Trash2 className="h-3.5 w-3.5 mr-1" />
                          {t('common.delete')}
                        </Button>
                      </div>

                      {match.youtubelink && (
                        <div className="mt-2 pt-2 border-t">
                          <a
                            href={match.youtubelink}
                            target="_blank"
                            rel="noopener noreferrer"
                            className="flex items-center gap-1 text-xs text-red-600 hover:text-red-700"
                          >
                            <Youtube className="h-3.5 w-3.5" />
                            {t('matches.watchHighlights', 'Watch Highlights')}
                          </a>
                        </div>
                      )}

                      {/* Match Comments */}
                      <div className="mt-3">
                        <MatchComments matchId={match.id} players={players} />
                      </div>
                    </AccordionContent>
                  </AccordionItem>
                ))}
              </Accordion>
            </div>
          ))}
        </div>
        )}
      </div>

      {/* Add/Edit Match Modal */}
       <Dialog open={isAddModalOpen} onOpenChange={setIsAddModalOpen}>
           <DialogContent className="sm:max-w-[650px]">
             <DialogHeader><DialogTitle>{selectedMatch ? t('matches.editMatch') : t('matches.addMatch')}</DialogTitle></DialogHeader>
             <form onSubmit={handleFormSubmit}>
               <div className="grid gap-6 py-4 pr-6 overflow-y-auto" style={{ maxHeight: 'calc(80vh - 150px)' }}>
                    {/* Add date input field */}
                    <div className="grid grid-cols-4 items-center gap-4">
                      <Label htmlFor="matchDate" className="text-right">{t('matches.date')}</Label>
                      <Input
                        id="matchDate"
                        type="date"
                        value={formData.date}
                        onChange={(e) => setFormData({ ...formData, date: e.target.value })}
                        className="col-span-3"
                        required
                        disabled={isProcessing}
                      />
                    </div>

                    {/* Add search input before the team selection */}
                    <div className="relative w-full">
                      <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                      <Input
                        placeholder={t('matches.searchPlayers', 'Search players (e.g., Player1, Player2)')}
                        value={playerSearch}
                        onChange={(e) => setPlayerSearch(e.target.value)}
                        className="pl-8"
                      />
                    </div>
                    <div className="grid grid-cols-2 gap-6">
                        {['teamA', 'teamB'].map((teamKey) => (
                          <div key={teamKey} className="space-y-2">
                            <Label className="font-semibold">{teamKey === 'teamA' ? t('matches.teamA') : t('matches.teamB')}</Label>
                            <div className="space-y-2 max-h-[200px] overflow-y-auto border rounded-md p-3">
                              {filteredModalPlayers.map(player => (
                                <div key={`${teamKey}-${player.id}`} className="flex items-center space-x-2">
                                  <Checkbox
                                    id={`${teamKey}-${player.id}`}
                                    checked={formData[teamKey as 'teamA' | 'teamB'].includes(player.id)}
                                    onCheckedChange={() => handleTogglePlayerSelection(teamKey as 'teamA' | 'teamB', player.id)}
                                    disabled={isProcessing}
                                  />
                                  <label
                                    htmlFor={`${teamKey}-${player.id}`}
                                    className={`text-sm font-medium leading-none flex-1 ${isProcessing ? 'cursor-not-allowed opacity-70' : 'peer-disabled:cursor-not-allowed peer-disabled:opacity-70'}`}
                                  >
                                    {player.name}
                                    <span className="ml-2 text-xs text-muted-foreground">
                                      ({player.gamesPlayed} {player.gamesPlayed === 1 ? t('matches.game', 'game') : t('matches.games', 'games')})
                                    </span>
                                  </label>
                                </div>
                              ))}
                            </div>
                          </div>
                        ))}
                    </div>
                    <div className="border p-4 rounded-md space-y-4">
                        <div className="flex items-center justify-between mb-4">
                          <p className="text-sm font-medium">{t('matches.result')}</p>
                          <div className="flex items-center gap-2">
                            <Checkbox
                              id="autoScore"
                              checked={formData.autoScore}
                              onCheckedChange={(checked) => {
                                if (checked) {
                                  // When enabling auto-score, calculate from goalscorers
                                  const teamAScore = formData.goalscorers?.filter(g => g.team === 'A').length || 0;
                                  const teamBScore = formData.goalscorers?.filter(g => g.team === 'B').length || 0;
                                  setFormData({
                                    ...formData,
                                    autoScore: true,
                                    scoreA: teamAScore,
                                    scoreB: teamBScore,
                                    winner: undefined
                                  });
                                } else {
                                  // When disabling, keep current scores but allow manual editing
                                  setFormData({
                                    ...formData,
                                    autoScore: false
                                  });
                                }
                              }}
                            />
                            <Label htmlFor="autoScore" className="text-sm">
                              {t('matches.autoCalculate', 'Auto-calculate from goalscorers')}
                            </Label>
                          </div>
                        </div>

                        {/* Score section */}
                        <div className="space-y-4">
                          <div className="grid grid-cols-2 gap-4 items-end">
                              <div>
                                <Label htmlFor="scoreA">{t('matches.teamAScore', 'Team A Score (Optional)')}</Label>
                                {formData.autoScore ? (
                                  <div className="mt-1 p-2 bg-muted rounded-md text-center font-medium">
                                    {formData.scoreA || 0}
                                  </div>
                                ) : (
                                  <Input
                                    id="scoreA"
                                    type="number"
                                    min={0}
                                    placeholder={t('matches.leaveEmpty', 'Leave empty for no score')}
                                    value={formData.scoreA}
                                    onChange={(e) => setFormData({
                                      ...formData,
                                      scoreA: e.target.value,
                                    })}
                                    className="mt-1"
                                    disabled={isProcessing}
                                  />
                                )}
                              </div>
                              <div>
                                <Label htmlFor="scoreB">{t('matches.teamBScore', 'Team B Score (Optional)')}</Label>
                                {formData.autoScore ? (
                                  <div className="mt-1 p-2 bg-muted rounded-md text-center font-medium">
                                    {formData.scoreB || 0}
                                  </div>
                                ) : (
                                  <Input
                                    id="scoreB"
                                    type="number"
                                    min={0}
                                    placeholder={t('matches.leaveEmpty', 'Leave empty for no score')}
                                    value={formData.scoreB}
                                    onChange={(e) => setFormData({
                                      ...formData,
                                      scoreB: e.target.value,
                                    })}
                                    className="mt-1"
                                    disabled={isProcessing}
                                  />
                                )}
                              </div>
                          </div>

                          {/* Winner selection - always visible when not in auto-score mode */}
                          {!formData.autoScore && (
                            <div className="pt-4 border-t">
                              <Label className="mb-2 block">{t('matches.matchResult', 'Match Result')}:</Label>
                              <RadioGroup
                                value={formData.winner || ''}
                                onValueChange={(value) => setFormData({
                                  ...formData,
                                  winner: value as 'A' | 'B' | 'Draw',
                                })}
                                className="flex space-x-4"
                                disabled={isProcessing}
                              >
                                <div className="flex items-center space-x-2">
                                  <RadioGroupItem value="A" id="r1" disabled={isProcessing} />
                                  <Label htmlFor="r1">{t('matches.teamAWon')}</Label>
                                </div>
                                <div className="flex items-center space-x-2">
                                  <RadioGroupItem value="B" id="r2" disabled={isProcessing} />
                                  <Label htmlFor="r2">{t('matches.teamBWon')}</Label>
                                </div>
                                <div className="flex items-center space-x-2">
                                  <RadioGroupItem value="Draw" id="r3" disabled={isProcessing} />
                                  <Label htmlFor="r3">{t('matches.draw')}</Label>
                                </div>
                              </RadioGroup>
                            </div>
                          )}
                        </div>
                    </div>
                    <div className="border p-4 rounded-md space-y-3">
                        <Label className="font-medium">{t('matches.goalscorers', 'Goalscorers (Optional)')}</Label>
                        <div className="grid grid-cols-2 gap-6">
                            {(['A', 'B'] as const).map(team => { const teamKey = team === 'A' ? 'teamA' : 'teamB'; const teamPlayers = players.filter(p => formData[teamKey].includes(p.id)); return (<div key={team} className="space-y-2"><p className="text-sm font-semibold">{team === 'A' ? t('matches.teamA') : t('matches.teamB')}</p>{teamPlayers.length === 0 && <p className="text-xs text-muted-foreground">{t('matches.selectPlayersFirst', 'Select players first')}</p>}{teamPlayers.map(player => { const count = getGoalscorerCount(team, player.id); return (<div key={`goal-${team}-${player.id}`} className="flex items-center justify-between"><Label htmlFor={`goal-${team}-${player.id}`} className="text-sm">{player.name}</Label><div className="flex items-center gap-2"><Button type="button" variant="outline" size="sm" className="h-6 w-6 p-0 disabled:opacity-50" onClick={() => handleGoalscorerChange(team, player.id, false)} disabled={count === 0 || isProcessing}> - </Button><span className="text-sm w-4 text-center">{count}</span><Button type="button" variant="outline" size="sm" className="h-6 w-6 p-0" onClick={() => handleGoalscorerChange(team, player.id, true)} disabled={isProcessing}> + </Button></div></div>)})}</div>)})}
                        </div>
                    </div>
                    <div className="grid grid-cols-4 items-center gap-4">
                        <Label htmlFor="youtubeLink" className="text-right">{t('matches.youtubeLink')}</Label>
                        <Input id="youtubeLink" type="url" placeholder={t('matches.youtubeLinkPlaceholder', 'Optional (e.g., https://youtu.be/...)')} value={formData.youtubeLink} onChange={(e) => setFormData({ ...formData, youtubeLink: e.target.value })} className="col-span-3" disabled={isProcessing}/>
                    </div>
               </div>
               <DialogFooter className="pt-4 border-t">
                 <Button type="button" variant="outline" onClick={() => setIsAddModalOpen(false)} disabled={isProcessing}>{t('common.cancel')}</Button>
                 <Button type="submit" className="bg-soccer-primary hover:bg-soccer-primary/90" disabled={isProcessing || !isFormValid}>
                     {isProcessing && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                     {selectedMatch ? t('matches.saveChanges', 'Save Changes') : t('matches.addMatch')}
                 </Button>
               </DialogFooter>
             </form>
           </DialogContent>
       </Dialog>

      {/* Single Delete Confirmation Modal */}
      <ConfirmationModal isOpen={isDeleteModalOpen} onClose={() => setIsDeleteModalOpen(false)} onConfirm={handleDeleteConfirm} title={t('matches.deleteMatch')} message={t('matches.deleteMatchConfirm', 'Are you sure you want to delete Match #{{id}}? This action cannot be undone.', { id: selectedMatch?.id })} confirmText={isProcessing ? t('common.deleting') : t('common.delete')} isDestructive={true} isProcessing={isProcessing} />
      {/* Bulk Delete Confirmation Modal */}
       <ConfirmationModal isOpen={isBulkDeleteModalOpen} onClose={() => setIsBulkDeleteModalOpen(false)} onConfirm={handleBulkDeleteConfirm} title={t('matches.deleteSelectedMatches')} message={t('matches.deleteSelectedMatchesConfirm', 'Are you sure you want to delete {{count}} selected match(es)? This action cannot be undone.', { count: selectedMatchIds.length })} confirmText={isProcessing ? t('common.deleting') : t('matches.deleteMatches', 'Delete {{count}} Matches', { count: selectedMatchIds.length })} isDestructive={true} isProcessing={isProcessing} />
    </>
  );
};

export default MatchesPage;

