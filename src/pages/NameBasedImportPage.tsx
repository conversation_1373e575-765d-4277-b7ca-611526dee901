import React from 'react';
import { useNavigate, Link } from 'react-router-dom';
import { useTheme } from '@/context/ThemeContext';
import { useAuth } from '@/context/AuthContext';
import ThemeToggle from '@/components/ThemeToggle';
import LogoComponent from '@/components/LogoComponent';
import { UserCircle } from 'lucide-react';
import NameBasedImport from '@/components/groups/NameBasedImport';

const NameBasedImportPage = () => {
  const { theme } = useTheme();
  const navigate = useNavigate();
  const { user } = useAuth();

  // Redirect to login if not authenticated
  React.useEffect(() => {
    if (!user) {
      navigate('/');
    }
  }, [user, navigate]);

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-soccer-dark transition-colors duration-200">
      {/* Header with profile link */}
      <header className="bg-soccer-primary text-white dark:bg-soccer-dark py-4 transition-colors duration-200">
        <div className="container mx-auto flex justify-between items-center">
          <div className="flex items-center gap-2">
            <LogoComponent to="/dashboard" />
          </div>
          <div className="flex items-center gap-4">
            <ThemeToggle />
            <Link to="/profile" className="flex items-center gap-2 text-white hover:text-white/90">
              <UserCircle className="h-5 w-5" />
              Profile
            </Link>
          </div>
        </div>
      </header>

      <div className="container mx-auto py-6">
        <div className="flex justify-between items-center mb-6">
          <h2 className="text-2xl font-bold dark:text-white">Name-Based Import</h2>
          <button
            onClick={() => navigate('/groups')}
            className="text-soccer-primary hover:underline"
          >
            Back to Groups
          </button>
        </div>

        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-8 transition-colors duration-200">
          <div className="max-w-md mx-auto">
            <p className="text-gray-600 dark:text-gray-300 mb-6">
              This special import method uses player names instead of indices for more reliable team references.
              Use this with the converted name-based JSON file.
            </p>
            <NameBasedImport />
          </div>
        </div>
      </div>
    </div>
  );
};

export default NameBasedImportPage;
