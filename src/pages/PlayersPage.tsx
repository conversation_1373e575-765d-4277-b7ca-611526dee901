import { useState, useEffect, useMemo } from "react";
import { useTranslation } from "react-i18next";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import UnifiedLayout from "@/components/layout/UnifiedLayout";
import ConfirmationModal from "@/components/ConfirmationModal";
import { PlayerAvatar } from "@/components/players/PlayerAvatar";
import { EmptyState } from "@/components/ui/empty-state";
import { CompactMobileTable } from "@/components/ui/compact-mobile-table";
import { MobileTable } from "@/components/ui/mobile-table";
import { useMediaQuery } from "@/hooks/use-media-query";

import {
  Plus,
  Edit,
  Trash2,
  Footprints,
  BrainCog,
  TrendingUp,
  Star,
  Loader2,
  User,
  Dumbbell,
  Heart,
  Users,
  Save,
  X,
  UserPlus,
  Info
} from "lucide-react";
import {
  Dialog,
  DialogContent,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";
import { Badge } from "@/components/ui/badge";
import { PostgrestError } from '@supabase/supabase-js'
import { supabase } from "@/lib/supabase";
import { useToast } from "@/hooks/use-toast";
import { useEnhancedToast } from "@/hooks/use-enhanced-toast";
import { useAuth } from "@/context/AuthContext";
import { useGroup } from "@/lib/context/GroupContext";
import { calculatePlayerRating } from "@/utils/playerRating";

// Enhanced components
import { EnhancedCard } from "@/components/ui/enhanced-card";
import { ResponsiveTable } from "@/components/ui/responsive-table";
import { EnhancedInput } from "@/components/ui/enhanced-input";
import { AnimatedButton } from "@/components/ui/animated-button";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";

// Player interface (matching Supabase table structure)
interface Player {
  id: number;
  created_at: string;
  name: string;
  skills: number;
  effort: number;
  stamina: number;
  group_id?: string; // Added group_id field
  rating?: number; // Calculated client-side
  avatar_url?: string | null; // Profile picture URL
}

// Default form state
const defaultFormData = { name: "", skills: 75, effort: 75, stamina: 75 };

const PlayersPage = () => {
  const { t } = useTranslation();
  const [players, setPlayers] = useState<Player[]>([]);
  const [loading, setLoading] = useState(true);
  const [isProcessing, setIsProcessing] = useState(false);
  const isMobile = useMediaQuery("(max-width: 768px)");

  const [isAddModalOpen, setIsAddModalOpen] = useState(false);
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  const [selectedPlayer, setSelectedPlayer] = useState<Player | null>(null);
  const [formData, setFormData] = useState(defaultFormData);
  const { toast } = useToast(); // Initialize toast

  const selectedGroupId = localStorage.getItem('selectedGroupId');

  // Fetch players on mount
  useEffect(() => {
    const fetchPlayersData = async () => {
      setLoading(true);
      try {
        const { data, error } = await supabase
          .from('players')
          .select('*')
          .eq('group_id', selectedGroupId)
          .order('name');

        if (error) throw error;
        setPlayers(data || []);
      } catch (error) {
        console.error('Error fetching players:', error);
        toast({
          title: t('errors.error'),
          description: t('players.fetchError', 'Failed to fetch players.'),
          variant: 'destructive',
        });
      } finally {
        setLoading(false);
      }
    };

    fetchPlayersData();
  }, [toast]);

  const handleAddPlayer = () => {
    setFormData(defaultFormData);
    setSelectedPlayer(null);
    setIsAddModalOpen(true);
  };

  const handleEditPlayer = (player: Player) => {
    setFormData({
      name: player.name,
      skills: Number(player.skills),
      effort: Number(player.effort),
      stamina: Number(player.stamina)
    });
    setSelectedPlayer(player);
    setIsAddModalOpen(true);
  };

  const handleDeleteClick = (player: Player) => {
    setSelectedPlayer(player);
    setIsDeleteModalOpen(true);
  };

  const handleDeleteConfirm = async () => {
    if (!selectedPlayer) return;

    setIsProcessing(true);
    try {
      console.log('Attempting to delete player:', selectedPlayer);
      const { error, data } = await supabase
        .from('players')
        .delete()
        .eq('id', selectedPlayer.id)
        .select(); // Add select to see what was deleted

      if (error) {
        console.error('Supabase delete error:', error);
        throw error;
      }

      console.log('Delete response:', data);

      if (!data || data.length === 0) {
        throw new Error('No player was deleted');
      }

      setPlayers(prev => prev.filter(p => p.id !== selectedPlayer.id));
      toast({
        title: t('players.deleteSuccess', 'Player deleted successfully!'),
        description: t('players.playerRemoved', '{{name}} has been removed.', { name: selectedPlayer.name })
      });
      setSelectedPlayer(null);
      setIsDeleteModalOpen(false);
    } catch (error) {
      const e = error as PostgrestError;
      console.error('Error deleting player:', e);
      toast({
        title: t('players.deleteError', 'Error deleting player'),
        description: e.message,
        variant: "destructive",
      });
    } finally {
      setIsProcessing(false);
    }
  };

  const handleFormSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!formData.name) {
      toast({ title: t('errors.validationError', 'Validation Error'), description: t('players.nameRequired', 'Player name cannot be empty.'), variant: "destructive" });
      return;
    }
    setIsProcessing(true);

    // Make sure we have a selected group
    if (!selectedGroupId) {
      toast({ title: t('errors.error'), description: t('groups.noGroupSelected', 'No group selected. Please select a group first.'), variant: "destructive" });
      setIsProcessing(false);
      return;
    }

    const playerData = {
      name: formData.name.trim(),
      skills: Math.max(1, Math.min(100, formData.skills)),
      effort: Math.max(1, Math.min(100, formData.effort)),
      stamina: Math.max(1, Math.min(100, formData.stamina)),
      group_id: selectedGroupId,
    };

    try {
      if (selectedPlayer) {
        console.log('Starting player update:', {
          playerId: selectedPlayer.id,
          updateData: playerData
        });

        // Optimistic update
        const optimisticPlayer = {
          ...selectedPlayer,
          ...playerData,
          rating: calculatePlayerRating(playerData),
        };
        setPlayers(prev =>
          prev.map(p => p.id === selectedPlayer.id ? optimisticPlayer : p)
            .sort((a, b) => a.name.localeCompare(b.name))
        );

        // Perform the update
        const { data: updateData, error: updateError } = await supabase
          .from('players')
          .update({
            name: playerData.name,
            skills: Math.round(Number(playerData.skills)),
            effort: Math.round(Number(playerData.effort)),
            stamina: Math.round(Number(playerData.stamina)),
            group_id: playerData.group_id
          })
          .eq('id', selectedPlayer.id)
          .select();

        console.log('Update response:', { updateData, updateError });

        if (updateError) {
          console.error('Update error details:', updateError);
          throw updateError;
        }

        if (!updateData || updateData.length === 0) {
          console.error('No data returned from update');
          throw new Error('No data returned from update');
        }

        // Fetch fresh data to ensure consistency
        const { data: freshData, error: fetchError } = await supabase
          .from('players')
          .select('*')
          .eq('id', selectedPlayer.id)
          .single();

        console.log('Fresh data fetch:', { freshData, fetchError });

        if (fetchError) {
          console.error('Fetch error details:', fetchError);
          throw fetchError;
        }

        if (freshData) {
          const playerWithRating = {
            ...freshData,
            rating: calculatePlayerRating(freshData)
          };

          setPlayers(prev =>
            prev.map(p => p.id === freshData.id ? playerWithRating : p)
              .sort((a, b) => a.name.localeCompare(b.name))
          );

          toast({ title: t('players.updateSuccess', 'Player updated successfully!') });
        } else {
          console.error('No fresh data returned');
          throw new Error('No fresh data returned');
        }
      } else {
        const { data, error } = await supabase
          .from('players')
          .insert(playerData)
          .select();

        if (error) throw error;
        if (data && data.length > 0) {
          const playerWithRating = {
            ...data[0],
            rating: calculatePlayerRating(data[0])
          };
          setPlayers(prev =>
            [...prev, playerWithRating].sort((a, b) =>
              a.name.localeCompare(b.name)
            )
          );
          toast({ title: t('players.addSuccess', 'Player added successfully!') });
        }
      }
      setFormData(defaultFormData);
      setSelectedPlayer(null);
      setIsAddModalOpen(false);
    } catch (error) {
      const e = error as PostgrestError;
      console.error('Error saving player:', e);
      toast({
        title: selectedPlayer ? t('players.updateError', 'Error updating player') : t('players.addError', 'Error adding player'),
        description: e.message,
        variant: "destructive",
      });
      // Revert optimistic update if it was an update operation
      if (selectedPlayer) {
        setPlayers(prev =>
          prev.map(p => p.id === selectedPlayer.id ? selectedPlayer : p)
            .sort((a, b) => a.name.localeCompare(b.name))
        );
      }
    } finally {
      setIsProcessing(false);
    }
  };

  return (
    <>
      <TooltipProvider>
        <div className="space-y-4">
          <div className="flex justify-between items-center">
            <h1 className="text-2xl font-bold">{t('players.title')}</h1>
            <div>
            <Button
              onClick={handleAddPlayer}
              className="bg-soccer-primary hover:bg-soccer-primary/90"
              disabled={loading} // Disable while loading
            >
              <Plus className="mr-2 h-4 w-4" /> {t('players.addPlayer')}
            </Button>
            </div>
          </div>
          {isMobile ? (
            <MobileTable
              data={players}
              columns={[
                {
                  header: t('players.name'),
                  accessorKey: 'name' as keyof Player,
                  priority: "high",
                  cell: (player: Player) => (
                    <div className="flex items-center gap-1">
                      <PlayerAvatar
                        playerId={player.id}
                        playerName={player.name}
                        avatarUrl={player.avatar_url}
                        size="xs"
                        editable={false}
                      />
                      <span className="font-medium text-xs truncate max-w-[80px]">{player.name}</span>
                    </div>
                  ),
                  meta: { width: "40%" }
                },
                {
                  header: t('players.skills', 'S'),
                  accessorKey: 'skills' as keyof Player,
                  priority: "high",
                  cell: (player: Player) => (
                    <div className="flex items-center justify-center">
                      <Footprints className="h-3 w-3 text-blue-500 mr-1" />
                      <span>{player.skills}</span>
                    </div>
                  ),
                  meta: { align: "center", width: "20%" }
                },
                {
                  header: t('players.effort', 'E'),
                  accessorKey: 'effort' as keyof Player,
                  priority: "high",
                  cell: (player: Player) => (
                    <div className="flex items-center justify-center">
                      <BrainCog className="h-3 w-3 text-green-500 mr-1" />
                      <span>{player.effort}</span>
                    </div>
                  ),
                  meta: { align: "center", width: "20%" }
                },
                {
                  header: t('players.stamina', 'S'),
                  accessorKey: 'stamina' as keyof Player,
                  priority: "high",
                  cell: (player: Player) => (
                    <div className="flex items-center justify-center">
                      <TrendingUp className="h-3 w-3 text-red-500 mr-1" />
                      <span>{player.stamina}</span>
                    </div>
                  ),
                  meta: { align: "center", width: "20%" }
                },
                {
                  header: t('players.rating'),
                  accessorKey: 'rating' as keyof Player,
                  priority: "high",
                  cell: (player: Player) => (
                    <div className="flex items-center justify-center">
                      <div className="bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-100 rounded-full px-2 py-0.5 text-xs font-medium">
                        {player.rating ?? calculatePlayerRating(player)}
                      </div>
                    </div>
                  ),
                  meta: { align: "center", width: "20%" }
                },
              ]}
              keyExtractor={(player) => player.id}
              onRowClick={(player) => handleEditPlayer(player)}
              emptyState={
                <EmptyState
                  icon={UserPlus}
                  title={t('dashboard.noPlayersAdded')}
                  description={t('players.noPlayersDescription', 'Add players to start tracking their performance and statistics.')}
                  actionLabel={t('dashboard.addFirstPlayer')}
                  actionIcon={Plus}
                  onAction={handleAddPlayer}
                  className="border-0 py-8"
                />
              }
              isLoading={loading}
            />
          ) : (
            <div className="rounded-md border">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead className="w-[50px] text-center">ID</TableHead>
                    <TableHead className="w-[200px]">{t('players.name')}</TableHead>
                    <TableHead className="w-[100px] text-center">
                      <Tooltip>
                        <TooltipTrigger className="w-full flex items-center justify-center gap-1">
                          <Footprints className="h-4 w-4" /> {t('players.skills', 'Skills')}
                        </TooltipTrigger>
                        <TooltipContent>{t('players.skills', 'Skills')}</TooltipContent>
                      </Tooltip>
                    </TableHead>
                    <TableHead className="w-[100px] text-center">
                       <Tooltip>
                         <TooltipTrigger className="w-full flex items-center justify-center gap-1">
                           <BrainCog className="h-4 w-4" /> {t('players.effort', 'Effort')}
                         </TooltipTrigger>
                         <TooltipContent>{t('players.effort', 'Effort')}</TooltipContent>
                       </Tooltip>
                    </TableHead>
                    <TableHead className="w-[100px] text-center">
                       <Tooltip>
                         <TooltipTrigger className="w-full flex items-center justify-center gap-1">
                           <TrendingUp className="h-4 w-4" /> {t('players.stamina', 'Stamina')}
                         </TooltipTrigger>
                         <TooltipContent>{t('players.stamina', 'Stamina')}</TooltipContent>
                       </Tooltip>
                    </TableHead>
                    <TableHead className="w-[100px] text-center">
                       <Tooltip>
                         <TooltipTrigger className="w-full flex items-center justify-center gap-1">
                           <Star className="h-4 w-4" /> {t('players.rating')}
                         </TooltipTrigger>
                         <TooltipContent>{t('players.averageRating', 'Average Rating')}</TooltipContent>
                       </Tooltip>
                    </TableHead>
                    <TableHead className="w-[120px] text-right">{t('common.actions')}</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {loading ? (
                      <TableRow>
                          <TableCell colSpan={7} className="h-24 text-center">
                              <Loader2 className="h-6 w-6 animate-spin inline-block mr-2" aria-hidden="true" />
                              <span>{t('common.loading')}</span>
                              <span className="sr-only">{t('players.loadingPlayers', 'Loading player data')}</span>
                          </TableCell>
                      </TableRow>
                  ) : players.length === 0 ? (
                      <TableRow>
                          <TableCell colSpan={7} className="h-24 text-center p-0">
                              <EmptyState
                                  icon={UserPlus}
                                  title={t('dashboard.noPlayersAdded')}
                                  description={t('players.noPlayersDescription', 'Add players to start tracking their performance and statistics.')}
                                  actionLabel={t('dashboard.addFirstPlayer')}
                                  actionIcon={Plus}
                                  onAction={handleAddPlayer}
                                  className="border-0 py-8"
                              />
                          </TableCell>
                      </TableRow>
                  ) : (
                      players.map((player) => (
                          <TableRow key={player.id}>
                            <TableCell className="w-[50px]">
                              <div className="text-center w-full">{player.id}</div>
                            </TableCell>
                            <TableCell className="w-[200px] font-medium">
                              <div className="flex items-center gap-2">
                                <div className="relative">
                                  <PlayerAvatar
                                    playerId={player.id}
                                    playerName={player.name}
                                    avatarUrl={player.avatar_url}
                                    size="sm"
                                    editable={false}
                                  />
                                </div>
                                <span>{player.name}</span>
                              </div>
                            </TableCell>
                            <TableCell className="w-[100px]">
                              <div className="text-center w-full">{player.skills}</div>
                            </TableCell>
                            <TableCell className="w-[100px]">
                              <div className="text-center w-full">{player.effort}</div>
                            </TableCell>
                            <TableCell className="w-[100px]">
                              <div className="text-center w-full">{player.stamina}</div>
                            </TableCell>
                            <TableCell className="w-[100px]">
                              <div className="text-center w-full">
                                <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                  {player.rating ?? calculatePlayerRating(player)} {/* Use stored or calculate */}
                                </span>
                              </div>
                            </TableCell>
                            <TableCell className="w-[120px] text-right">
                              <div className="flex justify-end gap-2">
                                <Button variant="outline" size="sm" onClick={() => handleEditPlayer(player)} disabled={isProcessing}>
                                  <Edit className="h-4 w-4" /> <span className="sr-only">{t('common.edit')}</span>
                                </Button>
                                <Button variant="destructive" size="sm" onClick={() => handleDeleteClick(player)} disabled={isProcessing}>
                                  <Trash2 className="h-4 w-4" /> <span className="sr-only">{t('common.delete')}</span>
                                </Button>
                              </div>
                            </TableCell>
                          </TableRow>
                        ))
                  )}
                </TableBody>
              </Table>
            </div>
          )}
        </div>
      </TooltipProvider>

      {/* Add/Edit Player Modal */}
      <Dialog open={isAddModalOpen} onOpenChange={setIsAddModalOpen}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>{selectedPlayer ? t('players.editPlayer') : t('players.addPlayer')}</DialogTitle>
          </DialogHeader>
          <div className="flex justify-center py-2">
            <div className="relative">
              <PlayerAvatar
                playerId={selectedPlayer?.id || 0}
                playerName={formData.name || "New Player"}
                avatarUrl={selectedPlayer?.avatar_url}
                size="lg"
                editable={!!selectedPlayer}
                onAvatarChange={(url) => {
                  if (selectedPlayer) {
                    // Update the player in the list with the new avatar URL
                    setPlayers(prev =>
                      prev.map(p => p.id === selectedPlayer.id ? { ...p, avatar_url: url } : p)
                    );
                  }
                }}
              />
            </div>
          </div>


          <form onSubmit={handleFormSubmit}>
            <div className="grid gap-4 py-4">
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="name" className="text-right">{t('players.name')}</Label>
                <Input id="name" value={formData.name} onChange={(e) => setFormData({ ...formData, name: e.target.value })} className="col-span-3" required disabled={isProcessing} />
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="skills" className="text-right flex gap-1 items-center justify-end"><Footprints className="h-4 w-4" /> {t('players.skills', 'Skills')}</Label>
                <Input id="skills" type="number" min={1} max={100} value={formData.skills} onChange={(e) => setFormData({ ...formData, skills: Number(e.target.value) })} className="col-span-3" required disabled={isProcessing} />
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                 <Label htmlFor="effort" className="text-right flex gap-1 items-center justify-end"><BrainCog className="h-4 w-4" /> {t('players.effort', 'Effort')}</Label>
                <Input id="effort" type="number" min={1} max={100} value={formData.effort} onChange={(e) => setFormData({ ...formData, effort: Number(e.target.value) })} className="col-span-3" required disabled={isProcessing} />
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                 <Label htmlFor="stamina" className="text-right flex gap-1 items-center justify-end"><TrendingUp className="h-4 w-4" /> {t('players.stamina', 'Stamina')}</Label>
                <Input id="stamina" type="number" min={1} max={100} value={formData.stamina} onChange={(e) => setFormData({ ...formData, stamina: Number(e.target.value) })} className="col-span-3" required disabled={isProcessing} />
              </div>
            </div>
            <DialogFooter>
              <Button type="button" variant="outline" onClick={() => setIsAddModalOpen(false)} disabled={isProcessing}>{t('common.cancel')}</Button>
              <Button type="submit" className="bg-soccer-primary hover:bg-soccer-primary/90" disabled={isProcessing}>
                {isProcessing && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                {selectedPlayer ? t('common.saveChanges', 'Save Changes') : t('players.addPlayer')}
              </Button>
            </DialogFooter>
          </form>
        </DialogContent>
      </Dialog>

      {/* Delete Confirmation Modal */}
      <ConfirmationModal
        isOpen={isDeleteModalOpen}
        onClose={() => setIsDeleteModalOpen(false)}
        onConfirm={handleDeleteConfirm}
        title={t('players.deletePlayer')}
        message={t('players.deleteConfirmMessage', 'Are you sure you want to delete {{name}}? This action cannot be undone.', { name: selectedPlayer?.name })}
        confirmText={isProcessing ? t('players.deleting', 'Deleting...') : t('common.delete')}
        isDestructive={true}
        isProcessing={isProcessing} // Disable confirm while processing
      />
    </>
  );
};

export default PlayersPage;
