import { useState } from "react";
import { useNavigate } from "react-router-dom";
import { useTranslation } from "react-i18next";
import { useToast } from "@/hooks/use-toast";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { EnhancedCard } from "@/components/ui/enhanced-card";
import { PlayerAvatar } from "@/components/players/PlayerAvatar";
import { LogOut, User, KeyRound, MessageSquare, Coffee, Moon, Sun, Languages, Users, PlusCircle, Settings, Heart, Shield } from "lucide-react";
import { useAuth } from "@/context/AuthContext";
import { FEEDBACK_FORM_URL, openFeedbackForm } from "@/lib/services/feedbackService";
import ThemeToggle from "@/components/ThemeToggle";
import LanguageToggle from "@/components/LanguageToggle";
import { useTheme } from "@/context/ThemeContext";
import { useMediaQuery } from "@/hooks/use-media-query";
import { useGroup } from "@/lib/context/GroupContext";
import { useLanguage } from "@/context/LanguageContext";


const ProfilePage = () => {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const { toast } = useToast();
  const { logout, user } = useAuth();
  const { theme } = useTheme();
  const { language } = useLanguage();
  const isMobile = useMediaQuery("(max-width: 768px)");
  const [isChangingPassword, setIsChangingPassword] = useState(false);
  const [passwordData, setPasswordData] = useState({
    currentPassword: "",
    newPassword: "",
    confirmPassword: "",
  });

  const handleLogout = async () => {
    try {
      await logout();
      navigate("/");
    } catch (error) {
      console.error("Failed to log out:", error);
    }
  };

  const handlePasswordChange = async (e: React.FormEvent) => {
    e.preventDefault();

    if (passwordData.newPassword !== passwordData.confirmPassword) {
      toast({
        title: t('errors.validationError'),
        description: t('profile.passwordsDoNotMatch', 'New passwords do not match'),
        variant: "destructive",
      });
      return;
    }

    try {
      // This is a placeholder - you'll need to implement the actual password change functionality
      // For now, we'll just show a success message
      toast({
        title: t('common.success'),
        description: t('profile.passwordChangeSuccess', 'Password changed successfully'),
      });
      setIsChangingPassword(false);
      setPasswordData({
        currentPassword: "",
        newPassword: "",
        confirmPassword: "",
      });
    } catch (error) {
      toast({
        title: t('errors.error'),
        description: error instanceof Error ? error.message : t('profile.passwordChangeError', 'Failed to change password'),
        variant: "destructive",
      });
    }
  };

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div className="flex items-center gap-3">
        <div className="p-2 bg-soccer-primary/10 rounded-lg">
          <User className="h-6 w-6 text-soccer-primary" />
        </div>
        <div>
          <h1 className="text-2xl md:text-3xl font-bold">{t('nav.profile')}</h1>
          <p className="text-muted-foreground">{t('profile.settingsDescription', 'Manage your account settings and preferences')}</p>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Account Information Section */}
        <div className="lg:col-span-2 space-y-6">
          {/* Account Info Card */}
          <EnhancedCard hoverable>
            <EnhancedCard.Header>
              <EnhancedCard.Title className="flex items-center gap-2">
                <User className="h-5 w-5 text-soccer-primary" />
                {t('profile.accountInfo', 'Account Information')}
              </EnhancedCard.Title>
              <EnhancedCard.Description>
                {t('profile.accountInfoDescription', 'Your personal account details and profile information')}
              </EnhancedCard.Description>
            </EnhancedCard.Header>
            <EnhancedCard.Content className="space-y-6">
              {/* User Avatar and Basic Info */}
              <div className="flex flex-col sm:flex-row items-start sm:items-center gap-4">
                <div className="flex-shrink-0">
                  <PlayerAvatar
                    playerId={user?.id ? parseInt(user.id) : 0}
                    playerName={user?.email?.split('@')[0] || 'User'}
                    avatarUrl={user?.user_metadata?.avatar_url}
                    size="lg"
                    editable={true}
                  />
                </div>
                <div className="flex-1 space-y-2">
                  <div>
                    <Label className="text-sm font-medium">{t('profile.email', 'Email')}</Label>
                    <div className="text-sm text-muted-foreground mt-1">{user?.email}</div>
                  </div>
                  <div>
                    <Label className="text-sm font-medium">{t('profile.memberSince', 'Member Since')}</Label>
                    <div className="text-sm text-muted-foreground mt-1">
                      {user?.created_at ? new Date(user.created_at).toLocaleDateString() : 'N/A'}
                    </div>
                  </div>
                </div>
              </div>
            </EnhancedCard.Content>
          </EnhancedCard>

          {/* Security Card */}
          <EnhancedCard hoverable>
            <EnhancedCard.Header>
              <EnhancedCard.Title className="flex items-center gap-2">
                <Shield className="h-5 w-5 text-soccer-primary" />
                {t('profile.security', 'Security')}
              </EnhancedCard.Title>
              <EnhancedCard.Description>
                {t('profile.securityDescription', 'Update your password and security settings')}
              </EnhancedCard.Description>
            </EnhancedCard.Header>
            {!isChangingPassword ? (
              <EnhancedCard.Content>
                <Button
                  variant="outline"
                  onClick={() => setIsChangingPassword(true)}
                  className="hover:bg-soccer-primary/10 hover:border-soccer-primary/30"
                >
                  <KeyRound className="mr-2 h-4 w-4" />
                  {t('profile.changePassword', 'Change Password')}
                </Button>
              </EnhancedCard.Content>
            ) : (
              <form onSubmit={handlePasswordChange}>
                <EnhancedCard.Content className="space-y-4">
                  <div className="space-y-2">
                    <Label htmlFor="currentPassword">{t('profile.currentPassword', 'Current Password')}</Label>
                    <Input
                      id="currentPassword"
                      type="password"
                      value={passwordData.currentPassword}
                      onChange={(e) =>
                        setPasswordData({ ...passwordData, currentPassword: e.target.value })
                      }
                      required
                      className="focus:border-soccer-primary focus:ring-soccer-primary"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="newPassword">{t('profile.newPassword', 'New Password')}</Label>
                    <Input
                      id="newPassword"
                      type="password"
                      value={passwordData.newPassword}
                      onChange={(e) =>
                        setPasswordData({ ...passwordData, newPassword: e.target.value })
                      }
                      required
                      className="focus:border-soccer-primary focus:ring-soccer-primary"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="confirmPassword">{t('profile.confirmNewPassword', 'Confirm New Password')}</Label>
                    <Input
                      id="confirmPassword"
                      type="password"
                      value={passwordData.confirmPassword}
                      onChange={(e) =>
                        setPasswordData({ ...passwordData, confirmPassword: e.target.value })
                      }
                      required
                      className="focus:border-soccer-primary focus:ring-soccer-primary"
                    />
                  </div>
                </EnhancedCard.Content>
                <EnhancedCard.Footer className="flex flex-row gap-2">
                  <Button
                    type="submit"
                    disabled={isChangingPassword}
                    className="bg-soccer-primary hover:bg-soccer-primary/90"
                  >
                    {isChangingPassword ? t('common.loading', 'Loading...') : t('profile.updatePassword', 'Update Password')}
                  </Button>
                  <Button
                    type="button"
                    variant="outline"
                    onClick={() => {
                      setIsChangingPassword(false);
                      setPasswordData({ currentPassword: "", newPassword: "", confirmPassword: "" });
                    }}
                  >
                    {t('common.cancel', 'Cancel')}
                  </Button>
                </EnhancedCard.Footer>
              </form>
            )}
          </EnhancedCard>
        </div>

        {/* Sidebar Section - Preferences and Support */}
        <div className="lg:col-span-1 space-y-6">
          {/* Preferences Card */}
          <EnhancedCard hoverable>
            <EnhancedCard.Header>
              <EnhancedCard.Title className="flex items-center gap-2">
                <Settings className="h-5 w-5 text-soccer-primary" />
                {t('profile.preferences', 'Preferences')}
              </EnhancedCard.Title>
              <EnhancedCard.Description>
                {t('profile.preferencesDescription', 'Customize your app experience')}
              </EnhancedCard.Description>
            </EnhancedCard.Header>
            <EnhancedCard.Content className="space-y-6">
              {/* Theme Setting */}
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  {theme === 'dark' ? <Moon className="h-4 w-4 text-soccer-primary" /> : <Sun className="h-4 w-4 text-soccer-primary" />}
                  <div>
                    <div className="font-medium text-sm">{t('profile.theme', 'Theme')}</div>
                    <div className="text-xs text-muted-foreground">
                      {theme === 'dark' ? t('profile.darkMode', 'Dark Mode') : t('profile.lightMode', 'Light Mode')}
                    </div>
                  </div>
                </div>
                <ThemeToggle />
              </div>

              {/* Language Setting */}
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  <Languages className="h-4 w-4 text-soccer-primary" />
                  <div>
                    <div className="font-medium text-sm">{t('profile.language', 'Language')}</div>
                    <div className="text-xs text-muted-foreground">
                      {language === 'es' ? 'Español' : 'English'}
                    </div>
                  </div>
                </div>
                <LanguageToggle />
              </div>

              {/* Group Management for Mobile */}
              {isMobile && (
                <div className="pt-4 border-t">
                  <Button
                    variant="outline"
                    className="w-full hover:bg-soccer-primary/10 hover:border-soccer-primary/30"
                    onClick={() => navigate('/group-selection')}
                  >
                    <Users className="mr-2 h-4 w-4" />
                    {t('groups.manageGroups', 'Manage Groups')}
                  </Button>
                </div>
              )}
            </EnhancedCard.Content>
          </EnhancedCard>

          {/* Support Card */}
          <EnhancedCard hoverable>
            <EnhancedCard.Header>
              <EnhancedCard.Title className="flex items-center gap-2">
                <Heart className="h-5 w-5 text-soccer-primary" />
                {t('profile.support', 'Support')}
              </EnhancedCard.Title>
              <EnhancedCard.Description>
                {t('profile.supportDescription', 'Help us improve and grow')}
              </EnhancedCard.Description>
            </EnhancedCard.Header>
            <EnhancedCard.Content className="space-y-4">
              <Button
                variant="outline"
                className="w-full bg-gradient-to-r from-purple-500 to-purple-600 hover:from-purple-600 hover:to-purple-700 text-white border-purple-500 hover:border-purple-600 shadow-md hover:shadow-lg hover:scale-105 transition-all duration-300"
                onClick={openFeedbackForm}
              >
                <MessageSquare className="mr-2 h-4 w-4" />
                {t('profile.giveFeedback', 'Give Feedback')}
              </Button>

              <Button
                className="w-full bg-gradient-to-r from-blue-400 to-blue-500 hover:from-blue-500 hover:to-blue-600 text-white shadow-md hover:shadow-lg hover:scale-105 transition-all duration-300"
                onClick={() => window.open('https://cafecito.app/fulbitostats', '_blank', 'noopener')}
              >
                <Coffee className="mr-2 h-4 w-4" />
                {t('profile.supportWithCafecito', 'Support with Cafecito')}
              </Button>
            </EnhancedCard.Content>
          </EnhancedCard>

          {/* Sign Out Card */}
          <EnhancedCard hoverable variant="destructive">
            <EnhancedCard.Header>
              <EnhancedCard.Title className="flex items-center gap-2 text-destructive">
                <LogOut className="h-5 w-5" />
                {t('auth.signOut')}
              </EnhancedCard.Title>
              <EnhancedCard.Description>
                {t('profile.signOutDescription', 'Sign out of your account')}
              </EnhancedCard.Description>
            </EnhancedCard.Header>
            <EnhancedCard.Content>
              <Button
                variant="destructive"
                onClick={handleLogout}
                className="w-full"
              >
                <LogOut className="mr-2 h-4 w-4" />
                {t('auth.signOut')}
              </Button>
            </EnhancedCard.Content>
          </EnhancedCard>
        </div>
      </div>
    </div>
  );
};

export default ProfilePage;
