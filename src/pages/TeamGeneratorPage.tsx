
import { useState, useEffect, useMemo } from "react";
import { useTranslation } from "react-i18next";
import UnifiedLayout from "@/components/layout/UnifiedLayout";
import PlayerSelectionCard from "@/components/team-generator/PlayerSelectionCard";
import TeamsDisplayCard from "@/components/team-generator/TeamsDisplayCard";
import SaveMatchDialog from "@/components/team-generator/SaveMatchDialog";
import { TeamPreference } from "@/components/team-generator/TeamPreferences";
import { SimpleButtonEditor } from "@/components/team-generator/SimpleButtonEditor";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { useToast } from "@/hooks/use-toast";
import { supabase } from "@/lib/supabase";
import {
  Player,
  Match,
  gameFormats,
  GameFormatKey,
  GenerationMode,
  MATCHES_STORAGE_KEY,
  saveData,
  calculateAverageRating,
  generateTeams as generateTeamsUtil
} from "@/utils/teamGenerator";

const TeamGeneratorPage = () => {
  const { t } = useTranslation();
  const [players, setPlayers] = useState<Player[]>([]);
  const [matches, setMatches] = useState<Match[]>([]);
  const { toast } = useToast();
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedPlayers, setSelectedPlayers] = useState<number[]>([]);
  const [teamA, setTeamA] = useState<Player[]>([]);
  const [teamB, setTeamB] = useState<Player[]>([]);
  const [isSaveModalOpen, setIsSaveModalOpen] = useState(false);
  const [scoreA, setScoreA] = useState<number>(0);
  const [scoreB, setScoreB] = useState<number>(0);
  const [format, setFormat] = useState<GameFormatKey>("5v5");
  const [generationMode, setGenerationMode] = useState<GenerationMode>("balanced");
  const [activeTab, setActiveTab] = useState<string>("auto");

  // Handle tab changes
  useEffect(() => {
    // When switching to manual tab, ensure we have the latest player data
    if (activeTab === "manual") {
      // The DraggableTeamEditor will handle the initial setup
    }
  }, [activeTab]);

  // Get the selected group ID from localStorage
  const selectedGroupId = localStorage.getItem('selectedGroupId');

  // Load players and matches on mount
  useEffect(() => {
    const fetchData = async () => {
      setLoading(true);

      // Check if a group is selected
      if (!selectedGroupId) {
        console.warn("No group selected. Please select a group first.");
        setPlayers([]);
        setLoading(false);
        return;
      }

      try {
        // Get both players and matches to calculate games played, filtered by group
        const [{ data: playersData, error: playersError }, { data: matchesData }] = await Promise.all([
          supabase.from('players').select('*').eq('group_id', selectedGroupId).order('name'),
          supabase.from('matches').select('*').eq('group_id', selectedGroupId)
        ]);

        if (playersError) throw playersError;

        // Calculate played games for each player
        const playersWithStats = (playersData || []).map(p => {
          const gamesPlayed = (matchesData || []).filter(
            m => m.teama?.includes(p.id) || m.teamb?.includes(p.id)
          ).length;

          return {
            ...p,
            rating: calculateAverageRating(p),
            played: gamesPlayed
          };
        });

        setPlayers(playersWithStats);
      } catch (error) {
        console.error("Error fetching players:", error);
        toast({
          title: t('teamGenerator.loadError', 'Error loading players'),
          description: t('teamGenerator.fetchError', 'Failed to fetch player data'),
          variant: "destructive"
        });
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [toast, selectedGroupId]);

  const handlePlayerToggle = (playerId: number) => {
    setSelectedPlayers((prev) =>
      prev.includes(playerId)
        ? prev.filter((id) => id !== playerId)
        : [...prev, playerId]
    );
  };

  const handleSelectAll = () => {
    if (selectedPlayers.length === players.length) {
      setSelectedPlayers([]);
    } else {
      setSelectedPlayers(players.map((p) => p.id));
    }
  };

  const generateTeams = () => {
    const playersNeeded = gameFormats[format].playersPerTeam * 2;

    if (selectedPlayers.length < playersNeeded) {
      toast({
        title: t('teamGenerator.insufficientPlayers', 'Insufficient Players'),
        description: t('teamGenerator.selectAtLeast', 'Please select at least {{count}} players for the {{format}} format.', { count: playersNeeded, format: gameFormats[format].title }),
        variant: "destructive"
      });
      return;
    }

    if (selectedPlayers.length > playersNeeded) {
      toast({
        title: t('teamGenerator.tooManyPlayers', 'Too Many Players'),
        description: t('teamGenerator.selectExactly', 'Please select exactly {{count}} players for the {{format}} format. You have {{selected}}.', { count: playersNeeded, format: gameFormats[format].title, selected: selectedPlayers.length }),
        variant: "destructive"
      });
      return;
    }

    const { teamA: newTeamA, teamB: newTeamB } = generateTeamsUtil(
      selectedPlayers,
      players,
      format,
      generationMode
    );

    setTeamA(newTeamA);
    setTeamB(newTeamB);
  };

  const handleQuickGenerate = (preference: TeamPreference) => {
    // Set format and generation mode from preference
    setFormat(preference.format);
    setGenerationMode(preference.generationMode);

    // Use saved player IDs if available, otherwise keep current selection
    if (preference.playerIds && preference.playerIds.length > 0) {
      // Filter to ensure all players exist in the current player list
      const validPlayerIds = preference.playerIds.filter(id =>
        players.some(player => player.id === id)
      );

      setSelectedPlayers(validPlayerIds);

      // Generate teams with a slight delay to ensure state updates
      setTimeout(() => {
        const playersNeeded = gameFormats[preference.format].playersPerTeam * 2;

        // Check if we have enough players
        if (validPlayerIds.length < playersNeeded) {
          toast({
            title: t('teamGenerator.insufficientPlayers', 'Insufficient Players'),
            description: t('teamGenerator.savedPreferenceRequires', 'The saved preference requires {{count}} players, but only {{valid}} valid players were found.', { count: playersNeeded, valid: validPlayerIds.length }),
            variant: "destructive"
          });
          return;
        }

        // If we have too many players, take the first N needed
        const playerIdsToUse = validPlayerIds.length > playersNeeded
          ? validPlayerIds.slice(0, playersNeeded)
          : validPlayerIds;

        const { teamA: newTeamA, teamB: newTeamB } = generateTeamsUtil(
          playerIdsToUse,
          players,
          preference.format,
          preference.generationMode
        );

        setTeamA(newTeamA);
        setTeamB(newTeamB);
      }, 100);
    } else {
      // Just use current selection and generate teams
      generateTeams();
    }
  };

  const handleSaveMatch = async () => {
    // Check if a group is selected
    if (!selectedGroupId) {
      toast({
        title: t('groups.noGroupSelected', 'No Group Selected'),
        description: t('teamGenerator.selectGroupBeforeSaving', 'Please select a group before saving a match.'),
        variant: "destructive"
      });
      return;
    }

    // Create a date at noon UTC to avoid timezone issues
    const now = new Date();
    const dateAtNoon = new Date(Date.UTC(
      now.getFullYear(),
      now.getMonth(),
      now.getDate(),
      12, 0, 0
    ));

    const newMatchData = {
      match_date: dateAtNoon.toISOString(),
      teama: teamA.map((p) => p.id),
      teamb: teamB.map((p) => p.id),
      scorea: scoreA,
      scoreb: scoreB,
      group_id: selectedGroupId,
      winner: scoreA > scoreB ? 'A' : scoreB > scoreA ? 'B' : 'Draw',
    };

    try {
      // Save to Supabase
      const { data, error } = await supabase
        .from('matches')
        .insert([newMatchData])
        .select();

      if (error) throw error;

      // Update local state with the saved match
      if (data && data.length > 0) {
        const savedMatch = {
          ...data[0],
          date: new Date(data[0].match_date),
          teamA: data[0].teama,
          teamB: data[0].teamb,
          scoreA: data[0].scorea,
          scoreB: data[0].scoreb,
        } as Match;

        setMatches([savedMatch, ...matches]);
        toast({ title: t('teamGenerator.matchSavedSuccess', 'Match Saved Successfully!') });
      }
    } catch (error: any) {
      console.error("Error saving match:", error);
      toast({
        title: t('teamGenerator.saveError', 'Error Saving Match'),
        description: error?.message || t('teamGenerator.saveFailure', 'Failed to save match'),
        variant: "destructive"
      });
    }

    setIsSaveModalOpen(false);
    setScoreA(0);
    setScoreB(0);
  };

  const hasGeneratedTeams = teamA.length > 0 || teamB.length > 0;

  // Add filtered players logic
  const filteredAvailablePlayers = useMemo(() => {
    const searchTerms = searchQuery.toLowerCase().split(',').map(term => term.trim());
    return players.filter(player =>
      searchTerms.some(term => player.name.toLowerCase().includes(term))
    );
  }, [players, searchQuery]);

  return (
    <>
      <div className="space-y-4">
        <h1 className="text-2xl font-bold text-foreground">{t('teamGenerator.title')}</h1>

        <Tabs value={activeTab} onValueChange={(value) => {
          console.log('DEBUG: Tab changed to:', value);
          console.log('DEBUG: Current teams:', teamA, teamB);
          setActiveTab(value);
        }}>
          <TabsList className="grid w-full grid-cols-2 mb-4">
            <TabsTrigger value="auto">{t('teamGenerator.autoGenerate', 'Auto Generate')}</TabsTrigger>
            <TabsTrigger value="manual">{t('teamGenerator.manualEditor', 'Manual Editor')}</TabsTrigger>
          </TabsList>

          <TabsContent value="auto" className="space-y-6">
            <div className="grid grid-cols-1 gap-6">
              <div>
                <PlayerSelectionCard
                  players={filteredAvailablePlayers}
                  selectedPlayers={selectedPlayers}
                  format={format}
                  gameFormats={gameFormats}
                  generationMode={generationMode}
                  searchQuery={searchQuery}
                  onSearchChange={setSearchQuery}
                  onPlayerToggle={handlePlayerToggle}
                  onSelectAll={handleSelectAll}
                  onFormatChange={(value) => setFormat(value as GameFormatKey)}
                  onGenerationModeChange={setGenerationMode}
                  onGenerateTeams={generateTeams}
                  onQuickGenerate={handleQuickGenerate}
                />
              </div>

              <div>
                <TeamsDisplayCard
                  teamA={teamA}
                  teamB={teamB}
                  hasGeneratedTeams={hasGeneratedTeams}
                  onSaveMatch={() => {
                    if (!hasGeneratedTeams) {
                      toast({title: t('teamGenerator.noTeamsGenerated', 'No Teams Generated'), description: t('teamGenerator.generateTeamsFirst', 'Generate teams first before saving.'), variant: "destructive"});
                      return;
                    }
                    setIsSaveModalOpen(true);
                  }}
                />
              </div>
            </div>
          </TabsContent>

          <TabsContent value="manual">
            <SimpleButtonEditor
              teamA={teamA}
              teamB={teamB}
              availablePlayers={players}
              onTeamsChange={(newTeamA, newTeamB) => {
                setTeamA(newTeamA);
                setTeamB(newTeamB);
              }}
              onSaveTeams={() => {
                setIsSaveModalOpen(true);
              }}
            />
          </TabsContent>
        </Tabs>
      </div>

      <SaveMatchDialog
        open={isSaveModalOpen}
        onOpenChange={setIsSaveModalOpen}
        teamA={teamA}
        teamB={teamB}
        scoreA={scoreA}
        scoreB={scoreB}
        format={format}
        onScoreAChange={setScoreA}
        onScoreBChange={setScoreB}
        onSaveMatch={handleSaveMatch}
      />
    </>
  );
};

export default TeamGeneratorPage;
