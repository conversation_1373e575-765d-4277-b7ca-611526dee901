import { useState, useEffect, useMemo } from "react";
import { useTranslation } from "react-i18next";
import DemoLayout from "@/components/layout/DemoLayout";
import { useDemo } from "@/context/DemoContext";
import { useMediaQuery } from "@/hooks/use-media-query";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Handshake, Users, UsersRound, Filter } from "lucide-react";
import { MobileTable } from "@/components/ui/mobile-table";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";

interface ChemistryData {
  id: string;
  players: number[];
  played: number;
  wins: number;
  winRate: number;
}

const DemoChemistryPage = () => {
  const { t } = useTranslation();
  const { players, matches } = useDemo();
  const [minGames, setMinGames] = useState("3");
  const [viewMode, setViewMode] = useState<"duos" | "trios" | "quads">("duos");
  const isMobile = useMediaQuery("(max-width: 768px)");

  // Set page title
  useEffect(() => {
    document.title = `${t('nav.chemistry')} - ${t('app.name')} Demo`;
  }, [t]);

  // Get player name helper
  const getPlayerName = (playerId: number) => {
    const player = players.find(p => p.id === playerId);
    return player?.name || `Player ${playerId}`;
  };

  // Calculate chemistry data
  const chemistryData = useMemo(() => {
    const minGamesNum = parseInt(minGames);
    const duosMap = new Map<string, { played: number; wins: number; players: number[] }>();
    const triosMap = new Map<string, { played: number; wins: number; players: number[] }>();
    const quadsMap = new Map<string, { played: number; wins: number; players: number[] }>();

    matches.forEach(match => {
      const processTeam = (team: number[], won: boolean) => {
        // Process duos
        for (let i = 0; i < team.length; i++) {
          for (let j = i + 1; j < team.length; j++) {
            const key = [team[i], team[j]].sort().join('-');
            const existing = duosMap.get(key) || { played: 0, wins: 0, players: [team[i], team[j]].sort() };
            existing.played++;
            if (won) existing.wins++;
            duosMap.set(key, existing);
          }
        }

        // Process trios
        for (let i = 0; i < team.length; i++) {
          for (let j = i + 1; j < team.length; j++) {
            for (let k = j + 1; k < team.length; k++) {
              const key = [team[i], team[j], team[k]].sort().join('-');
              const existing = triosMap.get(key) || { played: 0, wins: 0, players: [team[i], team[j], team[k]].sort() };
              existing.played++;
              if (won) existing.wins++;
              triosMap.set(key, existing);
            }
          }
        }

        // Process quads
        for (let i = 0; i < team.length; i++) {
          for (let j = i + 1; j < team.length; j++) {
            for (let k = j + 1; k < team.length; k++) {
              for (let l = k + 1; l < team.length; l++) {
                const key = [team[i], team[j], team[k], team[l]].sort().join('-');
                const existing = quadsMap.get(key) || { played: 0, wins: 0, players: [team[i], team[j], team[k], team[l]].sort() };
                existing.played++;
                if (won) existing.wins++;
                quadsMap.set(key, existing);
              }
            }
          }
        }
      };

      if (match.winner === 'A') {
        processTeam(match.teama, true);
        processTeam(match.teamb, false);
      } else if (match.winner === 'B') {
        processTeam(match.teama, false);
        processTeam(match.teamb, true);
      } else {
        // Draw - no wins for either team
        processTeam(match.teama, false);
        processTeam(match.teamb, false);
      }
    });

    const processChemistryData = (dataMap: Map<string, any>): ChemistryData[] => {
      return Array.from(dataMap.values())
        .filter(item => item.played >= minGamesNum)
        .map((item, index) => ({
          id: `${item.players.join('-')}-${index}`,
          ...item,
          winRate: item.played > 0 ? (item.wins / item.played) * 100 : 0
        }))
        .sort((a, b) => b.winRate - a.winRate);
    };

    return {
      duos: processChemistryData(duosMap),
      trios: processChemistryData(triosMap),
      quads: processChemistryData(quadsMap)
    };
  }, [matches, minGames]);

  // Get current data based on view mode
  const currentData = chemistryData[viewMode];

  // Format player names
  const formatPlayerNames = (playerIds: number[]) => {
    return playerIds.map(id => getPlayerName(id)).join(' & ');
  };

  // Desktop table columns
  const columns = [
    {
      header: t('chemistry.players'),
      accessorKey: 'players',
      cell: (item: ChemistryData) => (
        <span className="font-medium">{formatPlayerNames(item.players)}</span>
      ),
    },
    {
      header: t('chemistry.gamesPlayed'),
      accessorKey: 'played',
      cell: (item: ChemistryData) => (
        <span className="font-medium">{item.played}</span>
      ),
    },
    {
      header: t('chemistry.wins'),
      accessorKey: 'wins',
      cell: (item: ChemistryData) => (
        <span className="font-medium text-green-600">{item.wins}</span>
      ),
    },
    {
      header: t('chemistry.winRate'),
      accessorKey: 'winRate',
      cell: (item: ChemistryData) => (
        <span className="font-medium">{item.winRate.toFixed(1)}%</span>
      ),
    },
    {
      header: t('chemistry.record'),
      accessorKey: 'record',
      cell: (item: ChemistryData) => (
        <Badge variant="outline">
          {t('chemistry.wonFormat', { wins: item.wins, total: item.played })}
        </Badge>
      ),
    },
  ];

  return (
    <DemoLayout title={t('nav.chemistry')}>
      <div className="space-y-6">
        {/* Filters */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Filter className="h-5 w-5" />
              {t('chemistry.filters')}
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="text-sm font-medium mb-2 block">{t('chemistry.minGames')}</label>
                <Select value={minGames} onValueChange={setMinGames}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    {[0, 1, 2, 3, 4, 5].map(num => (
                      <SelectItem key={num} value={num.toString()}>
                        {num} {t('stats.games')}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Chemistry Tabs */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Handshake className="h-5 w-5" />
              {t('chemistry.title')}
            </CardTitle>
          </CardHeader>
          <CardContent>
            <Tabs value={viewMode} onValueChange={(value) => setViewMode(value as any)} className="w-full">
              <TabsList className="grid w-full grid-cols-3 md:w-[400px]">
                <TabsTrigger value="duos">
                  <Handshake className="h-4 w-4 mr-1" />
                  {t('chemistry.duos')}
                </TabsTrigger>
                <TabsTrigger value="trios">
                  <Users className="h-4 w-4 mr-1" />
                  {t('chemistry.trios')}
                </TabsTrigger>
                <TabsTrigger value="quads">
                  <UsersRound className="h-4 w-4 mr-1" />
                  {t('chemistry.quads')}
                </TabsTrigger>
              </TabsList>

              <TabsContent value={viewMode} className="mt-6">
                {currentData.length === 0 ? (
                  <div className="text-center py-8 text-muted-foreground">
                    {t('chemistry.noDataAvailable')}
                  </div>
                ) : (
                  <>
                    {isMobile ? (
                      <MobileTable
                        data={currentData}
                        columns={[
                          {
                            header: t('chemistry.players'),
                            accessorKey: 'players' as keyof ChemistryData,
                            priority: "high",
                            cell: (item: ChemistryData) => (
                              <span className="font-medium">{formatPlayerNames(item.players)}</span>
                            ),
                          },
                          {
                            header: t('chemistry.winRate'),
                            accessorKey: 'winRate' as keyof ChemistryData,
                            priority: "high",
                            cell: (item: ChemistryData) => (
                              <span className="font-medium">{item.winRate.toFixed(1)}%</span>
                            ),
                          },
                          {
                            header: t('chemistry.record'),
                            accessorKey: 'record' as keyof ChemistryData,
                            priority: "medium",
                            cell: (item: ChemistryData) => (
                              <Badge variant="outline">
                                {t('chemistry.wonFormat', { wins: item.wins, total: item.played })}
                              </Badge>
                            ),
                          },
                        ]}
                        emptyMessage={t('chemistry.noDataAvailable')}
                      />
                    ) : (
                      <div className="rounded-md border">
                        <Table>
                          <TableHeader>
                            <TableRow>
                              {columns.map((column, index) => (
                                <TableHead key={index}>{column.header}</TableHead>
                              ))}
                            </TableRow>
                          </TableHeader>
                          <TableBody>
                            {currentData.map((item) => (
                              <TableRow key={item.id}>
                                {columns.map((column, index) => (
                                  <TableCell key={index}>
                                    {column.cell(item)}
                                  </TableCell>
                                ))}
                              </TableRow>
                            ))}
                          </TableBody>
                        </Table>
                      </div>
                    )}
                  </>
                )}
              </TabsContent>
            </Tabs>
          </CardContent>
        </Card>

        {/* Data count */}
        <div className="text-sm text-muted-foreground text-center">
          {t('chemistry.showingCombinations', { 
            count: currentData.length,
            type: t(`chemistry.${viewMode}`)
          })}
        </div>
      </div>
    </DemoLayout>
  );
};

export default DemoChemistryPage;
