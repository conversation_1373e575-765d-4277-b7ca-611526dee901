import { useState, useEffect, use<PERSON>emo } from "react";
import { useParams } from "react-router-dom";
import SharedViewLayout from "@/components/layout/SharedViewLayout";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { useTranslation } from "react-i18next";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Tabs,
  Ta<PERSON>Content,
  TabsList,
  TabsTrigger,
} from "@/components/ui/tabs";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { supabase } from "@/lib/supabase";
import { useToast } from "@/hooks/use-toast";
import { useMediaQuery } from "@/hooks/use-media-query";
import { <PERSON>, Handshake, <PERSON>, <PERSON>Round, <PERSON>R<PERSON> } from "lucide-react";

// --- Interfaces ---
interface Player {
  id: number;
  name: string;
}

interface Match {
  id: number;
  match_date: string;
  teama: number[];
  teamb: number[];
  scorea: number;
  scoreb: number;
  winner: 'A' | 'B' | 'Draw';
  group_id?: string;
}

interface ChemistryPair {
  id?: string;
  player1: number;
  player2: number;
  played: number;
  wins: number;
  winRate: number;
}

interface ChemistryTrio {
  id?: string;
  player1: number;
  player2: number;
  player3: number;
  played: number;
  wins: number;
  winRate: number;
}

interface ChemistryQuad {
  id?: string;
  player1: number;
  player2: number;
  player3: number;
  player4: number;
  played: number;
  wins: number;
  winRate: number;
}

const SharedChemistryPage = () => {
  const { groupId } = useParams<{ groupId: string }>();
  const { toast } = useToast();
  const { t } = useTranslation();
  const [loading, setLoading] = useState(true);
  const [players, setPlayers] = useState<Player[]>([]);
  const [matches, setMatches] = useState<Match[]>([]);
  const [duoData, setDuoData] = useState<ChemistryPair[]>([]);
  const [trioData, setTrioData] = useState<ChemistryTrio[]>([]);
  const [quadData, setQuadData] = useState<ChemistryQuad[]>([]);

  const [search, setSearch] = useState("");
  const [selectedPlayer, setSelectedPlayer] = useState<string>("");
  const [minGames, setMinGames] = useState<string>("0");
  const [viewMode, setViewMode] = useState<"duos" | "trios" | "quads">("duos");
  const [currentPage, setCurrentPage] = useState(1);
  const [groupName, setGroupName] = useState<string>("");
  const isMobile = useMediaQuery("(max-width: 768px)");
  const itemsPerPage = isMobile ? 6 : 20;

  // Fetch data on mount
  useEffect(() => {
    const fetchData = async () => {
      setLoading(true);

      if (!groupId) {
        console.warn("No group ID provided");
        setLoading(false);
        return;
      }

      try {
        // Fetch group info first to get the name
        const { data: groupData, error: groupError } = await supabase
          .from('friend_groups')
          .select('name')
          .eq('id', groupId)
          .single();

        if (groupError) throw groupError;
        if (groupData) {
          setGroupName(groupData.name);
        }

        // Fetch players and matches data
        const [{ data: playersData, error: playersError }, { data: matchesData, error: matchesError }] = await Promise.all([
          supabase.from('players').select('id, name').eq('group_id', groupId),
          supabase.from('matches').select('*').eq('group_id', groupId)
        ]);

        if (playersError) throw playersError;
        if (matchesError) throw matchesError;

        setPlayers(playersData || []);
        setMatches(matchesData || []);

        // Calculate chemistry data
        calculateChemistryData(playersData || [], matchesData || []);
      } catch (error) {
        console.error('Error fetching data:', error);
        toast({
          title: t('common.error'),
          description: t('chemistry.fetchError'),
          variant: 'destructive',
        });
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [groupId, toast]);

  // Calculate chemistry data
  const calculateChemistryData = (players: Player[], matches: Match[]) => {
    // Initialize data structures
    const duos: { [key: string]: ChemistryPair } = {};
    const trios: { [key: string]: ChemistryTrio } = {};
    const quads: { [key: string]: ChemistryQuad } = {};

    // Process each match
    matches.forEach(match => {
      if (!match.winner) return;

      // Process Team A
      processTeamChemistry(match.teama, match.winner === 'A', duos, trios, quads);

      // Process Team B
      processTeamChemistry(match.teamb, match.winner === 'B', duos, trios, quads);
    });

    // Convert to arrays and calculate win rates
    const duoArray = Object.values(duos).map(pair => ({
      ...pair,
      winRate: (pair.wins / pair.played) * 100
    }));

    const trioArray = Object.values(trios).map(trio => ({
      ...trio,
      winRate: (trio.wins / trio.played) * 100
    }));

    const quadArray = Object.values(quads).map(quad => ({
      ...quad,
      winRate: (quad.wins / quad.played) * 100
    }));

    setDuoData(duoArray);
    setTrioData(trioArray);
    setQuadData(quadArray);
  };

  // Helper function to process team chemistry
  const processTeamChemistry = (
    team: number[],
    isWin: boolean,
    duos: { [key: string]: ChemistryPair },
    trios: { [key: string]: ChemistryTrio },
    quads: { [key: string]: ChemistryQuad }
  ) => {
    // Process duos (pairs)
    for (let i = 0; i < team.length; i++) {
      for (let j = i + 1; j < team.length; j++) {
        const player1 = Math.min(team[i], team[j]);
        const player2 = Math.max(team[i], team[j]);
        const key = `${player1}-${player2}`;

        if (!duos[key]) {
          duos[key] = {
            player1,
            player2,
            played: 0,
            wins: 0,
            winRate: 0
          };
        }

        duos[key].played++;
        if (isWin) duos[key].wins++;
      }
    }

    // Process trios (groups of 3)
    if (team.length >= 3) {
      for (let i = 0; i < team.length; i++) {
        for (let j = i + 1; j < team.length; j++) {
          for (let k = j + 1; k < team.length; k++) {
            const players = [team[i], team[j], team[k]].sort((a, b) => a - b);
            const key = players.join('-');

            if (!trios[key]) {
              trios[key] = {
                player1: players[0],
                player2: players[1],
                player3: players[2],
                played: 0,
                wins: 0,
                winRate: 0
              };
            }

            trios[key].played++;
            if (isWin) trios[key].wins++;
          }
        }
      }
    }

    // Process quads (groups of 4)
    if (team.length >= 4) {
      for (let i = 0; i < team.length; i++) {
        for (let j = i + 1; j < team.length; j++) {
          for (let k = j + 1; k < team.length; k++) {
            for (let l = k + 1; l < team.length; l++) {
              const players = [team[i], team[j], team[k], team[l]].sort((a, b) => a - b);
              const key = players.join('-');

              if (!quads[key]) {
                quads[key] = {
                  player1: players[0],
                  player2: players[1],
                  player3: players[2],
                  player4: players[3],
                  played: 0,
                  wins: 0,
                  winRate: 0
                };
              }

              quads[key].played++;
              if (isWin) quads[key].wins++;
            }
          }
        }
      }
    }
  };

  // Get player name by ID
  const getPlayerName = (id: number) => {
    const player = players.find(p => p.id === id);
    return player ? player.name : `${t('players.name')} ${id}`;
  };

  // Filter chemistry data based on search and min games
  const filteredData = useMemo(() => {
    const minGamesValue = parseInt(minGames) || 0;
    let data: any[] = [];

    if (viewMode === 'duos') {
      data = duoData.filter(pair => pair.played >= minGamesValue);
    } else if (viewMode === 'trios') {
      data = trioData.filter(trio => trio.played >= minGamesValue);
    } else if (viewMode === 'quads') {
      data = quadData.filter(quad => quad.played >= minGamesValue);
    }

    if (search) {
      const searchLower = search.toLowerCase();
      return data.filter(item => {
        const playerNames = [];

        if ('player1' in item) playerNames.push(getPlayerName(item.player1));
        if ('player2' in item) playerNames.push(getPlayerName(item.player2));
        if ('player3' in item) playerNames.push(getPlayerName(item.player3));
        if ('player4' in item) playerNames.push(getPlayerName(item.player4));

        return playerNames.some(name => name.toLowerCase().includes(searchLower));
      });
    }

    if (selectedPlayer && selectedPlayer !== 'all') {
      const playerId = parseInt(selectedPlayer);
      return data.filter(item => {
        return item.player1 === playerId ||
               item.player2 === playerId ||
               ('player3' in item && item.player3 === playerId) ||
               ('player4' in item && item.player4 === playerId);
      });
    }

    return data;
  }, [duoData, trioData, quadData, search, minGames, viewMode, selectedPlayer]);

  // Sort data by win rate
  const sortedData = useMemo(() => {
    return [...filteredData].sort((a, b) => {
      // Sort by win rate (descending)
      if (b.winRate !== a.winRate) return b.winRate - a.winRate;

      // If win rates are equal, sort by games played (descending)
      return b.played - a.played;
    });
  }, [filteredData]);

  // Paginate data
  const paginatedData = useMemo(() => {
    const startIndex = (currentPage - 1) * itemsPerPage;
    return sortedData.slice(startIndex, startIndex + itemsPerPage);
  }, [sortedData, currentPage, itemsPerPage]);

  // Get CSS class for win rate badge
  const getWinRateBadgeClass = (winRate: number) => {
    if (winRate >= 70) return "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-100";
    if (winRate >= 50) return "bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-100";
    if (winRate >= 30) return "bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-100";
    return "bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-100";
  };

  // Render table content based on view mode
  const renderTableContent = () => {
    if (loading) {
      return (
        <div className="flex items-center justify-center min-h-[200px]">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-soccer-primary" />
        </div>
      );
    }

    if (paginatedData.length === 0) {
      return (
        <div className="text-center py-10 text-muted-foreground">
          {search || selectedPlayer ? t('chemistry.noDataMatchesFilters') : t('chemistry.noDataAvailable')}
        </div>
      );
    }

    let renderRow;

    if (viewMode === 'duos') {
      renderRow = (item: ChemistryPair) => (
        <TableRow key={`duo-${item.player1}-${item.player2}`}>
          <TableCell className="font-medium">{getPlayerName(item.player1)}</TableCell>
          <TableCell className="text-center"><ArrowRight className="inline-block h-4 w-4" /></TableCell>
          <TableCell>{getPlayerName(item.player2)}</TableCell>
          <TableCell className="text-center">{item.played}</TableCell>
          <TableCell className="text-center">{item.wins}</TableCell>
          <TableCell className="text-right">
            <Badge variant="outline" className={getWinRateBadgeClass(item.winRate)}>
              {(item.winRate ?? 0).toFixed(1)}%
            </Badge>
          </TableCell>
        </TableRow>
      );
    } else if (viewMode === 'trios') {
      renderRow = (item: ChemistryTrio) => (
        <TableRow key={`trio-${item.player1}-${item.player2}-${item.player3}`}>
          <TableCell className="font-medium">
            {getPlayerName(item.player1)}, {getPlayerName(item.player2)}, {getPlayerName(item.player3)}
          </TableCell>
          <TableCell className="text-center">{item.played}</TableCell>
          <TableCell className="text-center">{item.wins}</TableCell>
          <TableCell className="text-right">
            <Badge variant="outline" className={getWinRateBadgeClass(item.winRate)}>
              {(item.winRate ?? 0).toFixed(1)}%
            </Badge>
          </TableCell>
        </TableRow>
      );
    } else {
      renderRow = (item: ChemistryQuad) => (
        <TableRow key={`quad-${item.player1}-${item.player2}-${item.player3}-${item.player4}`}>
          <TableCell className="font-medium">
            {getPlayerName(item.player1)}, {getPlayerName(item.player2)},
            {getPlayerName(item.player3)}, {getPlayerName(item.player4)}
          </TableCell>
          <TableCell className="text-center">{item.played}</TableCell>
          <TableCell className="text-center">{item.wins}</TableCell>
          <TableCell className="text-right">
            <Badge variant="outline" className={getWinRateBadgeClass(item.winRate)}>
              {(item.winRate ?? 0).toFixed(1)}%
            </Badge>
          </TableCell>
        </TableRow>
      );
    }

    if (isMobile) {
      // Mobile view rendering
      return (
        <>
          <div className="rounded-md border divide-y">
            {paginatedData.map((item) => (
              <div key={item.id || Math.random()} className="p-4 flex flex-col gap-2">
                {viewMode === 'duos' ? (
                  <>
                    <div className="flex justify-between items-center">
                      <span className="text-sm text-muted-foreground">{t('players.title')}</span>
                      <div className="font-medium">
                        {getPlayerName(item.player1)} → {getPlayerName(item.player2)}
                      </div>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-sm text-muted-foreground">{t('matches.games')}</span>
                      <div className="font-medium">{item.played}</div>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-sm text-muted-foreground">{t('players.winRate')}</span>
                      <Badge variant="outline" className={getWinRateBadgeClass(item.winRate)}>
                        {(item.winRate ?? 0).toFixed(1)}%
                      </Badge>
                    </div>
                  </>
                ) : viewMode === 'trios' ? (
                  <>
                    <div className="flex justify-between items-center">
                      <span className="text-sm text-muted-foreground">{t('players.title')}</span>
                      <div className="font-medium">
                        {getPlayerName(item.player1)}, {getPlayerName(item.player2)}, {getPlayerName(item.player3)}
                      </div>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-sm text-muted-foreground">{t('matches.games')}</span>
                      <div className="font-medium">{item.played}</div>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-sm text-muted-foreground">{t('players.winRate')}</span>
                      <Badge variant="outline" className={getWinRateBadgeClass(item.winRate)}>
                        {(item.winRate ?? 0).toFixed(1)}%
                      </Badge>
                    </div>
                  </>
                ) : (
                  <>
                    <div className="flex justify-between items-center">
                      <span className="text-sm text-muted-foreground">{t('players.title')}</span>
                      <div className="font-medium">
                        {getPlayerName(item.player1)}, {getPlayerName(item.player2)},
                        {getPlayerName(item.player3)}, {getPlayerName(item.player4)}
                      </div>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-sm text-muted-foreground">{t('matches.games')}</span>
                      <div className="font-medium">{item.played}</div>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-sm text-muted-foreground">{t('players.winRate')}</span>
                      <Badge variant="outline" className={getWinRateBadgeClass(item.winRate)}>
                        {(item.winRate ?? 0).toFixed(1)}%
                      </Badge>
                    </div>
                  </>
                )}
              </div>
            ))}
          </div>

          {/* Pagination for mobile */}
          {sortedData.length > itemsPerPage && (
            <div className="flex justify-between items-center mt-4">
              <button
                onClick={() => setCurrentPage(prev => Math.max(prev - 1, 1))}
                disabled={currentPage === 1}
                className="px-3 py-1 rounded border disabled:opacity-50"
              >
                {t('common.previous')}
              </button>
              <span className="text-sm">
                {t('common.page')} {currentPage} {t('common.of')} {Math.ceil(sortedData.length / itemsPerPage)}
              </span>
              <button
                onClick={() => setCurrentPage(prev => Math.min(prev + 1, Math.ceil(sortedData.length / itemsPerPage)))}
                disabled={currentPage >= Math.ceil(sortedData.length / itemsPerPage)}
                className="px-3 py-1 rounded border disabled:opacity-50"
              >
                {t('common.next')}
              </button>
            </div>
          )}
        </>
      );
    }

    // Desktop view rendering
    return (
      <>
        <Table>
          <TableHeader>
            <TableRow>
              {viewMode === 'duos' ? (
                <>
                  <TableHead>Player 1</TableHead>
                  <TableHead className="w-[50px]"></TableHead>
                  <TableHead>Player 2</TableHead>
                </>
              ) : (
                <TableHead>Players</TableHead>
              )}
              <TableHead className="text-center">Games</TableHead>
              <TableHead className="text-center">Wins</TableHead>
              <TableHead className="text-right">Win Rate</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {paginatedData.map(renderRow)}
          </TableBody>
        </Table>

        {/* Pagination for desktop */}
        {sortedData.length > itemsPerPage && (
          <div className="flex justify-between items-center mt-4">
            <button
              onClick={() => setCurrentPage(prev => Math.max(prev - 1, 1))}
              disabled={currentPage === 1}
              className="px-3 py-1 rounded border disabled:opacity-50"
            >
              {t('common.previous')}
            </button>
            <span className="text-sm">
              {t('common.page')} {currentPage} {t('common.of')} {Math.ceil(sortedData.length / itemsPerPage)}
            </span>
            <button
              onClick={() => setCurrentPage(prev => Math.min(prev + 1, Math.ceil(sortedData.length / itemsPerPage)))}
              disabled={currentPage >= Math.ceil(sortedData.length / itemsPerPage)}
              className="px-3 py-1 rounded border disabled:opacity-50"
            >
              {t('common.next')}
            </button>
          </div>
        )}
      </>
    );
  };

  return (
    <SharedViewLayout title={t('chemistry.title')} groupName={groupName}>
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Card 1: Analysis Table */}
        <Card className="lg:col-span-1">
          <CardHeader className="pb-3">
            <CardTitle>{t('chemistry.analysisTitle')}</CardTitle>
            <CardDescription>
              {t('chemistry.analysisDescription')}
            </CardDescription>
          </CardHeader>
          <CardContent>
            {/* Filter Controls */}
            <div className="flex flex-col sm:flex-row gap-4 mb-6">
              <div className="relative flex-grow">
                <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                <Input placeholder={t('chemistry.searchPlaceholder', { viewMode })} value={search} onChange={(e) => setSearch(e.target.value)} className="pl-8" />
              </div>
              <div className="flex gap-2 items-center">
                <Select value={minGames} onValueChange={setMinGames}>
                  <SelectTrigger className="w-[100px]">
                    <SelectValue placeholder={t('leaderboard.minGames')} />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="0">{t('common.all')}</SelectItem>
                    <SelectItem value="2">2+</SelectItem>
                    <SelectItem value="3">3+</SelectItem>
                    <SelectItem value="5">5+</SelectItem>
                    <SelectItem value="7">7+</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            {/* Tabs for View Mode */}
            <Tabs value={viewMode} onValueChange={(value) => setViewMode(value as any)} className="w-full">
              <TabsList className="grid w-full grid-cols-3 md:w-[400px]">
                <TabsTrigger value="duos"><Handshake className="h-4 w-4 mr-1 inline-block"/>{t('chemistry.duos')}</TabsTrigger>
                <TabsTrigger value="trios"><Users className="h-4 w-4 mr-1 inline-block"/>{t('chemistry.trios')}</TabsTrigger>
                <TabsTrigger value="quads"><UsersRound className="h-4 w-4 mr-1 inline-block"/>{t('chemistry.quads')}</TabsTrigger>
              </TabsList>
              {renderTableContent()}
            </Tabs>
          </CardContent>
        </Card>

        {/* Card 2: Player Filter */}
        <Card className="lg:col-span-1">
          <CardHeader className="pb-3">
            <CardTitle>{t('chemistry.playerFilterTitle')}</CardTitle>
            <CardDescription>
              {t('chemistry.playerFilterDescription')}
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Select value={selectedPlayer} onValueChange={setSelectedPlayer}>
              <SelectTrigger>
                <SelectValue placeholder={t('chemistry.selectPlayerPlaceholder')} />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">{t('players.allPlayers')}</SelectItem>
                {players
                  .sort((a, b) => a.name.localeCompare(b.name))
                  .map(player => (
                    <SelectItem key={player.id} value={player.id.toString()}>
                      {player.name}
                    </SelectItem>
                  ))}
              </SelectContent>
            </Select>

            {selectedPlayer && (
              <div className="mt-6">
                <h3 className="text-sm font-medium mb-2">{t('chemistry.summary')}</h3>
                <div className="grid grid-cols-3 gap-4">
                  <div className="bg-gray-50 dark:bg-gray-800 p-3 rounded-md border dark:border-gray-700">
                    <div className="text-xs text-muted-foreground mb-1">{t('chemistry.bestPartner')}</div>
                    {(() => {
                      // Get all best partners with the same win rate
                      const filteredPairs = duoData
                        .filter(pair => pair.player1 === parseInt(selectedPlayer) || pair.player2 === parseInt(selectedPlayer))
                        .filter(pair => pair.played >= 3)
                        .sort((a, b) => b.winRate - a.winRate);

                      // If there are no pairs, return null
                      if (filteredPairs.length === 0) return null;

                      // Get the highest win rate
                      const highestWinRate = filteredPairs[0].winRate;

                      // Get all pairs with the highest win rate
                      const bestPairs = filteredPairs.filter(pair => pair.winRate === highestWinRate);

                      // Select one at random
                      const randomPair = bestPairs[Math.floor(Math.random() * bestPairs.length)];

                      return (
                        <div key={`best-${randomPair.player1}-${randomPair.player2}`}>
                          <div className="font-medium">
                            {getPlayerName(randomPair.player1 === parseInt(selectedPlayer) ? randomPair.player2 : randomPair.player1)}
                          </div>
                          <div className="text-xs mt-1">
                            <Badge variant="outline" className={getWinRateBadgeClass(randomPair.winRate)}>
                              {randomPair.winRate.toFixed(1)}% ({randomPair.played} {t('matches.games')})
                            </Badge>
                          </div>
                        </div>
                      );
                    })()}
                  </div>

                  <div className="bg-gray-50 dark:bg-gray-800 p-3 rounded-md border dark:border-gray-700">
                    <div className="text-xs text-muted-foreground mb-1">{t('chemistry.bestTrio')}</div>
                    {(() => {
                      // Get all best trios with the same win rate
                      const filteredTrios = trioData
                        .filter(trio =>
                          trio.player1 === parseInt(selectedPlayer) ||
                          trio.player2 === parseInt(selectedPlayer) ||
                          trio.player3 === parseInt(selectedPlayer)
                        )
                        .filter(trio => trio.played >= 2)
                        .sort((a, b) => b.winRate - a.winRate);

                      // If there are no trios, return null
                      if (filteredTrios.length === 0) return null;

                      // Get the highest win rate
                      const highestWinRate = filteredTrios[0].winRate;

                      // Get all trios with the highest win rate
                      const bestTrios = filteredTrios.filter(trio => trio.winRate === highestWinRate);

                      // Select one at random
                      const randomTrio = bestTrios[Math.floor(Math.random() * bestTrios.length)];

                      const otherPlayers = [randomTrio.player1, randomTrio.player2, randomTrio.player3]
                        .filter(id => id !== parseInt(selectedPlayer))
                        .map(id => getPlayerName(id))
                        .join(", ");

                      return (
                        <div key={`best-trio-${randomTrio.player1}-${randomTrio.player2}-${randomTrio.player3}`}>
                          <div className="font-medium text-sm">{otherPlayers}</div>
                          <div className="text-xs mt-1">
                            <Badge variant="outline" className={getWinRateBadgeClass(randomTrio.winRate)}>
                              {randomTrio.winRate.toFixed(1)}% ({randomTrio.played} {t('matches.games')})
                            </Badge>
                          </div>
                        </div>
                      );
                    })()}
                  </div>

                  <div className="bg-gray-50 dark:bg-gray-800 p-3 rounded-md border dark:border-gray-700">
                    <div className="text-xs text-muted-foreground mb-1">{t('chemistry.bestQuad')}</div>
                    {(() => {
                      // Get all best quads with the same win rate
                      const filteredQuads = quadData
                        .filter(quad =>
                          quad.player1 === parseInt(selectedPlayer) ||
                          quad.player2 === parseInt(selectedPlayer) ||
                          quad.player3 === parseInt(selectedPlayer) ||
                          quad.player4 === parseInt(selectedPlayer)
                        )
                        .filter(quad => quad.played >= 2)
                        .sort((a, b) => b.winRate - a.winRate);

                      // If there are no quads, return null
                      if (filteredQuads.length === 0) return null;

                      // Get the highest win rate
                      const highestWinRate = filteredQuads[0].winRate;

                      // Get all quads with the highest win rate
                      const bestQuads = filteredQuads.filter(quad => quad.winRate === highestWinRate);

                      // Select one at random
                      const randomQuad = bestQuads[Math.floor(Math.random() * bestQuads.length)];

                      const otherPlayers = [randomQuad.player1, randomQuad.player2, randomQuad.player3, randomQuad.player4]
                        .filter(id => id !== parseInt(selectedPlayer))
                        .map(id => getPlayerName(id))
                        .join(", ");

                      return (
                        <div key={`best-quad-${randomQuad.player1}-${randomQuad.player2}-${randomQuad.player3}-${randomQuad.player4}`}>
                          <div className="font-medium text-sm">{otherPlayers}</div>
                          <div className="text-xs mt-1">
                            <Badge variant="outline" className={getWinRateBadgeClass(randomQuad.winRate)}>
                              {randomQuad.winRate.toFixed(1)}% ({randomQuad.played} {t('matches.games')})
                            </Badge>
                          </div>
                        </div>
                      );
                    })()}
                  </div>
                </div>
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </SharedViewLayout>
  );
};

export default SharedChemistryPage;
