import { useState, useMemo, useEffect } from "react";
import { useParams } from "react-router-dom";
import { supabase } from "@/lib/supabase";
import SharedViewLayout from "@/components/layout/SharedViewLayout";
import { useSharedLink } from "@/context/SharedLinkContext";
import StatCard from "@/components/StatCard";
import {
  Users, Calendar, TrendingUp, Percent, User, ShieldCheck, Trophy, History, Sparkles, Search
} from "lucide-react";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle
} from "@/components/ui/card";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { useTranslation } from "react-i18next";
import { Label } from "@/components/ui/label";
import { format, parseISO } from "date-fns";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { useToast } from "@/hooks/use-toast";

// --- Interfaces ---
interface Player {
  id: number;
  name: string;
  skills: number;
  effort: number;
  stamina: number;
  group_id?: string;
}

interface Match {
  id: number;
  date: Date;
  teamA: number[];
  teamB: number[];
  scoreA: number | null;
  scoreB: number | null;
  winner?: 'A' | 'B' | 'Draw';
  goalscorers?: Goalscorer[];
  youtubeLink?: string;
  group_id?: string;
}

interface Goalscorer {
  team: 'A' | 'B';
  playerId: number;
}

interface PlayerStats {
  id: number;
  name: string;
  played: number;
  wins: number;
  draws: number;
  losses: number;
  winRate: number;
  rating: number;
}

// --- Helper Functions ---
const calculateAverageRating = (player: Omit<Player, 'id' | 'name'>): number => {
  if (!player || typeof player.skills !== 'number' || typeof player.effort !== 'number' || typeof player.stamina !== 'number') return 0;
  return Math.round((player.skills + player.effort + player.stamina) / 3);
};

const SharedDashboardPage = () => {
  const { groupId } = useParams<{ groupId: string }>();
  const { toast } = useToast();
  const { accessLevel } = useSharedLink();
  const { t } = useTranslation();
  const [selectedPlayerId, setSelectedPlayerId] = useState<string>("");
  const [minGamesForWinRate, setMinGamesForWinRate] = useState<string>("0");
  const [loading, setLoading] = useState(true);
  const [players, setPlayers] = useState<Player[]>([]);
  const [matches, setMatches] = useState<Match[]>([]);
  const [groupName, setGroupName] = useState<string>("");

  // Fetch data on mount
  useEffect(() => {
    const fetchDashboardData = async () => {
      setLoading(true);

      if (!groupId) {
        console.warn("No group ID provided");
        setLoading(false);
        return;
      }

      try {
        // Fetch group info first to get the name
        const { data: groupData, error: groupError } = await supabase
          .from('friend_groups')
          .select('name')
          .eq('id', groupId)
          .single();

        if (groupError) throw groupError;
        if (groupData) {
          setGroupName(groupData.name);
        }

        // Fetch matches data
        const { data: matchesData, error: matchesError } = await supabase
          .from('matches')
          .select('*')
          .eq('group_id', groupId)
          .order('match_date', { ascending: false });

        if (matchesError) throw matchesError;

        // Transform match data
        const transformedMatches = (matchesData || []).map(m => ({
          id: m.id,
          date: parseISO(m.match_date),
          teamA: m.teama,
          teamB: m.teamb,
          scoreA: m.scorea,
          scoreB: m.scoreb,
          winner: m.winner,
          goalscorers: m.goalscorers,
          youtubeLink: m.youtubelink,
          group_id: m.group_id
        }));

        // Fetch players data
        const { data: playersData, error: playersError } = await supabase
          .from('players')
          .select('*')
          .eq('group_id', groupId)
          .order('name');

        if (playersError) throw playersError;

        setMatches(transformedMatches);
        setPlayers(playersData || []);
      } catch (error: any) {
        console.error("Error fetching dashboard data:", error);
        toast({
          title: t('common.error'),
          description: error?.message || t('dashboard.fetchError'),
          variant: "destructive"
        });
      } finally {
        setLoading(false);
      }
    };

    fetchDashboardData();
  }, [groupId, toast]);

  // Calculate player stats with win rate
  const playerStatsWithWinRate = useMemo(() => {
    const stats: { [key: number]: PlayerStats } = {};

    // Initialize stats for all players
    players.forEach(player => {
      stats[player.id] = {
        id: player.id,
        name: player.name,
        played: 0,
        wins: 0,
        draws: 0,
        losses: 0,
        winRate: 0,
        rating: calculateAverageRating(player)
      };
    });

    // Calculate stats from matches
    matches.forEach(match => {
      if (match.scoreA === null || match.scoreB === null) return;

      const winner = match.scoreA > match.scoreB ? 'A' : match.scoreB > match.scoreA ? 'B' : 'Draw';

      // Process Team A players
      match.teamA.forEach(playerId => {
        if (!stats[playerId]) return;

        stats[playerId].played++;
        if (winner === 'A') stats[playerId].wins++;
        else if (winner === 'Draw') stats[playerId].draws++;
        else stats[playerId].losses++;
      });

      // Process Team B players
      match.teamB.forEach(playerId => {
        if (!stats[playerId]) return;

        stats[playerId].played++;
        if (winner === 'B') stats[playerId].wins++;
        else if (winner === 'Draw') stats[playerId].draws++;
        else stats[playerId].losses++;
      });
    });

    // Calculate win rates
    Object.values(stats).forEach(player => {
      player.winRate = player.played > 0 ? (player.wins / player.played) * 100 : 0;
    });

    return Object.values(stats);
  }, [players, matches]);

  // Filter players by minimum games played
  const filteredPlayerStats = useMemo(() => {
    const minGames = parseInt(minGamesForWinRate) || 0;
    return playerStatsWithWinRate.filter(player => player.played >= minGames);
  }, [playerStatsWithWinRate, minGamesForWinRate]);

  // Get top win rate player
  const topWinRatePlayer = useMemo(() => {
    if (filteredPlayerStats.length === 0) return null;
    return [...filteredPlayerStats].sort((a, b) => b.winRate - a.winRate)[0];
  }, [filteredPlayerStats]);

  // Get selected player data
  const selectedPlayerData = useMemo(() => {
    if (!selectedPlayerId) return null;
    const playerId = parseInt(selectedPlayerId);
    return playerStatsWithWinRate.find(p => p.id === playerId) || null;
  }, [selectedPlayerId, playerStatsWithWinRate]);

  if (loading) {
    return (
      <SharedViewLayout title={t('nav.dashboard')} groupName={groupName} accessLevel={accessLevel}>
        <div className="flex items-center justify-center min-h-[400px]">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-soccer-primary" />
        </div>
      </SharedViewLayout>
    );
  }

  return (
    <SharedViewLayout title={t('nav.dashboard')} groupName={groupName} accessLevel={accessLevel}>
      <div className="grid gap-4 grid-cols-1 md:grid-cols-2 lg:grid-cols-4 mb-6">
        <StatCard title={t('dashboard.totalPlayers')} value={players.length.toString()} icon={<Users className="h-4 w-4" />} />
        <StatCard title={t('dashboard.totalMatches')} value={matches.length.toString()} icon={<Calendar className="h-4 w-4" />} />
        <div className="col-span-1 md:col-span-2 lg:col-span-2 flex flex-col">
          <StatCard
            title={t('dashboard.highestWinRate')}
            value={topWinRatePlayer ? topWinRatePlayer.name : t('common.na')}
            icon={<Percent className="h-4 w-4" />}
            trend="neutral"
            trendValue={topWinRatePlayer ? `${topWinRatePlayer.winRate.toFixed(1)}% (${topWinRatePlayer.played} ${t('matches.games')})` : t('common.na')}
          />
          <div className="flex items-center gap-2 mt-1.5 pl-1">
            <Label htmlFor="minGames" className="text-xs">{t('leaderboard.minGames')}:</Label>
            <Select value={minGamesForWinRate} onValueChange={setMinGamesForWinRate}>
              <SelectTrigger id="minGames" className="h-7 w-16 text-xs">
                <SelectValue placeholder="0" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="0">{t('common.all')}</SelectItem>
                <SelectItem value="3">3+</SelectItem>
                <SelectItem value="5">5+</SelectItem>
                <SelectItem value="10">10+</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>
      </div>

      <Card className="mb-6">
        <div className="p-4">
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-3">
            <div className="font-medium text-lg">{t('dashboard.playerStats')}</div>
            <div className="flex flex-col sm:flex-row items-start sm:items-center gap-3">
              <div className="relative w-full sm:w-[200px]">
                <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
                <Input
                  type="text"
                  placeholder={t('dashboard.searchPlayers')}
                  className="pl-8 h-9 text-sm w-full"
                  onChange={(e) => {
                    // Filter the dropdown options based on search
                    const searchTerm = e.target.value.toLowerCase();
                    if (searchTerm && playerStatsWithWinRate.length > 0) {
                      const matchedPlayer = playerStatsWithWinRate.find(p =>
                        p.name.toLowerCase().includes(searchTerm)
                      );
                      if (matchedPlayer) {
                        setSelectedPlayerId(matchedPlayer.id.toString());
                      }
                    }
                  }}
                />
              </div>
              <div className="flex items-center gap-2 w-full sm:w-auto">
                <Select value={selectedPlayerId} onValueChange={setSelectedPlayerId}>
                  <SelectTrigger id="playerSelect" className="w-full sm:w-[150px] h-9">
                    <SelectValue placeholder={t('players.playerSelected')} />
                  </SelectTrigger>
                  <SelectContent>
                    {playerStatsWithWinRate
                      .sort((a, b) => b.played - a.played)
                      .map(player => (
                        <SelectItem key={player.id} value={player.id.toString()}>
                          {player.name} ({player.played})
                        </SelectItem>
                      ))}
                  </SelectContent>
                </Select>
                <User className="h-5 w-5 text-muted-foreground" />
              </div>
            </div>
          </div>
        </div>
      </Card>

      {selectedPlayerData && (
        <Card className="mb-6 bg-gradient-to-r from-blue-50 to-indigo-50 border-blue-200 dark:bg-gradient-to-r dark:from-gray-800 dark:to-gray-900 dark:border-gray-700 transition-colors duration-200">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <User className="h-5 w-5 text-blue-700 dark:text-blue-400" />
              {selectedPlayerData.name} - {t('players.playerSelected')}
            </CardTitle>
            <CardDescription className="dark:text-gray-400">{t('players.viewingStatsFor', { name: selectedPlayerData.name })}</CardDescription>
          </CardHeader>
          <CardContent className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-x-6 gap-y-4 text-sm">
            <div className="flex items-center gap-2">
              <ShieldCheck className="h-4 w-4 text-green-600 dark:text-green-400" />
              <span className="text-gray-500 dark:text-gray-400">{t('players.winRate')}:</span>
              <span className="font-medium dark:text-gray-300">{selectedPlayerData.winRate.toFixed(1)}%</span>
            </div>
            <div className="flex items-center gap-2">
              <Trophy className="h-4 w-4 text-amber-500 dark:text-amber-400" />
              <span className="text-gray-500 dark:text-gray-400">{t('players.won')}:</span>
              <span className="font-medium dark:text-gray-300">{selectedPlayerData.wins}</span>
            </div>
            <div className="flex items-center gap-2">
              <History className="h-4 w-4 text-blue-500 dark:text-blue-400" />
              <span className="text-gray-500 dark:text-gray-400">{t('players.played')}:</span>
              <span className="font-medium dark:text-gray-300">{selectedPlayerData.played}</span>
            </div>
            <div className="flex items-center gap-2">
              <TrendingUp className="h-4 w-4 text-purple-500 dark:text-purple-400" />
              <span className="text-gray-500 dark:text-gray-400">{t('players.rating')}:</span>
              <span className="font-medium dark:text-gray-300">{selectedPlayerData.rating}</span>
            </div>
            <div className="flex items-center gap-2">
              <Sparkles className="h-4 w-4 text-indigo-500 dark:text-indigo-400" />
              <span className="text-gray-500 dark:text-gray-400">{t('matches.result')}:</span>
              <span className="font-medium dark:text-gray-300">
                {matches
                  .filter(m => m.teamA.includes(selectedPlayerData.id) || m.teamB.includes(selectedPlayerData.id))
                  .slice(0, 5)
                  .map(m => {
                    const isTeamA = m.teamA.includes(selectedPlayerData.id);
                    const isWin = (isTeamA && m.winner === 'A') || (!isTeamA && m.winner === 'B');
                    const isDraw = m.winner === 'Draw';
                    return (
                      <Badge key={m.id} variant={isWin ? "default" : isDraw ? "outline" : "destructive"} className={`mr-1 ${isDraw && 'dark:border-gray-600 dark:text-gray-300'}`}>
                        {isWin ? 'W' : isDraw ? 'D' : 'L'}
                      </Badge>
                    );
                  })}
              </span>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Dashboard Content */}
      <div className="grid gap-4 grid-cols-1 md:grid-cols-2 mt-6">
        {/* Leaderboard Preview */}
        <Card className="col-span-1">
          <CardHeader className="pb-2">
            <CardTitle className="text-lg">{t('leaderboard.title')}</CardTitle>
            <CardDescription>{t('players.averageRating')}</CardDescription>
          </CardHeader>
          <CardContent>
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>{t('leaderboard.rank')}</TableHead>
                  <TableHead>{t('players.name')}</TableHead>
                  <TableHead className="text-right">{t('players.rating')}</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {[...players]
                  .sort((a, b) => calculateAverageRating(b) - calculateAverageRating(a))
                  .slice(0, 5)
                  .map((player, index) => (
                    <TableRow key={player.id}>
                      <TableCell className="font-medium">{index + 1}</TableCell>
                      <TableCell>{player.name}</TableCell>
                      <TableCell className="text-right">{calculateAverageRating(player)}</TableCell>
                    </TableRow>
                  ))}
              </TableBody>
            </Table>
          </CardContent>
        </Card>

        {/* Recent Matches */}
        <Card className="col-span-1">
          <CardHeader className="pb-2">
            <CardTitle className="text-lg">{t('dashboard.recentMatchesTitle')}</CardTitle>
            <CardDescription>{t('dashboard.recentMatchesDescription')}</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {matches.slice(0, 3).map((match) => (
                <div key={match.id} className="border rounded-md p-3 bg-gray-50 dark:bg-gray-800 dark:border-gray-700">
                  <div className="flex justify-between items-center mb-2">
                    <span className="text-xs text-gray-500 dark:text-gray-400">
                      {format(match.date, "MMM d, yyyy")}
                    </span>
                    <Badge variant={match.winner === 'Draw' ? "outline" : "default"}>
                      {match.winner === 'Draw'
                        ? t('matches.draw')
                        : match.winner === 'A'
                          ? t('matches.teamAWon')
                          : t('matches.teamBWon')}
                    </Badge>
                  </div>
                  <div className="flex justify-between items-center">
                    <div className="flex-1">
                      <div className="font-medium">{t('matches.teamA')}</div>
                      <div className="text-sm text-gray-500 dark:text-gray-400">
                        {match.teamA.map(id => {
                          const player = players.find(p => p.id === id);
                          return player ? player.name : `${t('players.name')} ${id}`;
                        }).join(", ")}
                      </div>
                    </div>
                    <div className="px-4 font-bold text-lg">
                      {match.scoreA} - {match.scoreB}
                    </div>
                    <div className="flex-1 text-right">
                      <div className="font-medium">{t('matches.teamB')}</div>
                      <div className="text-sm text-gray-500 dark:text-gray-400">
                        {match.teamB.map(id => {
                          const player = players.find(p => p.id === id);
                          return player ? player.name : `${t('players.name')} ${id}`;
                        }).join(", ")}
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>
    </SharedViewLayout>
  );
};

export default SharedDashboardPage;
