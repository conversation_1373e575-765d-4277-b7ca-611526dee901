import { useState, useEffect } from "react";
import { useParams, useNavigate } from "react-router-dom";
import { supabase } from "@/lib/supabase";
import { useToast } from "@/hooks/use-toast";
import AuthThemeToggle from "@/components/auth/AuthThemeToggle";

const SharedEntryPage = () => {
  const { groupId } = useParams<{ groupId: string }>();
  const navigate = useNavigate();
  const { toast } = useToast();
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const validateGroup = async () => {
      if (!groupId) {
        navigate('/');
        return;
      }

      try {
        // Check if the group exists
        const { data, error } = await supabase
          .from('friend_groups')
          .select('id, name')
          .eq('id', groupId)
          .single();

        if (error || !data) {
          toast({
            title: "Invalid Group",
            description: "The shared group link is invalid or has expired.",
            variant: "destructive"
          });
          navigate('/');
          return;
        }

        // Group exists, redirect to the dashboard
        navigate(`/shared/${groupId}/dashboard`);
      } catch (error) {
        console.error("Error validating group:", error);
        toast({
          title: "Error",
          description: "Failed to validate the shared group link.",
          variant: "destructive"
        });
        navigate('/');
      } finally {
        setLoading(false);
      }
    };

    validateGroup();
  }, [groupId, navigate, toast]);

  return (
    <div className="flex items-center justify-center min-h-screen bg-gray-50 dark:bg-soccer-dark transition-colors duration-200">
      <AuthThemeToggle />
      <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-soccer-primary" />
    </div>
  );
};

export default SharedEntryPage;
