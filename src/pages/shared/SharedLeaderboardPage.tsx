import { useState, useEffect, use<PERSON><PERSON><PERSON> } from "react";
import { useParams } from "react-router-dom";
import SharedViewLayout from "@/components/layout/SharedViewLayout";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { MobileTable } from "@/components/ui/mobile-table";
import { CompactMobileTable } from "@/components/ui/compact-mobile-table";
import { useMediaQuery } from "@/hooks/use-media-query";
import {
  Ta<PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  Ta<PERSON>List,
  TabsTrigger,
} from "@/components/ui/tabs";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { supabase } from "@/lib/supabase";
import { useToast } from "@/hooks/use-toast";
import { <PERSON>, Medal, Trophy, Tren<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "lucide-react";
import { useTranslation } from "react-i18next";

// --- Interfaces ---
interface Player {
  id: number;
  name: string;
  skills: number;
  effort: number;
  stamina: number;
  group_id?: string;
  rating?: number; // Calculated client-side
}

interface Match {
  id: number;
  match_date: string;
  teama: number[];
  teamb: number[];
  scorea: number | null;
  scoreb: number | null;
  winner?: 'A' | 'B' | 'Draw';
  group_id?: string;
}

interface PlayerStats {
  id: number;
  name: string;
  rating: number;
  played: number;
  wins: number;
  draws: number;
  losses: number;
  winRate: number;
  goalsScored?: number;
}

// --- Helper Functions ---
const calculatePlayerRating = (player: Player): number => {
  return Math.round((player.skills + player.effort + player.stamina) / 3);
};

const SharedLeaderboardPage = () => {
  const { groupId } = useParams<{ groupId: string }>();
  const [players, setPlayers] = useState<Player[]>([]);
  const [matches, setMatches] = useState<Match[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState("");
  const [activeTab, setActiveTab] = useState("winrate"); // Default to winrate
  const [minGames, setMinGames] = useState<number>(3); // Default minimum games filter
  const [groupName, setGroupName] = useState<string>("");
  const { toast } = useToast();
  const { t } = useTranslation();
  const isMobile = useMediaQuery("(max-width: 768px)");

  // Fetch data on mount
  useEffect(() => {
    const fetchData = async () => {
      setLoading(true);

      if (!groupId) {
        console.warn("No group ID provided");
        setLoading(false);
        return;
      }

      try {
        // Fetch group info first to get the name
        const { data: groupData, error: groupError } = await supabase
          .from('friend_groups')
          .select('name')
          .eq('id', groupId)
          .single();

        if (groupError) throw groupError;
        if (groupData) {
          setGroupName(groupData.name);
        }

        // Fetch players and matches data
        const [{ data: playersData, error: playersError }, { data: matchesData, error: matchesError }] = await Promise.all([
          supabase.from('players').select('*').eq('group_id', groupId),
          supabase.from('matches').select('*').eq('group_id', groupId)
        ]);

        if (playersError) throw playersError;
        if (matchesError) throw matchesError;

        // Calculate rating for each player
        const playersWithRating = (playersData || []).map(player => ({
          ...player,
          rating: calculatePlayerRating(player)
        }));

        setPlayers(playersWithRating);
        setMatches(matchesData || []);
      } catch (error) {
        console.error('Error fetching data:', error);
        toast({
          title: t('common.error'),
          description: t('leaderboard.loadError'),
          variant: 'destructive',
        });
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [groupId, toast]);

  // Calculate player stats
  const playerStats = useMemo(() => {
    const stats: { [key: number]: PlayerStats } = {};

    // Initialize stats for all players
    players.forEach(player => {
      stats[player.id] = {
        id: player.id,
        name: player.name,
        rating: player.rating || calculatePlayerRating(player),
        played: 0,
        wins: 0,
        draws: 0,
        losses: 0,
        winRate: 0,
        goalsScored: 0
      };
    });

    // Calculate stats from matches
    matches.forEach(match => {
      if (match.scorea === null || match.scoreb === null) return;

      const winner = match.winner || (match.scorea > match.scoreb ? 'A' : match.scoreb > match.scorea ? 'B' : 'Draw');

      // Process Team A players
      match.teama.forEach(playerId => {
        if (!stats[playerId]) return;

        stats[playerId].played++;
        if (winner === 'A') stats[playerId].wins++;
        else if (winner === 'Draw') stats[playerId].draws++;
        else stats[playerId].losses++;
      });

      // Process Team B players
      match.teamb.forEach(playerId => {
        if (!stats[playerId]) return;

        stats[playerId].played++;
        if (winner === 'B') stats[playerId].wins++;
        else if (winner === 'Draw') stats[playerId].draws++;
        else stats[playerId].losses++;
      });
    });

    // Calculate win rates
    Object.values(stats).forEach(player => {
      player.winRate = player.played > 0 ? (player.wins / player.played) * 100 : 0;
    });

    return Object.values(stats);
  }, [players, matches]);

  // Filter players based on search query and minimum games
  const filteredPlayerStats = useMemo(() => {
    return playerStats.filter(player =>
      player.name.toLowerCase().includes(searchQuery.toLowerCase()) &&
      player.played >= minGames
    );
  }, [playerStats, searchQuery, minGames]);

  // Sort players based on active tab
  const sortedPlayerStats = useMemo(() => {
    switch (activeTab) {
      case "rating":
        return [...filteredPlayerStats].sort((a, b) => b.rating - a.rating);
      case "winrate":
        return [...filteredPlayerStats].sort((a, b) => b.winRate - a.winRate);
      case "games":
        return [...filteredPlayerStats].sort((a, b) => b.played - a.played);
      default:
        return filteredPlayerStats;
    }
  }, [filteredPlayerStats, activeTab]);

  return (
    <SharedViewLayout title={t('leaderboard.title')} groupName={groupName}>
      <div className="flex flex-col md:flex-row justify-between gap-4 mb-6">
        <div className="flex flex-col sm:flex-row gap-4">
          <div className="relative">
            <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder={t('players.searchPlayers')}
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-8 w-full md:w-[300px]"
            />
          </div>
          <div className="flex gap-2 items-center">
            <span className="text-sm whitespace-nowrap">{t('leaderboard.minGames')}:</span>
            <Select
              value={minGames.toString()}
              onValueChange={(value) => setMinGames(parseInt(value))}
            >
              <SelectTrigger className="w-[100px]">
                <SelectValue placeholder={t('leaderboard.minGamesPlaceholder')} />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="0">{t('common.all')}</SelectItem>
                <SelectItem value="3">3+</SelectItem>
                <SelectItem value="5">5+</SelectItem>
                <SelectItem value="10">10+</SelectItem>
                <SelectItem value="15">15+</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>

        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full md:w-auto">
          <TabsList>
            <TabsTrigger value="rating" className="flex items-center gap-1">
              <Medal className="h-4 w-4" />
              <span className="hidden sm:inline">{t('players.rating')}</span>
            </TabsTrigger>
            <TabsTrigger value="winrate" className="flex items-center gap-1">
              <Percent className="h-4 w-4" />
              <span className="hidden sm:inline">{t('players.winRate')}</span>
            </TabsTrigger>
            <TabsTrigger value="games" className="flex items-center gap-1">
              <BarChart3 className="h-4 w-4" />
              <span className="hidden sm:inline">{t('matches.games')}</span>
            </TabsTrigger>
          </TabsList>
        </Tabs>
      </div>

      {loading ? (
        <div className="flex items-center justify-center min-h-[400px]">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-soccer-primary" />
        </div>
      ) : sortedPlayerStats.length === 0 ? (
        <div className="text-center py-10 text-muted-foreground">
          {searchQuery ? t('leaderboard.noPlayersFound') : t('players.noPlayersFound')}
        </div>
      ) : isMobile ? (
        <MobileTable
          data={sortedPlayerStats}
          columns={[
            {
              header: t('leaderboard.rank', '#'),
              accessorKey: 'id' as keyof PlayerStats,
              priority: "high",
              cell: (player: PlayerStats) => {
                const idx = sortedPlayerStats.findIndex(p => p.id === player.id);
                return (
                  <div className="flex items-center justify-center">
                    <div className="flex items-center justify-center w-5 h-5 rounded-full border-2 border-soccer-primary dark:border-soccer-primary bg-transparent">
                      <span className="text-soccer-primary dark:text-soccer-primary text-xs font-medium">{idx + 1}</span>
                    </div>
                  </div>
                );
              },
              meta: { align: "center", width: "10%" }
            },
            {
              header: t('players.name'),
              accessorKey: 'name',
              priority: "high",
              cell: (player: PlayerStats) => (
                <span className="text-xs font-medium truncate max-w-[60px] block">{player.name}</span>
              ),
              meta: { align: "left", width: "20%" }
            },
            {
              header: t('leaderboard.w', 'W'),
              accessorKey: 'wins',
              priority: "high",
              cell: (player: PlayerStats) => (
                <span className="text-green-600 font-medium text-xs">{player.wins}</span>
              ),
              meta: { align: "center", width: "8%" }
            },
            {
              header: t('leaderboard.d', 'D'),
              accessorKey: 'draws',
              priority: "high",
              cell: (player: PlayerStats) => (
                <span className="text-gray-500 font-medium text-xs">{player.draws}</span>
              ),
              meta: { align: "center", width: "8%" }
            },
            {
              header: t('leaderboard.l', 'L'),
              accessorKey: 'losses',
              priority: "high",
              cell: (player: PlayerStats) => (
                <span className="text-red-600 font-medium text-xs">{player.losses}</span>
              ),
              meta: { align: "center", width: "8%" }
            },
            {
              header: t('leaderboard.winRate', 'Win%'),
              accessorKey: 'winRate',
              priority: "high",
              cell: (player: PlayerStats) => (
                <span className={`text-xs font-medium ${
                  player.played < 3 ? "text-gray-500" :
                  player.winRate >= 70 ? "text-green-600" :
                  player.winRate >= 50 ? "text-amber-600" :
                  "text-red-600"
                }`}>
                  {player.played < 3 ? "N/A" : `${player.winRate.toFixed(0)}%`}
                </span>
              ),
              meta: { align: "center", width: "15%" }
            },
            {
              header: t('players.rating'),
              accessorKey: 'rating',
              priority: "high",
              cell: (player: PlayerStats) => (
                <div className="flex items-center justify-center">
                  <div className="bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-100 rounded-full px-1.5 py-0.5 text-xs font-medium">
                    {player.rating}
                  </div>
                </div>
              ),
              meta: { align: "center", width: "15%" }
            }
          ]}
          keyExtractor={(player) => player.id}
          emptyState={
            <div className="text-center py-8 text-muted-foreground">
              {searchQuery ? t('leaderboard.noPlayersFound') : t('players.noPlayersFound')}
            </div>
          }
          isLoading={loading}
          rowClassName={(player) => ""}
        />
      ) : (
        <div className="rounded-md border overflow-hidden">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead className="w-[60px]">{t('leaderboard.rank')}</TableHead>
                <TableHead>{t('players.name')}</TableHead>
                <TableHead className="text-center">{t('players.rating')}</TableHead>
                <TableHead className="text-center">{t('players.played')}</TableHead>
                <TableHead className="text-center">{t('leaderboard.w')}</TableHead>
                <TableHead className="text-center">{t('leaderboard.d')}</TableHead>
                <TableHead className="text-center">{t('leaderboard.l')}</TableHead>
                <TableHead className="text-right">{t('players.winRate')}</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {sortedPlayerStats.map((player, index) => (
                <TableRow key={player.id}>
                  <TableCell className="font-medium">
                    <div className="flex justify-center items-center w-8 h-8 rounded-full border-2 border-soccer-primary dark:border-soccer-primary bg-transparent">
                      <span className="text-soccer-primary dark:text-soccer-primary font-medium">{index + 1}</span>
                    </div>
                  </TableCell>
                  <TableCell>{player.name}</TableCell>
                  <TableCell className="text-center">
                    <Badge variant="outline" className="bg-blue-50 dark:bg-blue-900 dark:text-blue-100">
                      {player.rating}
                    </Badge>
                  </TableCell>
                  <TableCell className="text-center">{player.played}</TableCell>
                  <TableCell className="text-center">{player.wins}</TableCell>
                  <TableCell className="text-center">{player.draws}</TableCell>
                  <TableCell className="text-center">{player.losses}</TableCell>
                  <TableCell className="text-right">
                    <Badge
                      variant="outline"
                      className={
                        player.played < 3 ? "bg-gray-100 dark:bg-gray-800 dark:text-gray-300" :
                        player.winRate >= 70 ? "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-100" :
                        player.winRate >= 50 ? "bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-100" :
                        player.winRate >= 30 ? "bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-100" :
                        "bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-100"
                      }
                    >
                      {player.played < 3 ? "N/A" : `${player.winRate.toFixed(1)}%`}
                    </Badge>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </div>
      )}
    </SharedViewLayout>
  );
};

export default SharedLeaderboardPage;
