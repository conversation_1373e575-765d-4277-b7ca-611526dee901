import { useState, useEffect } from "react";
import { useParams } from "react-router-dom";
import SharedViewLayout from "@/components/layout/SharedViewLayout";
import { Input } from "@/components/ui/input";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { format, parseISO } from "date-fns";
import { useToast } from "@/hooks/use-toast";
import { supabase } from "@/lib/supabase";
import {
  Trophy,
  Calendar,
  Youtube,
  Users,
  Search,
} from "lucide-react";
import { useTranslation } from "react-i18next";

// --- Interfaces ---
interface Player { id: number; name: string; skills?: number; effort?: number; stamina?: number; }
interface Goalscorer { team: 'A' | 'B'; playerId: number; }
interface Match {
  id: number;
  created_at?: string;
  match_date: string | Date;
  teama: number[];
  teamb: number[];
  scorea: number | null;
  scoreb: number | null;
  winner?: 'A' | 'B' | 'Draw';
  goalscorers?: Goalscorer[];
  youtubelink?: string;
  group_id?: string;
}

const SharedMatchesPage = () => {
  const { groupId } = useParams<{ groupId: string }>();
  const [matches, setMatches] = useState<Match[]>([]);
  const [loading, setLoading] = useState(true);
  const [players, setPlayers] = useState<Player[]>([]);
  const [search, setSearch] = useState("");
  const [groupName, setGroupName] = useState<string>("");
  const { toast } = useToast();
  const { t } = useTranslation();

  // Fetch matches from Supabase on mount
  useEffect(() => {
    const fetchData = async () => {
      setLoading(true);

      if (!groupId) {
        console.warn("No group ID provided");
        setLoading(false);
        return;
      }

      try {
        // Fetch group info first to get the name
        const { data: groupData, error: groupError } = await supabase
          .from('friend_groups')
          .select('name')
          .eq('id', groupId)
          .single();

        if (groupError) throw groupError;
        if (groupData) {
          setGroupName(groupData.name);
        }

        // Fetch matches data
        const { data: matchesData, error: matchesError } = await supabase
          .from('matches')
          .select('*')
          .eq('group_id', groupId)
          .order('match_date', { ascending: false });

        if (matchesError) throw matchesError;

        // Fetch players data for names
        const { data: playersData, error: playersError } = await supabase
          .from('players')
          .select('*')
          .eq('group_id', groupId);

        if (playersError) throw playersError;

        setMatches(matchesData || []);
        setPlayers(playersData || []);
      } catch (error) {
        console.error('Error fetching data:', error);
        toast({
          title: t('common.error'),
          description: t('matches.fetchError'),
          variant: 'destructive',
        });
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [groupId, toast]);

  // Get player name by ID
  const getPlayerName = (id: number) => {
    const player = players.find(p => p.id === id);
    return player ? player.name : `${t('players.name')} ${id}`;
  };

  // Filter matches based on search
  const filteredMatches = matches.filter(match => {
    if (!search) return true;

    const searchLower = search.toLowerCase();

    // Check if any player in team A or B matches the search
    const teamAPlayers = match.teama.map(id => getPlayerName(id).toLowerCase());
    const teamBPlayers = match.teamb.map(id => getPlayerName(id).toLowerCase());

    return teamAPlayers.some(name => name.includes(searchLower)) ||
           teamBPlayers.some(name => name.includes(searchLower));
  });

  return (
    <SharedViewLayout title={t('matches.title')} groupName={groupName}>
      {/* Search Bar */}
      <div className="mb-6 relative">
        <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
        <Input
          placeholder={t('matches.searchPlaceholder')}
          value={search}
          onChange={(e) => setSearch(e.target.value)}
          className="pl-8 w-full md:w-[300px]"
        />
      </div>

      {loading ? (
        <div className="flex items-center justify-center min-h-[400px]">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-soccer-primary" />
        </div>
      ) : filteredMatches.length === 0 ? (
        <div className="text-center py-10 text-muted-foreground">
          {search ? t('matches.noMatchesSearch') : t('matches.noMatchesFound')}
        </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {filteredMatches.map((match) => (
            <Card key={match.id} className="overflow-hidden">
              <CardHeader className="pb-2">
                <div className="flex justify-between items-start">
                  <div>
                    <CardTitle className="text-lg flex items-center gap-2">
                      <Calendar className="h-4 w-4 text-muted-foreground" />
                      {format(parseISO(match.match_date as string), "MMM d, yyyy")}
                    </CardTitle>
                    <CardDescription>
                      {match.winner === 'A' ? t('matches.teamAWon') :
                       match.winner === 'B' ? t('matches.teamBWon') : t('matches.draw')}
                    </CardDescription>
                  </div>
                  <Badge variant={match.winner === 'Draw' ? "outline" : "default"}>
                    {match.scorea} - {match.scoreb}
                  </Badge>
                </div>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-2 gap-4">
                  <div className={`p-2 rounded-md ${match.winner === 'A' ? 'bg-green-50 dark:bg-green-900/30' : match.winner === 'B' ? 'bg-red-50 dark:bg-red-900/30' : ''}`}>
                    <h4 className="text-sm font-medium mb-1 flex items-center gap-1">
                      <Trophy className={`h-3.5 w-3.5 ${match.winner === 'A' ? 'text-green-600 dark:text-green-400' : match.winner === 'B' ? 'text-red-600 dark:text-red-400' : 'text-gray-400'}`} />
                      {t('matches.teamA')}
                      {match.winner === 'A' && <span className="text-xs text-green-600 dark:text-green-400 ml-1">(Winner)</span>}
                    </h4>
                    <div className="text-sm text-muted-foreground">
                      <ul className="list-disc list-inside">
                        {match.teama.map(playerId => (
                          <li key={`team-a-${playerId}`}>{getPlayerName(playerId)}</li>
                        ))}
                      </ul>
                    </div>
                  </div>
                  <div className={`p-2 rounded-md ${match.winner === 'B' ? 'bg-green-50 dark:bg-green-900/30' : match.winner === 'A' ? 'bg-red-50 dark:bg-red-900/30' : ''}`}>
                    <h4 className="text-sm font-medium mb-1 flex items-center gap-1">
                      <Trophy className={`h-3.5 w-3.5 ${match.winner === 'B' ? 'text-green-600 dark:text-green-400' : match.winner === 'A' ? 'text-red-600 dark:text-red-400' : 'text-gray-400'}`} />
                      {t('matches.teamB')}
                      {match.winner === 'B' && <span className="text-xs text-green-600 dark:text-green-400 ml-1">(Winner)</span>}
                    </h4>
                    <div className="text-sm text-muted-foreground">
                      <ul className="list-disc list-inside">
                        {match.teamb.map(playerId => (
                          <li key={`team-b-${playerId}`}>{getPlayerName(playerId)}</li>
                        ))}
                      </ul>
                    </div>
                  </div>
                </div>

                {match.goalscorers && match.goalscorers.length > 0 && (
                  <div className="mt-4">
                    <h4 className="text-sm font-medium mb-1">{t('matches.goalscorers')}</h4>
                    <div className="text-sm text-muted-foreground">
                      <ul className="list-disc list-inside">
                        {match.goalscorers.map((goal, index) => (
                          <li key={`goal-${index}`}>
                            {getPlayerName(goal.playerId)} ({t('matches.team')} {goal.team})
                          </li>
                        ))}
                      </ul>
                    </div>
                  </div>
                )}

                {match.youtubelink && (
                  <div className="mt-4">
                    <a
                      href={match.youtubelink}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="text-sm text-soccer-primary hover:underline flex items-center gap-1"
                    >
                      <Youtube className="h-3.5 w-3.5" />
                      {t('matches.watchHighlights')}
                    </a>
                  </div>
                )}
              </CardContent>
            </Card>
          ))}
        </div>
      )}
    </SharedViewLayout>
  );
};

export default SharedMatchesPage;
