import { useState, useEffect } from "react";
import { useParams } from "react-router-dom";
import SharedViewLayout from "@/components/layout/SharedViewLayout";
import { Input } from "@/components/ui/input";
import { Footprints, BrainCog, TrendingUp, Star, Search } from "lucide-react";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { MobileTable } from "@/components/ui/mobile-table";
import { useMediaQuery } from "@/hooks/use-media-query";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";
import { supabase } from "@/lib/supabase";
import { useToast } from "@/hooks/use-toast";
import { calculatePlayerRating } from "@/utils/playerRating";
import { useTranslation } from "react-i18next";

// Player interface (matching Supabase table structure)
interface Player {
  id: number;
  created_at: string;
  name: string;
  skills: number;
  effort: number;
  stamina: number;
  group_id?: string;
  rating?: number; // Calculated client-side
}

const SharedPlayersPage = () => {
  const { groupId } = useParams<{ groupId: string }>();
  const [players, setPlayers] = useState<Player[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState("");
  const [groupName, setGroupName] = useState<string>("");
  const { toast } = useToast();
  const { t } = useTranslation();
  const isMobile = useMediaQuery("(max-width: 768px)");

  // Fetch players on mount
  useEffect(() => {
    const fetchPlayersData = async () => {
      setLoading(true);

      if (!groupId) {
        console.warn("No group ID provided");
        setLoading(false);
        return;
      }

      try {
        // Fetch group info first to get the name
        const { data: groupData, error: groupError } = await supabase
          .from('friend_groups')
          .select('name')
          .eq('id', groupId)
          .single();

        if (groupError) throw groupError;
        if (groupData) {
          setGroupName(groupData.name);
        }

        // Fetch players data
        const { data, error } = await supabase
          .from('players')
          .select('*')
          .eq('group_id', groupId)
          .order('name');

        if (error) throw error;

        // Calculate rating for each player
        const playersWithRating = (data || []).map(player => ({
          ...player,
          rating: calculatePlayerRating(player)
        }));

        setPlayers(playersWithRating);
      } catch (error) {
        console.error('Error fetching players:', error);
        toast({
          title: t('common.error'),
          description: t('players.fetchError'),
          variant: 'destructive',
        });
      } finally {
        setLoading(false);
      }
    };

    fetchPlayersData();
  }, [groupId, toast]);

  // Filter players based on search query
  const filteredPlayers = players.filter(player =>
    player.name.toLowerCase().includes(searchQuery.toLowerCase())
  );

  return (
    <SharedViewLayout title={t('players.title')} groupName={groupName}>
      {/* Search Bar */}
      <div className="mb-6 relative">
        <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
        <Input
          placeholder={t('players.searchPlayers')}
          value={searchQuery}
          onChange={(e) => setSearchQuery(e.target.value)}
          className="pl-8 w-full md:w-[300px]"
        />
      </div>

      {loading ? (
        <div className="flex items-center justify-center min-h-[400px]">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-soccer-primary" />
        </div>
      ) : filteredPlayers.length === 0 ? (
        <div className="text-center py-10 text-muted-foreground">
          {searchQuery ? t('players.noPlayersFound') : t('players.noPlayersFound')}
        </div>
      ) : isMobile ? (
        <MobileTable
          data={filteredPlayers}
          columns={[
            {
              header: t('players.name'),
              accessorKey: 'name' as keyof Player,
              priority: "high",
              cell: (player: Player) => (
                <span className="text-xs font-medium truncate max-w-[80px] block">{player.name}</span>
              ),
              meta: { align: "left", width: "40%" }
            },
            {
              header: t('players.skills', 'S'),
              accessorKey: 'skills' as keyof Player,
              priority: "high",
              cell: (player: Player) => (
                <div className="flex items-center justify-center">
                  <BrainCog className="h-3 w-3 text-blue-500 mr-1" />
                  <span className="text-xs">{player.skills}</span>
                </div>
              ),
              meta: { align: "center", width: "15%" }
            },
            {
              header: t('players.effort', 'E'),
              accessorKey: 'effort' as keyof Player,
              priority: "high",
              cell: (player: Player) => (
                <div className="flex items-center justify-center">
                  <Footprints className="h-3 w-3 text-green-500 mr-1" />
                  <span className="text-xs">{player.effort}</span>
                </div>
              ),
              meta: { align: "center", width: "15%" }
            },
            {
              header: t('players.stamina', 'S'),
              accessorKey: 'stamina' as keyof Player,
              priority: "high",
              cell: (player: Player) => (
                <div className="flex items-center justify-center">
                  <TrendingUp className="h-3 w-3 text-red-500 mr-1" />
                  <span className="text-xs">{player.stamina}</span>
                </div>
              ),
              meta: { align: "center", width: "15%" }
            },
            {
              header: t('players.rating'),
              accessorKey: 'rating' as keyof Player,
              priority: "high",
              cell: (player: Player) => (
                <div className="flex items-center justify-center">
                  <div className="bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-100 rounded-full px-2 py-0.5 text-xs font-medium">
                    {player.rating}
                  </div>
                </div>
              ),
              meta: { align: "center", width: "15%" }
            },
          ]}
          keyExtractor={(player) => player.id}
          emptyState={
            <div className="text-center py-8 text-muted-foreground">
              {searchQuery ? t('players.noPlayersFound') : t('players.noPlayersFound')}
            </div>
          }
          isLoading={loading}
        />
      ) : (
        <TooltipProvider>
          <div className="rounded-md border overflow-hidden">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>{t('players.name')}</TableHead>
                  <TableHead className="text-center">
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <div className="flex items-center justify-center">
                          <BrainCog className="h-4 w-4" />
                        </div>
                      </TooltipTrigger>
                      <TooltipContent>
                        <p>{t('players.skills')}</p>
                      </TooltipContent>
                    </Tooltip>
                  </TableHead>
                  <TableHead className="text-center">
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <div className="flex items-center justify-center">
                          <Footprints className="h-4 w-4" />
                        </div>
                      </TooltipTrigger>
                      <TooltipContent>
                        <p>{t('players.effort')}</p>
                      </TooltipContent>
                    </Tooltip>
                  </TableHead>
                  <TableHead className="text-center">
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <div className="flex items-center justify-center">
                          <TrendingUp className="h-4 w-4" />
                        </div>
                      </TooltipTrigger>
                      <TooltipContent>
                        <p>{t('players.stamina')}</p>
                      </TooltipContent>
                    </Tooltip>
                  </TableHead>
                  <TableHead className="text-center">
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <div className="flex items-center justify-center">
                          <Star className="h-4 w-4" />
                        </div>
                      </TooltipTrigger>
                      <TooltipContent>
                        <p>{t('players.overallRating')}</p>
                      </TooltipContent>
                    </Tooltip>
                  </TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredPlayers.map((player) => (
                  <TableRow key={player.id}>
                    <TableCell className="font-medium">{player.name}</TableCell>
                    <TableCell className="text-center">{player.skills}</TableCell>
                    <TableCell className="text-center">{player.effort}</TableCell>
                    <TableCell className="text-center">{player.stamina}</TableCell>
                    <TableCell className="text-center font-medium">{player.rating}</TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>
        </TooltipProvider>
      )}
    </SharedViewLayout>
  );
};

export default SharedPlayersPage;
