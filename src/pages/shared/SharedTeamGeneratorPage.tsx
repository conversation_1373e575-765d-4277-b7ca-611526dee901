import { useState, useEffect, useMemo } from "react";
import { useParams } from "react-router-dom";
import SharedViewLayout from "@/components/layout/SharedViewLayout";
import PlayerSelectionCard from "@/components/team-generator/PlayerSelectionCard";
import TeamsDisplayCard from "@/components/team-generator/TeamsDisplayCard";
import { useToast } from "@/hooks/use-toast";
import { supabase } from "@/lib/supabase";
import {
  Player,
  GameFormatKey,
  GenerationMode,
  calculateAverageRating,
  generateTeams as generateTeamsUtil
} from "@/utils/teamGenerator";

const SharedTeamGeneratorPage = () => {
  const { groupId } = useParams<{ groupId: string }>();
  const [players, setPlayers] = useState<Player[]>([]);
  const { toast } = useToast();
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedPlayers, setSelectedPlayers] = useState<number[]>([]);
  const [teamA, setTeamA] = useState<Player[]>([]);
  const [teamB, setTeamB] = useState<Player[]>([]);
  const [format, setFormat] = useState<GameFormatKey>("5v5");
  const [generationMode, setGenerationMode] = useState<GenerationMode>("balanced");
  const [groupName, setGroupName] = useState<string>("");

  // Load players on mount
  useEffect(() => {
    const fetchData = async () => {
      setLoading(true);

      if (!groupId) {
        console.warn("No group ID provided");
        setLoading(false);
        return;
      }

      try {
        // Fetch group info first to get the name
        const { data: groupData, error: groupError } = await supabase
          .from('friend_groups')
          .select('name')
          .eq('id', groupId)
          .single();

        if (groupError) throw groupError;
        if (groupData) {
          setGroupName(groupData.name);
        }

        // Get both players and matches to calculate games played
        const [{ data: playersData, error: playersError }, { data: matchesData }] = await Promise.all([
          supabase.from('players').select('*').eq('group_id', groupId).order('name'),
          supabase.from('matches').select('*').eq('group_id', groupId)
        ]);

        if (playersError) throw playersError;

        // Calculate played games for each player
        const playersWithStats = (playersData || []).map(p => {
          const gamesPlayed = (matchesData || []).filter(
            m => m.teama?.includes(p.id) || m.teamb?.includes(p.id)
          ).length;

          return {
            ...p,
            rating: calculateAverageRating(p),
            played: gamesPlayed
          };
        });

        setPlayers(playersWithStats);
      } catch (error) {
        console.error("Error fetching players:", error);
        toast({
          title: "Error loading players",
          description: "Failed to fetch player data",
          variant: "destructive"
        });
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [groupId, toast]);

  const handlePlayerToggle = (playerId: number) => {
    setSelectedPlayers((prev) =>
      prev.includes(playerId)
        ? prev.filter((id) => id !== playerId)
        : [...prev, playerId]
    );
  };

  const handleSelectAll = () => {
    if (selectedPlayers.length === players.length) {
      setSelectedPlayers([]);
    } else {
      setSelectedPlayers(players.map((p) => p.id));
    }
  };

  const generateTeams = () => {
    const playersNeeded = gameFormats[format].playersPerTeam * 2;

    if (selectedPlayers.length < playersNeeded) {
      toast({
        title: "Insufficient Players",
        description: `Please select at least ${playersNeeded} players for the ${gameFormats[format].title} format.`,
        variant: "destructive"
      });
      return;
    }

    if (selectedPlayers.length > playersNeeded) {
      toast({
        title: "Too Many Players",
        description: `Please select exactly ${playersNeeded} players for the ${gameFormats[format].title} format. You have ${selectedPlayers.length}.`,
        variant: "destructive"
      });
      return;
    }

    const { teamA: newTeamA, teamB: newTeamB } = generateTeamsUtil(
      selectedPlayers,
      players,
      format,
      generationMode
    );

    setTeamA(newTeamA);
    setTeamB(newTeamB);
  };

  const hasGeneratedTeams = teamA.length > 0 || teamB.length > 0;

  // Add filtered players logic
  const filteredAvailablePlayers = useMemo(() => {
    const searchTerms = searchQuery.toLowerCase().split(',').map(term => term.trim());
    return players.filter(player =>
      searchTerms.some(term => player.name.toLowerCase().includes(term))
    );
  }, [players, searchQuery]);

  // Game formats definition
  const gameFormats = {
    "5v5": { title: "5v5", playersPerTeam: 5 },
    "6v6": { title: "6v6", playersPerTeam: 6 },
    "7v7": { title: "7v7", playersPerTeam: 7 },
    "8v8": { title: "8v8", playersPerTeam: 8 },
    "11v11": { title: "11v11", playersPerTeam: 11 },
  };

  return (
    <SharedViewLayout title="Team Generator" groupName={groupName}>
      <div className="grid grid-cols-1 gap-6">
        <div>
          <PlayerSelectionCard
            players={filteredAvailablePlayers}
            selectedPlayers={selectedPlayers}
            format={format}
            gameFormats={gameFormats}
            generationMode={generationMode}
            searchQuery={searchQuery}
            onSearchChange={setSearchQuery}
            onPlayerToggle={handlePlayerToggle}
            onSelectAll={handleSelectAll}
            onFormatChange={(value) => setFormat(value as GameFormatKey)}
            onGenerationModeChange={setGenerationMode}
            onGenerateTeams={generateTeams}
          />
        </div>

        <div>
          <TeamsDisplayCard
            teamA={teamA}
            teamB={teamB}
            hasGeneratedTeams={hasGeneratedTeams}
            onSaveMatch={() => {
              toast({
                title: "View-only mode",
                description: "Saving matches is not available in view-only mode.",
                variant: "default"
              });
            }}
            viewOnly={true}
          />
        </div>
      </div>
    </SharedViewLayout>
  );
};

export default SharedTeamGeneratorPage;
