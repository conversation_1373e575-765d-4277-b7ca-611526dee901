import { render } from '@testing-library/react';
import { <PERSON>rowserRouter } from 'react-router-dom';
import { vi } from 'vitest';

interface WrapperProps {
  children: React.ReactNode;
}

export const TestWrapper = ({ children }: WrapperProps) => {
  return (
    <BrowserRouter>
      {children}
    </BrowserRouter>
  );
};

export function renderWithProviders(ui: React.ReactElement, { route = '/' } = {}) {
  window.history.pushState({}, 'Test page', route);
  
  return {
    ...render(ui, { wrapper: TestWrapper }),
  };
}

// API test helpers
export const mockApiResponse = <T,>(data: T) => ({
  data,
  error: null,
});

export const mockApiError = (message: string, code = 'ERROR') => ({
  data: null,
  error: { message, code },
});

export function renderWithRouter(ui: React.ReactElement, { route = '/' } = {}) {
  window.history.pushState({}, 'Test page', route);
  
  return {
    ...render(ui, { wrapper: <PERSON>rowserRouter }),
  };
}

export const mockToast = vi.fn();
vi.mock('@/hooks/use-toast', () => ({
  useToast: () => ({ toast: mockToast }),
}));
