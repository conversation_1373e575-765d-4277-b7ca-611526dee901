import { calculatePlayerRating } from "@/utils/playerRating";

export type Player = {
  id: number;
  name: string;
  wins: number;
  draws: number;
  losses: number;
  skills: number; // Rating out of 100 for skill
  effort: number; // Rating out of 100 for effort
  stamina: number; // Rating out of 100 for stamina
};

// Helper function to calculate derived stats
export const calculatePlayerStats = (player: Player) => {
  const played = player.wins + player.draws + player.losses;
  const rating = played > 0 ? calculatePlayerRating(player) : 0;
  const winRate = played > 0 ? Math.round(((player.wins + 0.5 * player.draws) / played) * 100) : 0;
  const winLossDraw = `${player.wins}/${player.draws}/${player.losses}`;

  return {
    ...player,
    played,
    rating,
    winRate,
    winLossDraw,
  };
};

// Define Match type (basic structure for now)
export type Match = {
  id: number;
  date: string; // Or Date object
  teamA: { name: string, score?: number, players: Player[] };
  teamB: { name: string, score?: number, players: Player[] };
  winner?: 'Team A' | 'Team B' | 'Draw';
  goalscorers?: { playerId: number, count: number }[];
  youtubeLink?: string;
};
