/**
 * Types for the sharing functionality
 */

// Access levels for shared links
export type AccessLevel = 'read' | 'comment';

// Shared link interface
export interface SharedLink {
  id: string;
  group_id: string;
  created_by: string;
  name: string;
  access_level: AccessLevel;
  expires_at: string | null;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

// Interface for creating a new shared link
export interface CreateSharedLinkParams {
  group_id: string;
  created_by: string;
  name: string;
  access_level: AccessLevel;
  expires_at: string | null;
  is_active: boolean;
}

// Interface for updating a shared link
export interface UpdateSharedLinkParams {
  name?: string;
  access_level?: AccessLevel;
  expires_at?: string | null;
  is_active?: boolean;
}

// Interface for shared link validation response
export interface SharedLinkValidationResponse {
  isValid: boolean;
  link?: SharedLink;
  group?: {
    id: string;
    name: string;
  };
  error?: string;
}
