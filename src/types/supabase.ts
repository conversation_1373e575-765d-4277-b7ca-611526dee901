export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: Json | undefined }
  | Json[]

export interface Database {
  public: {
    Tables: {
      players: {
        Row: {
          id: number
          created_at: string
          name: string
          skills: number
          effort: number
          stamina: number
          group_id: string | null
          avatar_url: string | null
        }
        Insert: {
          name: string
          skills: number
          effort: number
          stamina: number
          group_id: string
          avatar_url?: string | null
        }
        Update: {
          name?: string
          skills?: number
          effort?: number
          stamina?: number
          group_id?: string
          avatar_url?: string | null
        }
      }
      matches: {
        Row: {
          id: number
          match_date: string
          team_a: number[]
          team_b: number[]
          score_a: number | null
          score_b: number | null
          winner: 'A' | 'B' | 'Draw' | null
          goalscorers: Json | null
          youtube_url: string | null
          group_id: string | null
          location: string | null
          notes: string | null
          created_at: string
        }
        Insert: {
          match_date: string
          team_a: number[]
          team_b: number[]
          score_a?: number | null
          score_b?: number | null
          winner?: 'A' | 'B' | 'Draw' | null
          goalscorers?: Json | null
          youtube_url?: string | null
          group_id: string
          location?: string | null
          notes?: string | null
        }
        Update: {
          match_date?: string
          team_a?: number[]
          team_b?: number[]
          score_a?: number | null
          score_b?: number | null
          winner?: 'A' | 'B' | 'Draw' | null
          goalscorers?: Json | null
          youtube_url?: string | null
          group_id?: string
          location?: string | null
          notes?: string | null
        }
      }
      chemistry: {
        Row: {
          id: number
          player1_id: number
          player2_id: number
          games_together: number
          wins_together: number
          group_id: string | null
          created_at: string
        }
        Insert: {
          player1_id: number
          player2_id: number
          games_together?: number
          wins_together?: number
          group_id: string
        }
        Update: {
          player1_id?: number
          player2_id?: number
          games_together?: number
          wins_together?: number
          group_id?: string
        }
      }
      friend_groups: {
        Row: {
          id: string
          name: string
          created_by: string
          created_at: string
        }
        Insert: {
          name: string
          created_by: string
          id?: string
          created_at?: string
        }
        Update: {
          name?: string
          created_by?: string
          id?: string
          created_at?: string
        }
      }
      group_members: {
        Row: {
          id: string
          group_id: string
          user_id: string
          role: string
          created_at: string
        }
        Insert: {
          group_id: string
          user_id: string
          role: string
          id?: string
          created_at?: string
        }
        Update: {
          group_id?: string
          user_id?: string
          role?: string
          id?: string
          created_at?: string
        }
      }
      match_comments: {
        Row: {
          id: string
          match_id: number
          user_id: string
          content: string
          created_at: string
          group_id: string | null
        }
        Insert: {
          match_id: number
          user_id: string
          content: string
          group_id: string
          id?: string
          created_at?: string
        }
        Update: {
          match_id?: number
          user_id?: string
          content?: string
          group_id?: string
          id?: string
          created_at?: string
        }
      }
      user_preferences: {
        Row: {
          id: string
          user_id: string
          theme: string
          default_group_id: string | null
          notification_settings: Json
          display_settings: Json
          created_at: string
          updated_at: string
        }
        Insert: {
          user_id: string
          theme?: string
          default_group_id?: string | null
          notification_settings?: Json
          display_settings?: Json
          id?: string
          created_at?: string
          updated_at?: string
        }
        Update: {
          user_id?: string
          theme?: string
          default_group_id?: string | null
          notification_settings?: Json
          display_settings?: Json
          id?: string
          updated_at?: string
        }
      }

      shared_links: {
        Row: {
          id: string
          group_id: string
          created_by: string
          name: string
          access_level: string
          expires_at: string | null
          is_active: boolean
          created_at: string
          updated_at: string
        }
        Insert: {
          group_id: string
          created_by: string
          name: string
          access_level?: string
          expires_at?: string | null
          is_active?: boolean
          id?: string
          created_at?: string
          updated_at?: string
        }
        Update: {
          group_id?: string
          created_by?: string
          name?: string
          access_level?: string
          expires_at?: string | null
          is_active?: boolean
          id?: string
          updated_at?: string
        }
      }
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      [_ in never]: never
    }
    Enums: {
      [_ in never]: never
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
}
