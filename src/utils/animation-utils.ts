/**
 * Animation utilities for performance-optimized animations
 * Focuses on CSS transforms and opacity for hardware acceleration
 */

// Check if user prefers reduced motion
export const prefersReducedMotion = (): boolean => {
  if (typeof window === 'undefined') return false;
  return window.matchMedia('(prefers-reduced-motion: reduce)').matches;
};

// Performance-optimized animation classes
export const animationClasses = {
  // Base transitions using transform and opacity only
  smooth: 'transition-all duration-normal ease-out',
  fast: 'transition-all duration-fast ease-out',
  slow: 'transition-all duration-slow ease-out',
  spring: 'transition-all duration-normal ease-spring',
  
  // Transform-based animations (hardware accelerated)
  scale: 'transition-transform duration-fast ease-out',
  translate: 'transition-transform duration-normal ease-out',
  fade: 'transition-opacity duration-normal ease-out',
  
  // Hover states
  hoverScale: 'hover:scale-[1.02] active:scale-[0.98]',
  hoverLift: 'hover:-translate-y-1 hover:shadow-lg',
  hoverGlow: 'hover:shadow-lg hover:shadow-soccer-primary/25',
  
  // Focus states
  focusRing: 'focus-visible:ring-2 focus-visible:ring-soccer-primary focus-visible:ring-offset-2',
  focusScale: 'focus-visible:scale-[1.02]',
  
  // Loading states
  pulse: 'animate-pulse-glow',
  shimmer: 'animate-shimmer',
  spin: 'animate-spin',
  
  // Entry animations
  fadeIn: 'animate-fade-in',
  slideInUp: 'animate-slide-in-up',
  slideInDown: 'animate-slide-in-down',
  scaleIn: 'animate-scale-in',
} as const;

// Staggered animation delays
export const staggerDelays = {
  none: '0ms',
  xs: '50ms',
  sm: '100ms',
  md: '150ms',
  lg: '200ms',
  xl: '300ms',
} as const;

// Generate staggered delay for list items
export const getStaggerDelay = (index: number, baseDelay: keyof typeof staggerDelays = 'sm'): string => {
  const delay = parseInt(staggerDelays[baseDelay]);
  return `${delay * index}ms`;
};

// Animation performance utilities
export const animationUtils = {
  // Force hardware acceleration
  enableHardwareAcceleration: (element: HTMLElement) => {
    element.style.transform = 'translateZ(0)';
    element.style.willChange = 'transform, opacity';
  },
  
  // Disable hardware acceleration after animation
  disableHardwareAcceleration: (element: HTMLElement) => {
    element.style.willChange = 'auto';
  },
  
  // Batch DOM reads and writes for better performance
  batchAnimations: (animations: (() => void)[]) => {
    requestAnimationFrame(() => {
      animations.forEach(animation => animation());
    });
  },
  
  // Intersection observer for scroll-triggered animations
  createScrollAnimationObserver: (
    callback: (entries: IntersectionObserverEntry[]) => void,
    options?: IntersectionObserverInit
  ) => {
    if (typeof window === 'undefined') return null;
    
    return new IntersectionObserver(callback, {
      threshold: 0.1,
      rootMargin: '50px',
      ...options,
    });
  },
};

// React hook for performance-optimized animations
export const useOptimizedAnimation = () => {
  const isReducedMotion = prefersReducedMotion();
  
  const getAnimationClass = (animationKey: keyof typeof animationClasses): string => {
    if (isReducedMotion) return '';
    return animationClasses[animationKey];
  };
  
  const getStaggerStyle = (index: number, baseDelay: keyof typeof staggerDelays = 'sm') => {
    if (isReducedMotion) return {};
    return { animationDelay: getStaggerDelay(index, baseDelay) };
  };
  
  return {
    getAnimationClass,
    getStaggerStyle,
    isReducedMotion,
  };
};

// CSS-in-JS animation styles for complex animations
export const animationStyles = {
  cardHover: {
    transition: 'transform 250ms cubic-bezier(0.16, 1, 0.3, 1), box-shadow 250ms cubic-bezier(0.16, 1, 0.3, 1)',
    '&:hover': {
      transform: 'translateY(-2px)',
      boxShadow: '0 10px 25px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)',
    },
  },
  
  buttonPress: {
    transition: 'transform 150ms cubic-bezier(0.16, 1, 0.3, 1)',
    '&:active': {
      transform: 'scale(0.98)',
    },
  },
  
  fadeInUp: {
    opacity: 0,
    transform: 'translateY(10px)',
    animation: 'fadeInUp 300ms cubic-bezier(0.16, 1, 0.3, 1) forwards',
  },
  
  slideInLeft: {
    opacity: 0,
    transform: 'translateX(-20px)',
    animation: 'slideInLeft 300ms cubic-bezier(0.16, 1, 0.3, 1) forwards',
  },
} as const;

// Accessibility helpers
export const a11yAnimationHelpers = {
  // Respect user preferences
  respectMotionPreference: (animationClass: string): string => {
    return prefersReducedMotion() ? '' : animationClass;
  },
  
  // Add appropriate ARIA attributes for animated content
  getAnimationAriaAttributes: (isAnimating: boolean) => ({
    'aria-busy': isAnimating,
    'aria-live': isAnimating ? 'polite' : undefined,
  }),
  
  // Focus management during animations
  manageFocusDuringAnimation: (element: HTMLElement, duration: number) => {
    element.style.pointerEvents = 'none';
    setTimeout(() => {
      element.style.pointerEvents = 'auto';
    }, duration);
  },
};

export default {
  animationClasses,
  staggerDelays,
  getStaggerDelay,
  animationUtils,
  useOptimizedAnimation,
  animationStyles,
  a11yAnimationHelpers,
  prefersReducedMotion,
};
