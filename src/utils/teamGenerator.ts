import { parseISO } from "date-fns";
import { calculatePlayerRating } from "./playerRating";

// localStorage Keys
export const PLAYERS_STORAGE_KEY = 'soccerPlayersData';
export const MATCHES_STORAGE_KEY = 'soccerMatchesData';

// --- Interfaces ---
export interface Player {
  id: number;
  name: string;
  skills?: number;
  effort?: number;
  stamina?: number;
  rating?: number;
  played?: number;
}

export interface Match {
  id: number;
  date: Date | string; // Allow string initially from LS
  teamA: number[];
  teamB: number[];
  scoreA: number | null;
  scoreB: number | null;
  winner?: 'A' | 'B' | 'Draw';
  goalscorers?: any[]; // Simple type for now
  youtubeLink?: string;
}

export interface GameFormat {
  title: string;
  playersPerTeam: number;
}

export type GameFormatKey = "5v5" | "6v6" | "7v7" | "8v8" | "11v11";
export type GenerationMode = "balanced" | "random";

// Function to load data from localStorage (Generic)
export function loadData<T>(key: string, fallback: T[]): T[] {
    try {
      const storedData = typeof window !== 'undefined' ? localStorage.getItem(key) : null;
      if (storedData) {
          const parsedData = JSON.parse(storedData) as any[];
           // Date parsing specifically for matches
          if (key === MATCHES_STORAGE_KEY) {
               return parsedData.map(item => ({
                   ...item,
                   date: item.date ? parseISO(item.date) : new Date(0)
               })) as T[];
          }
          return parsedData as T[];
      }
    } catch (error) {
      console.error(`Error reading ${key} from localStorage:`, error);
    }
    return fallback;
}

// Function to save data to localStorage (Generic)
export function saveData<T>(key: string, data: T[]) {
    try {
        let dataToStore = data;
        // Date stringifying specifically for matches
        if (key === MATCHES_STORAGE_KEY) {
            dataToStore = data.map((item: any) => ({
                ...item,
                date: item.date instanceof Date ? item.date.toISOString() : item.date
             })) as T[];
        }
        localStorage.setItem(key, JSON.stringify(dataToStore));
    } catch (error) {
      console.error(`Error saving ${key} to localStorage:`, error);
    }
}

// Function to shuffle an array
export function shuffleArray<T>(array: T[]): T[] {
  let currentIndex = array.length, randomIndex;
  const newArray = [...array];
  while (currentIndex !== 0) {
    randomIndex = Math.floor(Math.random() * currentIndex);
    currentIndex--;
    [newArray[currentIndex], newArray[randomIndex]] = [newArray[randomIndex], newArray[currentIndex]];
  }
  return newArray;
}

// Function to calculate average rating
export const calculateAverageRating = (player: Player): number => {
    return calculatePlayerRating(player);
};

// Define game formats
export const gameFormats: Record<GameFormatKey, GameFormat> = {
  "5v5": { title: "5v5", playersPerTeam: 5 },
  "6v6": { title: "6v6", playersPerTeam: 6 },
  "7v7": { title: "7v7", playersPerTeam: 7 },
  "8v8": { title: "8v8", playersPerTeam: 8 },
  "11v11": { title: "11v11", playersPerTeam: 11 }
};

// Function to generate teams based on selected players
export const generateTeams = (
  selectedPlayerIds: number[],
  allPlayers: Player[],
  format: GameFormatKey,
  generationMode: GenerationMode
): { teamA: Player[], teamB: Player[] } => {
  const selectedPlayerObjects = allPlayers.filter((p) => selectedPlayerIds.includes(p.id));
  let playersToDistribute: Player[] = [];
  const teamA: Player[] = [];
  const teamB: Player[] = [];

  if (generationMode === 'balanced') {
    playersToDistribute = [...selectedPlayerObjects].sort((a, b) => (b.rating ?? 0) - (a.rating ?? 0));
    let teamARating = 0;
    let teamBRating = 0;
    playersToDistribute.forEach((player) => {
      if (teamARating <= teamBRating) {
        teamA.push(player);
        teamARating += (player.rating ?? 0);
      } else {
        teamB.push(player);
        teamBRating += (player.rating ?? 0);
      }
    });
  } else { // Random mode
    playersToDistribute = shuffleArray(selectedPlayerObjects);
    playersToDistribute.forEach((player, index) => {
      if (index % 2 === 0) teamA.push(player);
      else teamB.push(player);
    });
  }

  return { teamA, teamB };
};
