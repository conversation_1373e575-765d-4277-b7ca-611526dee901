// Setup type definitions for built-in Supabase Runtime APIs
import "jsr:@supabase/functions-js/edge-runtime.d.ts"
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2.49.4'

Deno.serve(async (req) => {
  try {
    // Get the authorization header from the request
    const authHeader = req.headers.get('Authorization')
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return new Response(
        JSON.stringify({ error: 'Missing or invalid authorization header' }),
        { status: 401, headers: { 'Content-Type': 'application/json' } }
      )
    }

    // Extract the token
    const token = authHeader.split(' ')[1]

    // Create a Supabase client with the service role key
    const supabaseUrl = Deno.env.get('SUPABASE_URL') || ''
    const supabaseKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') || ''
    const supabase = createClient(supabaseUrl, supabaseKey, {
      auth: {
        autoRefreshToken: false,
        persistSession: false,
      },
    })

    // Verify the token
    const { data: { user }, error: authError } = await supabase.auth.getUser(token)
    if (authError || !user) {
      return new Response(
        JSON.stringify({ error: 'Unauthorized', details: authError }),
        { status: 401, headers: { 'Content-Type': 'application/json' } }
      )
    }

    // SQL to create the shared_links table if it doesn't exist
    const sql = `
      -- Check if the shared_links table exists
      DO $$
      BEGIN
          IF NOT EXISTS (SELECT FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'shared_links') THEN
              -- Create shared_links table for enhanced sharing
              CREATE TABLE public.shared_links (
                  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
                  group_id UUID REFERENCES friend_groups(id) ON DELETE CASCADE NOT NULL,
                  created_by UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
                  name TEXT NOT NULL,
                  access_level TEXT DEFAULT 'read' CHECK (access_level IN ('read', 'comment')),
                  expires_at TIMESTAMP WITH TIME ZONE,
                  is_active BOOLEAN DEFAULT true,
                  created_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now()) NOT NULL,
                  updated_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now()) NOT NULL
              );

              -- Create index for better performance
              CREATE INDEX shared_links_group_id_idx ON public.shared_links(group_id);
              CREATE INDEX shared_links_created_by_idx ON public.shared_links(created_by);

              -- Enable Row Level Security
              ALTER TABLE public.shared_links ENABLE ROW LEVEL SECURITY;

              -- Create policies to restrict access to shared_links
              CREATE POLICY "Users can view their own shared links" ON public.shared_links
              FOR SELECT USING (
                created_by = auth.uid() OR
                group_id IN (
                  SELECT group_id FROM group_members
                  WHERE user_id = auth.uid() AND role = 'Admin'
                )
              );

              CREATE POLICY "Users can create shared links for their groups" ON public.shared_links
              FOR INSERT WITH CHECK (
                group_id IN (
                  SELECT group_id FROM group_members
                  WHERE user_id = auth.uid() AND role IN ('Admin', 'Collaborator')
                  UNION
                  SELECT id FROM friend_groups WHERE created_by = auth.uid()
                )
              );

              CREATE POLICY "Users can update their own shared links" ON public.shared_links
              FOR UPDATE USING (
                created_by = auth.uid() OR
                group_id IN (
                  SELECT group_id FROM group_members
                  WHERE user_id = auth.uid() AND role = 'Admin'
                )
              );

              CREATE POLICY "Users can delete their own shared links" ON public.shared_links
              FOR DELETE USING (
                created_by = auth.uid() OR
                group_id IN (
                  SELECT group_id FROM group_members
                  WHERE user_id = auth.uid() AND role = 'Admin'
                )
              );

              -- Check if the update_updated_at_column function exists
              IF EXISTS (SELECT FROM pg_proc WHERE proname = 'update_updated_at_column') THEN
                  -- Create trigger to update updated_at timestamp
                  CREATE TRIGGER update_shared_links_updated_at
                  BEFORE UPDATE ON public.shared_links
                  FOR EACH ROW
                  EXECUTE FUNCTION update_updated_at_column();
              END IF;

              RAISE NOTICE 'Created shared_links table and associated objects';
          ELSE
              RAISE NOTICE 'shared_links table already exists';
          END IF;
      END
      $$;
    `

    // Execute the SQL directly using the Postgres extension
    const { data: sqlResult, error: sqlError } = await supabase.rpc('exec_sql', { query: sql }).single()

    if (sqlError) {
      return new Response(
        JSON.stringify({
          error: 'Failed to create table',
          details: sqlError,
          message: 'The exec_sql function is not available. Please create the shared_links table manually using the SQL provided in the response.',
          sql: sql
        }),
        { status: 500, headers: { 'Content-Type': 'application/json' } }
      )
    }

    // Check if the table was created
    const { data, error: checkError } = await supabase
      .from('shared_links')
      .select('count')
      .limit(1)

    if (checkError) {
      if (checkError.code === '42P01') { // Table doesn't exist
        return new Response(
          JSON.stringify({
            error: 'Table creation failed',
            details: checkError,
            message: 'The shared_links table could not be created. Please create it manually using the SQL provided in the response.',
            sql: sql
          }),
          { status: 500, headers: { 'Content-Type': 'application/json' } }
        )
      } else {
        return new Response(
          JSON.stringify({ error: 'Error checking table', details: checkError }),
          { status: 500, headers: { 'Content-Type': 'application/json' } }
        )
      }
    }

    return new Response(
      JSON.stringify({
        success: true,
        message: 'The shared_links table has been created or already exists',
        data
      }),
      { status: 200, headers: { 'Content-Type': 'application/json' } }
    )
  } catch (error) {
    return new Response(
      JSON.stringify({ error: 'Internal server error', details: error.message }),
      { status: 500, headers: { 'Content-Type': 'application/json' } }
    )
  }
})

/* To invoke locally:

  1. Run `supabase start` (see: https://supabase.com/docs/reference/cli/supabase-start)
  2. Make an HTTP request:

  curl -i --location --request POST 'http://127.0.0.1:54321/functions/v1/create-shared-links-table' \
    --header 'Authorization: Bearer YOUR_AUTH_TOKEN' \
    --header 'Content-Type: application/json'

*/
