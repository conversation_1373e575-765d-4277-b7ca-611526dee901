// Follow this setup guide to integrate the Deno runtime into your application:
// https://deno.land/manual/examples/deploy_node_server
// This is using Deno and the Supabase Edge Functions

import { createClient } from 'https://esm.sh/@supabase/supabase-js@2.49.4'
import { corsHeaders } from '../_shared/cors.ts'

interface InvitationPayload {
  email: string
  groupId: string
  groupName: string
  invitedBy: string
  role: string
}

Deno.serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    const supabaseUrl = Deno.env.get('SUPABASE_URL') || ''
    const supabaseServiceKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') || ''
    const resendApiKey = Deno.env.get('RESEND_API_KEY') || ''
    // Get the sender email from environment variable or use a default Resend address
    const senderEmail = Deno.env.get('SENDER_EMAIL') || '<EMAIL>'
    const senderName = Deno.env.get('SENDER_NAME') || 'Soccer Stats'

    // Get the developer email for testing mode
    const developerEmail = Deno.env.get('DEVELOPER_EMAIL') || '<EMAIL>'

    // Check if we're in development mode (no verified domain)
    const isDevelopmentMode = Deno.env.get('RESEND_MODE') === 'development'

    if (!resendApiKey) {
      throw new Error('RESEND_API_KEY environment variable is not set')
    }

    const supabase = createClient(supabaseUrl, supabaseServiceKey, {
      auth: {
        autoRefreshToken: false,
        persistSession: false,
      },
    })

    // Get the request payload
    const payload: InvitationPayload = await req.json()
    const { email, groupId, groupName, invitedBy, role } = payload

    if (!email || !groupId || !groupName || !invitedBy || !role) {
      return new Response(
        JSON.stringify({ error: 'Missing required fields in payload' }),
        { status: 400, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      )
    }

    // Generate a signup URL with redirect to the group
    const siteUrl = Deno.env.get('SITE_URL') || 'http://localhost:3000'
    const signUpUrl = `${siteUrl}/signup?redirect=/groups/${groupId}&email=${encodeURIComponent(email)}`

    // Create email content
    const emailSubject = `You've been invited to join ${groupName} as a ${role}`
    const emailContent = `
      <h2>You've been invited to join ${groupName}</h2>
      <p>Hello,</p>
      <p>${invitedBy} has invited you to join their group "${groupName}" as a ${role}.</p>
      <p>Click the link below to sign up and join the group:</p>
      <p><a href="${signUpUrl}" style="display: inline-block; background-color: #35db71; color: white; padding: 10px 20px; text-decoration: none; border-radius: 4px;">Accept Invitation</a></p>
      <p>If you already have an account, you'll be able to log in and join the group automatically.</p>
      <p>This invitation will expire in 7 days.</p>
      <p>Thank you,<br>Soccer Stats Team</p>
    `

    // Prepare email data
    const emailData = {
      from: `${senderName} <${senderEmail}>`,
      subject: emailSubject,
      html: emailContent,
      tags: [
        {
          name: 'category',
          value: 'invitation'
        }
      ]
    }

    // In development mode, we can only send to our own email
    // So we'll redirect all emails to the developer email
    if (isDevelopmentMode) {
      // Add original recipient info to the email content for testing
      const devModeContent = `
        <div style="background-color: #f8f9fa; padding: 10px; margin-bottom: 20px; border-left: 4px solid #6c757d;">
          <p><strong>DEVELOPMENT MODE</strong></p>
          <p>This email was originally intended for: <strong>${email}</strong></p>
          <p>Redirected to developer email for testing purposes.</p>
        </div>
        ${emailContent}
      `;

      emailData.to = [developerEmail];
      emailData.html = devModeContent;
      console.log(`Development mode: Redirecting email from ${email} to ${developerEmail}`);
    } else {
      emailData.to = [email];
    }

    // Send the email using Resend API directly
    const response = await fetch('https://api.resend.com/emails', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${resendApiKey}`
      },
      body: JSON.stringify(emailData)
    })

    const result = await response.json()

    if (!response.ok) {
      console.error('Error sending invitation email:', result)
      return new Response(
        JSON.stringify({ error: 'Failed to send invitation email', details: result }),
        { status: 500, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      )
    }

    // Also create a record in the auth.users table for the invited user
    // This will allow them to sign up more easily
    try {
      await supabase.auth.admin.inviteUserByEmail(email, {
        redirectTo: signUpUrl,
        data: {
          groupId,
          groupName,
          role,
          invitedBy
        }
      })
    } catch (authError) {
      // Log but don't fail if this doesn't work
      console.warn('Could not create auth invitation, but email was sent:', authError)
    }

    return new Response(
      JSON.stringify({ success: true, message: 'Invitation email sent successfully', data: result }),
      { status: 200, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
    )
  } catch (error) {
    console.error('Error in send-invitation-email function:', error)
    return new Response(
      JSON.stringify({ error: 'Internal server error', details: error.message }),
      { status: 500, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
    )
  }
})

/* To invoke locally:

  1. Run `supabase start` (see: https://supabase.com/docs/reference/cli/supabase-start)
  2. Make an HTTP request:

  curl -i --location --request POST 'http://127.0.0.1:54321/functions/v1/send-invitation-email' \
    --header 'Authorization: Bearer YOUR_ANON_KEY' \
    --header 'Content-Type: application/json' \
    --data '{"email":"<EMAIL>","groupId":"123","groupName":"Test Group","invitedBy":"<EMAIL>","role":"Guest"}'

*/
