-- Create storage bucket if it doesn't exist
insert into storage.buckets (id, name, public)
values ('profiles', 'profiles', true)
on conflict (id) do nothing;

-- Allow public read access to files in the profiles bucket
create policy "Public Read Access"
on storage.objects for select
using (bucket_id = 'profiles');

-- Allow authenticated users to upload their own profile pictures
create policy "Authenticated User Upload"
on storage.objects for insert
with check (
  bucket_id = 'profiles' AND
  auth.uid() = owner AND
  (storage.foldername(name))[1] = 'avatars'
);

-- Allow users to update their own profile pictures
create policy "User Update Own Picture"
on storage.objects for update
using (
  bucket_id = 'profiles' AND
  auth.uid() = owner AND
  (storage.foldername(name))[1] = 'avatars'
);

-- Allow users to delete their own profile pictures
create policy "User Delete Own Picture"
on storage.objects for delete
using (
  bucket_id = 'profiles' AND
  auth.uid() = owner AND
  (storage.foldername(name))[1] = 'avatars'
);
