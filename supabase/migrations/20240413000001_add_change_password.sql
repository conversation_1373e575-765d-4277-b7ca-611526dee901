-- filepath: /Users/<USER>/soccer-ui-arena/supabase/migrations/20240413000001_add_change_password.sql
CREATE OR REPLACE FUNCTION change_group_password(
    current_password TEXT,
    new_password TEXT
) RET<PERSON>NS void AS $$
DECLARE
    group_id INT;
    group_role TEXT;
    stored_hash TEXT;
BEGIN
    -- Get the current group_id and role from session
    group_id := current_setting('app.group_id', true)::INT;
    group_role := current_setting('app.role', true);
    
    IF group_id IS NULL THEN
        RAISE EXCEPTION 'Not authenticated';
    END IF;

    -- Get the stored hash based on role
    SELECT 
        CASE 
            WHEN group_role = 'admin' THEN hashed_admin_password
            ELSE hashed_password
        END INTO stored_hash
    FROM groups 
    WHERE id = group_id;

    -- Verify current password
    IF NOT crypt(current_password, stored_hash) = stored_hash THEN
        RAISE EXCEPTION 'Current password is incorrect';
    END IF;

    -- Update the appropriate password field
    IF group_role = 'admin' THEN
        UPDATE groups 
        SET hashed_admin_password = crypt(new_password, gen_salt('bf'))
        WHERE id = group_id;
    ELSE
        UPDATE groups 
        SET hashed_password = crypt(new_password, gen_salt('bf'))
        WHERE id = group_id;
    END IF;
END;
$$ LANGUAGE plpgsql;
