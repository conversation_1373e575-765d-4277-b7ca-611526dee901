-- Migration to create the friend_groups table
CREATE TABLE friend_groups (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name TEXT NOT NULL,
    created_by UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    created_at TIMESTAMP DEFAULT now()
);

-- Enable RLS for friend_groups table
ALTER TABLE friend_groups ENABLE ROW LEVEL SECURITY;

-- Policy: Allow group creators to manage their groups
CREATE POLICY "Group Creator Full Access"
ON friend_groups
FOR ALL
USING (auth.uid() = created_by);

-- Migration to create the group_members table
CREATE TABLE group_members (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    group_id UUID REFERENCES friend_groups(id) ON DELETE CASCADE,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    role TEXT CHECK (role IN ('Admin', 'Collaborator', 'Guest')) NOT NULL,
    created_at TIMESTAMP DEFAULT now()
);

-- Enable RLS for group_members table
ALTER TABLE group_members ENABLE ROW LEVEL SECURITY;

-- Policy: Allow group members to view their memberships
CREATE POLICY "View Own Memberships"
ON group_members
FOR SELECT
USING (auth.uid() = user_id);

-- Policy: Allow Admins to manage group memberships
CREATE POLICY "Admins Manage Memberships"
ON group_members
FOR ALL
USING (
  EXISTS (
    SELECT 1 FROM group_members AS gm
    WHERE gm.group_id = group_members.group_id
    AND gm.user_id = auth.uid()
    AND gm.role = 'Admin'
  )
);

-- Add group_id column to matches table if it exists
DO $$
BEGIN
  IF EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'matches') THEN
    ALTER TABLE matches ADD COLUMN IF NOT EXISTS group_id UUID REFERENCES friend_groups(id) ON DELETE CASCADE;
  END IF;
END $$;

-- Add group_id column to player_stats table if it exists
DO $$
BEGIN
  IF EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'player_stats') THEN
    ALTER TABLE player_stats ADD COLUMN IF NOT EXISTS group_id UUID REFERENCES friend_groups(id) ON DELETE CASCADE;
  END IF;
END $$;