-- Add group_id column to players table if it doesn't exist
ALTER TABLE IF EXISTS public.players 
ADD COLUMN IF NOT EXISTS group_id UUID REFERENCES friend_groups(id) ON DELETE CASCADE;

-- Add group_id column to matches table if it doesn't exist
-- (This might already exist from a previous migration, but we'll include it for completeness)
ALTER TABLE IF EXISTS public.matches 
ADD COLUMN IF NOT EXISTS group_id UUID REFERENCES friend_groups(id) ON DELETE CASCADE;

-- Add group_id column to chemistry table if it doesn't exist
ALTER TABLE IF EXISTS public.chemistry 
ADD COLUMN IF NOT EXISTS group_id UUID REFERENCES friend_groups(id) ON DELETE CASCADE;

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS players_group_id_idx ON public.players(group_id);
CREATE INDEX IF NOT EXISTS matches_group_id_idx ON public.matches(group_id);
CREATE INDEX IF NOT EXISTS chemistry_group_id_idx ON public.chemistry(group_id);

-- Enable Row Level Security on these tables
ALTER TABLE public.players ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.matches ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.chemistry ENABLE ROW LEVEL SECURITY;

-- Create policies to restrict access to data based on group membership
CREATE POLICY "Users can view players in their groups" ON public.players
FOR SELECT USING (
  group_id IN (
    SELECT group_id FROM group_members WHERE user_id = auth.uid()
    UNION
    SELECT id FROM friend_groups WHERE created_by = auth.uid()
  )
);

CREATE POLICY "Users can modify players in their groups" ON public.players
FOR ALL USING (
  group_id IN (
    SELECT group_id FROM group_members WHERE user_id = auth.uid()
    UNION
    SELECT id FROM friend_groups WHERE created_by = auth.uid()
  )
);

CREATE POLICY "Users can view matches in their groups" ON public.matches
FOR SELECT USING (
  group_id IN (
    SELECT group_id FROM group_members WHERE user_id = auth.uid()
    UNION
    SELECT id FROM friend_groups WHERE created_by = auth.uid()
  )
);

CREATE POLICY "Users can modify matches in their groups" ON public.matches
FOR ALL USING (
  group_id IN (
    SELECT group_id FROM group_members WHERE user_id = auth.uid()
    UNION
    SELECT id FROM friend_groups WHERE created_by = auth.uid()
  )
);

CREATE POLICY "Users can view chemistry in their groups" ON public.chemistry
FOR SELECT USING (
  group_id IN (
    SELECT group_id FROM group_members WHERE user_id = auth.uid()
    UNION
    SELECT id FROM friend_groups WHERE created_by = auth.uid()
  )
);

CREATE POLICY "Users can modify chemistry in their groups" ON public.chemistry
FOR ALL USING (
  group_id IN (
    SELECT group_id FROM group_members WHERE user_id = auth.uid()
    UNION
    SELECT id FROM friend_groups WHERE created_by = auth.uid()
  )
);
