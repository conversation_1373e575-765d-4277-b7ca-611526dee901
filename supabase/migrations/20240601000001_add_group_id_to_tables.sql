-- Add group_id column to players table if it doesn't exist
ALTER TABLE IF EXISTS public.players 
ADD COLUMN IF NOT EXISTS group_id UUID REFERENCES friend_groups(id) ON DELETE CASCADE;

-- Add group_id column to matches table if it doesn't exist
ALTER TABLE IF EXISTS public.matches 
ADD COLUMN IF NOT EXISTS group_id UUID REFERENCES friend_groups(id) ON DELETE CASCADE;

-- Add group_id column to chemistry table if it doesn't exist
ALTER TABLE IF EXISTS public.chemistry 
ADD COLUMN IF NOT EXISTS group_id UUID REFERENCES friend_groups(id) ON DELETE CASCADE;

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS players_group_id_idx ON public.players(group_id);
CREATE INDEX IF NOT EXISTS matches_group_id_idx ON public.matches(group_id);
CREATE INDEX IF NOT EXISTS chemistry_group_id_idx ON public.chemistry(group_id);

-- Create policies to restrict access to data based on group membership
DROP POLICY IF EXISTS "Users can view players in their groups" ON public.players;
CREATE POLICY "Users can view players in their groups" ON public.players
FOR SELECT USING (
  group_id IN (
    SELECT group_id FROM group_members WHERE user_id = auth.uid()
    UNION
    SELECT id FROM friend_groups WHERE created_by = auth.uid()
  )
);

DROP POLICY IF EXISTS "Users can modify players in their groups" ON public.players;
CREATE POLICY "Users can modify players in their groups" ON public.players
FOR ALL USING (
  group_id IN (
    SELECT group_id FROM group_members WHERE user_id = auth.uid()
    UNION
    SELECT id FROM friend_groups WHERE created_by = auth.uid()
  )
);

DROP POLICY IF EXISTS "Users can view matches in their groups" ON public.matches;
CREATE POLICY "Users can view matches in their groups" ON public.matches
FOR SELECT USING (
  group_id IN (
    SELECT group_id FROM group_members WHERE user_id = auth.uid()
    UNION
    SELECT id FROM friend_groups WHERE created_by = auth.uid()
  )
);

DROP POLICY IF EXISTS "Users can modify matches in their groups" ON public.matches;
CREATE POLICY "Users can modify matches in their groups" ON public.matches
FOR ALL USING (
  group_id IN (
    SELECT group_id FROM group_members WHERE user_id = auth.uid()
    UNION
    SELECT id FROM friend_groups WHERE created_by = auth.uid()
  )
);

DROP POLICY IF EXISTS "Users can view chemistry in their groups" ON public.chemistry;
CREATE POLICY "Users can view chemistry in their groups" ON public.chemistry
FOR SELECT USING (
  group_id IN (
    SELECT group_id FROM group_members WHERE user_id = auth.uid()
    UNION
    SELECT id FROM friend_groups WHERE created_by = auth.uid()
  )
);

DROP POLICY IF EXISTS "Users can modify chemistry in their groups" ON public.chemistry;
CREATE POLICY "Users can modify chemistry in their groups" ON public.chemistry
FOR ALL USING (
  group_id IN (
    SELECT group_id FROM group_members WHERE user_id = auth.uid()
    UNION
    SELECT id FROM friend_groups WHERE created_by = auth.uid()
  )
);

-- Create a function to migrate existing data to use group_id
CREATE OR REPLACE FUNCTION migrate_user_data_to_group(user_id UUID)
RETURNS UUID AS $$
DECLARE
    default_group_id UUID;
    player_ids INTEGER[];
BEGIN
    -- Check if the user already has a group
    SELECT id INTO default_group_id
    FROM friend_groups
    WHERE created_by = user_id
    LIMIT 1;
    
    -- If not, create a default group for the user
    IF default_group_id IS NULL THEN
        INSERT INTO friend_groups (name, created_by)
        VALUES ('Default Group', user_id)
        RETURNING id INTO default_group_id;
        
        -- Add the user as an Admin in their own group
        INSERT INTO group_members (group_id, user_id, role)
        VALUES (default_group_id, user_id, 'Admin');
    END IF;
    
    -- Update players table
    UPDATE players
    SET group_id = default_group_id
    WHERE group_id IS NULL;
    
    -- Get all player IDs
    SELECT array_agg(id) INTO player_ids
    FROM players
    WHERE group_id = default_group_id;
    
    -- Update matches table
    UPDATE matches
    SET group_id = default_group_id
    WHERE group_id IS NULL
    AND (
        EXISTS (SELECT 1 FROM unnest(teama) AS player_id WHERE player_id = ANY(player_ids))
        OR EXISTS (SELECT 1 FROM unnest(teamb) AS player_id WHERE player_id = ANY(player_ids))
    );
    
    -- Update chemistry table
    UPDATE chemistry
    SET group_id = default_group_id
    WHERE group_id IS NULL
    AND (player1_id = ANY(player_ids) OR player2_id = ANY(player_ids));
    
    RETURN default_group_id;
END;
$$ LANGUAGE plpgsql;

-- Run this function for all users
DO $$
DECLARE
    user_record RECORD;
BEGIN
    FOR user_record IN SELECT id FROM auth.users
    LOOP
        PERFORM migrate_user_data_to_group(user_record.id);
    END LOOP;
END;
$$;
