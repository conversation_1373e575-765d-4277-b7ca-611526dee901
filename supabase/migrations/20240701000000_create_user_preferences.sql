-- Create user_preferences table
CREATE TABLE IF NOT EXISTS public.user_preferences (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
    theme TEXT DEFAULT 'system' CHECK (theme IN ('light', 'dark', 'system')),
    dashboard_layout JSONB DEFAULT '{}',
    default_group_id UUID REFERENCES friend_groups(id) ON DELETE SET NULL,
    notification_settings JSONB DEFAULT '{"email": true, "browser": true}',
    display_settings JSONB DEFAULT '{"compactView": false, "showAvatars": true, "animationsEnabled": true}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now()) NOT NULL,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now()) NOT NULL,
    UNIQUE(user_id)
);

-- Create index for better performance
CREATE INDEX IF NOT EXISTS user_preferences_user_id_idx ON public.user_preferences(user_id);

-- Enable Row Level Security
ALTER TABLE public.user_preferences ENABLE ROW LEVEL SECURITY;

-- Create policies to restrict access to user's own preferences
CREATE POLICY "Users can view their own preferences" ON public.user_preferences
FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own preferences" ON public.user_preferences
FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own preferences" ON public.user_preferences
FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete their own preferences" ON public.user_preferences
FOR DELETE USING (auth.uid() = user_id);

-- Create tags table for player tagging
CREATE TABLE IF NOT EXISTS public.tags (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name TEXT NOT NULL,
    color TEXT DEFAULT '#35db71',
    group_id UUID REFERENCES friend_groups(id) ON DELETE CASCADE NOT NULL,
    created_by UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now()) NOT NULL,
    UNIQUE(name, group_id)
);

-- Create index for better performance
CREATE INDEX IF NOT EXISTS tags_group_id_idx ON public.tags(group_id);

-- Enable Row Level Security
ALTER TABLE public.tags ENABLE ROW LEVEL SECURITY;

-- Create policies to restrict access to tags based on group membership
CREATE POLICY "Users can view tags in their groups" ON public.tags
FOR SELECT USING (
  group_id IN (
    SELECT group_id FROM group_members WHERE user_id = auth.uid()
    UNION
    SELECT id FROM friend_groups WHERE created_by = auth.uid()
  )
);

CREATE POLICY "Users can modify tags in their groups" ON public.tags
FOR ALL USING (
  group_id IN (
    SELECT group_id FROM group_members WHERE user_id = auth.uid()
    UNION
    SELECT id FROM friend_groups WHERE created_by = auth.uid()
  )
);

-- Create player_tags junction table
CREATE TABLE IF NOT EXISTS public.player_tags (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    player_id BIGINT REFERENCES public.players(id) ON DELETE CASCADE NOT NULL,
    tag_id UUID REFERENCES public.tags(id) ON DELETE CASCADE NOT NULL,
    created_by UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now()) NOT NULL,
    UNIQUE(player_id, tag_id)
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS player_tags_player_id_idx ON public.player_tags(player_id);
CREATE INDEX IF NOT EXISTS player_tags_tag_id_idx ON public.player_tags(tag_id);

-- Enable Row Level Security
ALTER TABLE public.player_tags ENABLE ROW LEVEL SECURITY;

-- Create policies to restrict access to player_tags based on group membership
CREATE POLICY "Users can view player_tags in their groups" ON public.player_tags
FOR SELECT USING (
  EXISTS (
    SELECT 1 FROM public.tags t
    JOIN public.players p ON p.id = player_tags.player_id
    WHERE t.id = player_tags.tag_id
    AND t.group_id = p.group_id
    AND t.group_id IN (
      SELECT group_id FROM group_members WHERE user_id = auth.uid()
      UNION
      SELECT id FROM friend_groups WHERE created_by = auth.uid()
    )
  )
);

CREATE POLICY "Users can modify player_tags in their groups" ON public.player_tags
FOR ALL USING (
  EXISTS (
    SELECT 1 FROM public.tags t
    JOIN public.players p ON p.id = player_tags.player_id
    WHERE t.id = player_tags.tag_id
    AND t.group_id = p.group_id
    AND t.group_id IN (
      SELECT group_id FROM group_members WHERE user_id = auth.uid()
      UNION
      SELECT id FROM friend_groups WHERE created_by = auth.uid()
    )
  )
);

-- Create shared_links table for enhanced sharing
CREATE TABLE IF NOT EXISTS public.shared_links (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    group_id UUID REFERENCES friend_groups(id) ON DELETE CASCADE NOT NULL,
    created_by UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
    name TEXT NOT NULL,
    access_level TEXT DEFAULT 'read' CHECK (access_level IN ('read', 'comment')),
    expires_at TIMESTAMP WITH TIME ZONE,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now()) NOT NULL,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now()) NOT NULL
);

-- Create index for better performance
CREATE INDEX IF NOT EXISTS shared_links_group_id_idx ON public.shared_links(group_id);
CREATE INDEX IF NOT EXISTS shared_links_created_by_idx ON public.shared_links(created_by);

-- Enable Row Level Security
ALTER TABLE public.shared_links ENABLE ROW LEVEL SECURITY;

-- Create policies to restrict access to shared_links
CREATE POLICY "Users can view their own shared links" ON public.shared_links
FOR SELECT USING (
  created_by = auth.uid() OR
  group_id IN (
    SELECT group_id FROM group_members 
    WHERE user_id = auth.uid() AND role = 'Admin'
  )
);

CREATE POLICY "Users can create shared links for their groups" ON public.shared_links
FOR INSERT WITH CHECK (
  group_id IN (
    SELECT group_id FROM group_members 
    WHERE user_id = auth.uid() AND role IN ('Admin', 'Collaborator')
    UNION
    SELECT id FROM friend_groups WHERE created_by = auth.uid()
  )
);

CREATE POLICY "Users can update their own shared links" ON public.shared_links
FOR UPDATE USING (
  created_by = auth.uid() OR
  group_id IN (
    SELECT group_id FROM group_members 
    WHERE user_id = auth.uid() AND role = 'Admin'
  )
);

CREATE POLICY "Users can delete their own shared links" ON public.shared_links
FOR DELETE USING (
  created_by = auth.uid() OR
  group_id IN (
    SELECT group_id FROM group_members 
    WHERE user_id = auth.uid() AND role = 'Admin'
  )
);

-- Create function to automatically update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
   NEW.updated_at = NOW();
   RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create triggers to update updated_at timestamp
CREATE TRIGGER update_user_preferences_updated_at
BEFORE UPDATE ON public.user_preferences
FOR EACH ROW
EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_shared_links_updated_at
BEFORE UPDATE ON public.shared_links
FOR EACH ROW
EXECUTE FUNCTION update_updated_at_column();
