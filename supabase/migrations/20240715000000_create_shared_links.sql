-- Create shared_links table for enhanced sharing
CREATE TABLE IF NOT EXISTS public.shared_links (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    group_id UUID REFERENCES friend_groups(id) ON DELETE CASCADE NOT NULL,
    created_by UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
    name TEXT NOT NULL,
    access_level TEXT DEFAULT 'read' CHECK (access_level IN ('read', 'comment')),
    expires_at TIMESTAMP WITH TIME ZONE,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now()) NOT NULL,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now()) NOT NULL
);

-- Create index for better performance
CREATE INDEX IF NOT EXISTS shared_links_group_id_idx ON public.shared_links(group_id);
CREATE INDEX IF NOT EXISTS shared_links_created_by_idx ON public.shared_links(created_by);

-- Enable Row Level Security
ALTER TABLE public.shared_links ENABLE ROW LEVEL SECURITY;

-- Create policies to restrict access to shared_links
CREATE POLICY "Users can view their own shared links" ON public.shared_links
FOR SELECT USING (
  created_by = auth.uid() OR
  group_id IN (
    SELECT group_id FROM group_members 
    WHERE user_id = auth.uid() AND role = 'Admin'
  )
);

CREATE POLICY "Users can create shared links for their groups" ON public.shared_links
FOR INSERT WITH CHECK (
  group_id IN (
    SELECT group_id FROM group_members 
    WHERE user_id = auth.uid() AND role IN ('Admin', 'Collaborator')
    UNION
    SELECT id FROM friend_groups WHERE created_by = auth.uid()
  )
);

CREATE POLICY "Users can update their own shared links" ON public.shared_links
FOR UPDATE USING (
  created_by = auth.uid() OR
  group_id IN (
    SELECT group_id FROM group_members 
    WHERE user_id = auth.uid() AND role = 'Admin'
  )
);

CREATE POLICY "Users can delete their own shared links" ON public.shared_links
FOR DELETE USING (
  created_by = auth.uid() OR
  group_id IN (
    SELECT group_id FROM group_members 
    WHERE user_id = auth.uid() AND role = 'Admin'
  )
);

-- Create trigger to update updated_at timestamp
CREATE TRIGGER update_shared_links_updated_at
BEFORE UPDATE ON public.shared_links
FOR EACH ROW
EXECUTE FUNCTION update_updated_at_column();
