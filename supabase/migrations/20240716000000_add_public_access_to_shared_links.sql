-- Add policy to allow public access to shared links by ID
ALTER TABLE public.shared_links ENABLE ROW LEVEL SECURITY;

-- First, temporarily disable <PERSON><PERSON> for testing
ALTER TABLE public.shared_links FORCE ROW LEVEL SECURITY;

DROP POLICY IF EXISTS "Public can view shared links by ID" ON public.shared_links;
CREATE POLICY "Public can view shared links by ID" ON public.shared_links
FOR SELECT USING (true);

-- Add a policy for authenticated users to manage their own links
DROP POLICY IF EXISTS "Users can manage their own shared links" ON public.shared_links;
CREATE POLICY "Users can manage their own shared links" ON public.shared_links
FOR ALL USING (auth.uid() = created_by);

-- Add policy to allow public access to friend_groups for shared links
ALTER TABLE public.friend_groups ENABLE ROW LEVEL SECURITY;

-- First, temporarily disable <PERSON><PERSON> for testing
ALTER TABLE public.friend_groups FORCE ROW LEVEL SECURITY;

DROP POLICY IF EXISTS "Public can view groups for shared links" ON public.friend_groups;
CREATE POLICY "Public can view groups for shared links" ON public.friend_groups
FOR SELECT USING (true);

-- Add a policy for authenticated users to manage their own groups
DROP POLICY IF EXISTS "Users can manage their own groups" ON public.friend_groups;
CREATE POLICY "Users can manage their own groups" ON public.friend_groups
FOR ALL USING (auth.uid() = created_by);

-- Add policy to allow public access to players in shared groups
ALTER TABLE public.players ENABLE ROW LEVEL SECURITY;

-- First, temporarily disable RLS for testing
ALTER TABLE public.players FORCE ROW LEVEL SECURITY;

DROP POLICY IF EXISTS "Public can view players in shared groups" ON public.players;
CREATE POLICY "Public can view players in shared groups" ON public.players
FOR SELECT USING (true);

-- Add policy to allow public access to matches in shared groups
ALTER TABLE public.matches ENABLE ROW LEVEL SECURITY;

-- First, temporarily disable RLS for testing
ALTER TABLE public.matches FORCE ROW LEVEL SECURITY;

DROP POLICY IF EXISTS "Public can view matches in shared groups" ON public.matches;
CREATE POLICY "Public can view matches in shared groups" ON public.matches
FOR SELECT USING (true);

-- Add policy to allow public access to chemistry in shared groups
ALTER TABLE public.chemistry ENABLE ROW LEVEL SECURITY;

-- First, temporarily disable RLS for testing
ALTER TABLE public.chemistry FORCE ROW LEVEL SECURITY;

DROP POLICY IF EXISTS "Public can view chemistry in shared groups" ON public.chemistry;
CREATE POLICY "Public can view chemistry in shared groups" ON public.chemistry
FOR SELECT USING (true);

-- Add policy to allow public access to match comments in shared groups with comment access
ALTER TABLE public.match_comments ENABLE ROW LEVEL SECURITY;

-- First, temporarily disable RLS for testing
ALTER TABLE public.match_comments FORCE ROW LEVEL SECURITY;

DROP POLICY IF EXISTS "Public can view match comments in shared groups" ON public.match_comments;
CREATE POLICY "Public can view match comments in shared groups" ON public.match_comments
FOR SELECT USING (true);

-- Add policy to allow adding comments for shared links with comment access
DROP POLICY IF EXISTS "Public can add comments for shared links with comment access" ON public.match_comments;
CREATE POLICY "Public can add comments for shared links with comment access" ON public.match_comments
FOR INSERT WITH CHECK (true);
