-- Create a function to check if a shared link exists
-- This function bypasses R<PERSON> and can be used for diagnostic purposes
CREATE OR REPLACE FUNCTION public.check_shared_link_exists(link_id uuid)
RETURNS json
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  result json;
BEGIN
  SELECT json_build_object(
    'exists', (SELECT EXISTS(SELECT 1 FROM public.shared_links WHERE id = link_id)),
    'link', (SELECT row_to_json(sl) FROM public.shared_links sl WHERE id = link_id),
    'group_id', (SELECT group_id FROM public.shared_links WHERE id = link_id),
    'group', (SELECT row_to_json(fg) FROM public.friend_groups fg WHERE id = (SELECT group_id FROM public.shared_links WHERE id = link_id))
  ) INTO result;
  
  RETURN result;
END;
$$;

-- Grant execute permission to anon and authenticated roles
GRANT EXECUTE ON FUNCTION public.check_shared_link_exists(uuid) TO anon;
GRANT EXECUTE ON FUNCTION public.check_shared_link_exists(uuid) TO authenticated;

-- Create a function to check RLS policies on shared_links table
CREATE OR REPLACE FUNCTION public.check_shared_links_policies()
RETURNS json
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  result json;
BEGIN
  SELECT json_build_object(
    'table_exists', (SELECT EXISTS(SELECT 1 FROM pg_tables WHERE schemaname = 'public' AND tablename = 'shared_links')),
    'policies', (SELECT json_agg(row_to_json(p)) FROM pg_policies p WHERE schemaname = 'public' AND tablename = 'shared_links'),
    'rls_enabled', (SELECT relrowsecurity FROM pg_class WHERE relname = 'shared_links' AND relnamespace = (SELECT oid FROM pg_namespace WHERE nspname = 'public'))
  ) INTO result;
  
  RETURN result;
END;
$$;

-- Grant execute permission to anon and authenticated roles
GRANT EXECUTE ON FUNCTION public.check_shared_links_policies() TO anon;
GRANT EXECUTE ON FUNCTION public.check_shared_links_policies() TO authenticated;
