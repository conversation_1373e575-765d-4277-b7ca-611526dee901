-- Temporarily disable <PERSON><PERSON> for testing
ALTER TABLE public.shared_links DISABLE ROW LEVEL SECURITY;
ALTER TABLE public.friend_groups DISABLE ROW LEVEL SECURITY;
ALTER TABLE public.players DISABLE ROW LEVEL SECURITY;
ALTER TABLE public.matches DISABLE ROW LEVEL SECURITY;
ALTER TABLE public.chemistry DISABLE ROW LEVEL SECURITY;
ALTER TABLE public.match_comments DISABLE ROW LEVEL SECURITY;

-- Create a function to toggle RLS for all tables
CREATE OR REPLACE FUNCTION public.toggle_rls_for_all_tables(enable boolean)
RETURNS void
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  IF enable THEN
    -- Enable RLS for all tables
    ALTER TABLE public.shared_links ENABLE ROW LEVEL SECURITY;
    ALTER TABLE public.friend_groups ENABLE ROW LEVEL SECURITY;
    ALTER TABLE public.players ENABLE ROW LEVEL SECURITY;
    ALTER TABLE public.matches ENABLE ROW LEVEL SECURITY;
    ALTER TABLE public.chemistry ENABLE ROW LEVEL SECURITY;
    ALTER TABLE public.match_comments ENABLE ROW LEVEL SECURITY;
  ELSE
    -- Disable RLS for all tables
    ALTER TABLE public.shared_links DISABLE ROW LEVEL SECURITY;
    ALTER TABLE public.friend_groups DISABLE ROW LEVEL SECURITY;
    ALTER TABLE public.players DISABLE ROW LEVEL SECURITY;
    ALTER TABLE public.matches DISABLE ROW LEVEL SECURITY;
    ALTER TABLE public.chemistry DISABLE ROW LEVEL SECURITY;
    ALTER TABLE public.match_comments DISABLE ROW LEVEL SECURITY;
  END IF;
END;
$$;

-- Grant execute permission to authenticated users
GRANT EXECUTE ON FUNCTION public.toggle_rls_for_all_tables(boolean) TO authenticated;
