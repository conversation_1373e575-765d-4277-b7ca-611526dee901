-- Function to check if a link exists (bypasses <PERSON><PERSON>)
CREATE OR <PERSON><PERSON><PERSON>CE FUNCTION public.check_link_exists(link_id UUID)
RETURNS BOOLEAN
SECURITY DEFINER
SET search_path = public
AS $$
BEGIN
  RETURN EXISTS (
    SELECT 1 FROM public.shared_links WHERE id = link_id
  );
END;
$$ LANGUAGE plpgsql;

-- <PERSON> execute permission to authenticated and anon users
GRANT EXECUTE ON FUNCTION public.check_link_exists TO authenticated, anon;