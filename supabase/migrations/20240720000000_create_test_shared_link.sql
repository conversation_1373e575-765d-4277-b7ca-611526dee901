-- Create a test shared link for testing
-- First, check if there are any groups
DO $$
DECLARE
  group_id UUID;
  user_id UUID;
BEGIN
  -- Get the first group
  SELECT id INTO group_id FROM public.friend_groups LIMIT 1;
  
  -- Get the first user
  SELECT id INTO user_id FROM auth.users LIMIT 1;
  
  -- If we have a group and a user, create a test shared link
  IF group_id IS NOT NULL AND user_id IS NOT NULL THEN
    -- Check if the test link already exists
    IF NOT EXISTS (SELECT 1 FROM public.shared_links WHERE name = 'Test Shared Link') THEN
      INSERT INTO public.shared_links (
        group_id,
        created_by,
        name,
        access_level,
        is_active
      ) VALUES (
        group_id,
        user_id,
        'Test Shared Link',
        'read',
        true
      );
      
      RAISE NOTICE 'Created test shared link for group % and user %', group_id, user_id;
    ELSE
      RAISE NOTICE 'Test shared link already exists';
    END IF;
  ELSE
    RAISE NOTICE 'No groups or users found, cannot create test shared link';
  END IF;
END $$;
