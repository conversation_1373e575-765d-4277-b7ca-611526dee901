-- Create a function to safely look up a user ID by email
-- This function needs to be run in your Supabase SQL editor
CREATE OR REPLACE FUNCTION public.find_user_id_by_email(email_to_find TEXT)
RETURNS UUID
LANGUAGE plpgsql
SECURITY DEFINER -- This runs with the privileges of the function creator
SET search_path = public
AS $$
DECLARE
    user_id UUID;
BEGIN
    -- Look up the user ID from auth.users
    SELECT id INTO user_id
    FROM auth.users
    WHERE email = email_to_find;
    
    RETURN user_id;
END;
$$;

-- Grant execute permission to authenticated users
GRANT EXECUTE ON FUNCTION public.find_user_id_by_email(TEXT) TO authenticated;

-- Create a simpler fallback function that doesn't require direct auth.users access
CREATE OR REPLACE FUNCTION public.get_current_user_id()
RETURNS UUID
LANGUAGE sql
SECURITY DEFINER
AS $$
    SELECT auth.uid();
$$;

-- Grant execute permission to authenticated users
GRANT EXECUTE ON FUNCTION public.get_current_user_id() TO authenticated;
