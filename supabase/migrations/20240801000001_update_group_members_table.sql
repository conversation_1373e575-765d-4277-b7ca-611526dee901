-- Add email_invite and status columns to group_members table
ALTER TABLE public.group_members
ADD COLUMN IF NOT EXISTS email_invite TEXT,
ADD COLUMN IF NOT EXISTS status TEXT DEFAULT 'active' CHECK (status IN ('active', 'pending', 'declined'));

-- Update the user_id column to be nullable
ALTER TABLE public.group_members
ALTER COLUMN user_id DROP NOT NULL;

-- Add a constraint to ensure either user_id or email_invite is provided
ALTER TABLE public.group_members
ADD CONSTRAINT user_id_or_email_invite_required
CHECK (
  (user_id IS NOT NULL AND email_invite IS NULL) OR
  (user_id IS NULL AND email_invite IS NOT NULL)
);

-- Create a function to handle user registration and link pending invites
CREATE OR REPLACE FUNCTION public.handle_new_user() 
RETURNS trigger AS $$
BEGIN
  -- Find any pending invites for this user's email and update them
  UPDATE public.group_members
  SET user_id = NEW.id, 
      status = 'active',
      email_invite = NULL
  WHERE email_invite = NEW.email AND status = 'pending';
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create a trigger to run the function when a new user is created
DROP TRIGGER IF EXISTS on_auth_user_created ON auth.users;
CREATE TRIGGER on_auth_user_created
  AFTER INSERT ON auth.users
  FOR EACH ROW EXECUTE FUNCTION public.handle_new_user();
