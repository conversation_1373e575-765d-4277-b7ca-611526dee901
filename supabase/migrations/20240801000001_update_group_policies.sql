-- Migration to update group policies for multi-admin support

-- Update policy for friend_groups to allow <PERSON><PERSON> to manage groups
DROP POLICY IF EXISTS "Group Creator Full Access" ON friend_groups;
CREATE POLICY "Group Admin Full Access"
ON friend_groups
FOR ALL
USING (
  auth.uid() = created_by
  OR EXISTS (
    SELECT 1 FROM group_members
    WHERE group_id = friend_groups.id
    AND user_id = auth.uid()
    AND role = 'Admin'
  )
);

-- Update policy for group_members to allow <PERSON><PERSON> to manage memberships
DROP POLICY IF EXISTS "Admins Manage Memberships" ON group_members;
CREATE POLICY "Admins Manage Memberships"
ON group_members
FOR ALL
USING (
  EXISTS (
    SELECT 1 FROM group_members AS gm
    WHERE gm.group_id = group_members.group_id
    AND gm.user_id = auth.uid()
    AND gm.role = 'Admin'
  )
  OR EXISTS (
    SELECT 1 FROM friend_groups
    WHERE id = group_members.group_id
    AND created_by = auth.uid()
  )
);

-- Update policies for players table
DROP POLICY IF EXISTS "Users can view players in their groups" ON public.players;
CREATE POLICY "Users can view players in their groups" ON public.players
FOR SELECT USING (
  group_id IN (
    SELECT group_id FROM group_members WHERE user_id = auth.uid()
    UNION
    SELECT id FROM friend_groups WHERE created_by = auth.uid()
  )
);

DROP POLICY IF EXISTS "Users can modify players in their groups" ON public.players;
CREATE POLICY "Users can modify players in their groups" ON public.players
FOR ALL USING (
  group_id IN (
    SELECT group_id FROM group_members 
    WHERE user_id = auth.uid() 
    AND role IN ('Admin', 'Collaborator')
    UNION
    SELECT id FROM friend_groups WHERE created_by = auth.uid()
  )
);

-- Update policies for matches table
DROP POLICY IF EXISTS "Users can view matches in their groups" ON public.matches;
CREATE POLICY "Users can view matches in their groups" ON public.matches
FOR SELECT USING (
  group_id IN (
    SELECT group_id FROM group_members WHERE user_id = auth.uid()
    UNION
    SELECT id FROM friend_groups WHERE created_by = auth.uid()
  )
);

DROP POLICY IF EXISTS "Users can modify matches in their groups" ON public.matches;
CREATE POLICY "Users can modify matches in their groups" ON public.matches
FOR ALL USING (
  group_id IN (
    SELECT group_id FROM group_members 
    WHERE user_id = auth.uid() 
    AND role IN ('Admin', 'Collaborator')
    UNION
    SELECT id FROM friend_groups WHERE created_by = auth.uid()
  )
);

-- Update policies for chemistry table
DROP POLICY IF EXISTS "Users can view chemistry in their groups" ON public.chemistry;
CREATE POLICY "Users can view chemistry in their groups" ON public.chemistry
FOR SELECT USING (
  group_id IN (
    SELECT group_id FROM group_members WHERE user_id = auth.uid()
    UNION
    SELECT id FROM friend_groups WHERE created_by = auth.uid()
  )
);

DROP POLICY IF EXISTS "Users can modify chemistry in their groups" ON public.chemistry;
CREATE POLICY "Users can modify chemistry in their groups" ON public.chemistry
FOR ALL USING (
  group_id IN (
    SELECT group_id FROM group_members 
    WHERE user_id = auth.uid() 
    AND role IN ('Admin', 'Collaborator')
    UNION
    SELECT id FROM friend_groups WHERE created_by = auth.uid()
  )
);

-- Update policies for match_comments table
DROP POLICY IF EXISTS "Users can view comments in their groups" ON public.match_comments;
CREATE POLICY "Users can view comments in their groups" ON public.match_comments
FOR SELECT USING (
  group_id IN (
    SELECT group_id FROM group_members WHERE user_id = auth.uid()
    UNION
    SELECT id FROM friend_groups WHERE created_by = auth.uid()
  )
);

DROP POLICY IF EXISTS "Users can insert comments in their groups" ON public.match_comments;
CREATE POLICY "Users can insert comments in their groups" ON public.match_comments
FOR INSERT WITH CHECK (
  group_id IN (
    SELECT group_id FROM group_members 
    WHERE user_id = auth.uid() 
    AND role IN ('Admin', 'Collaborator', 'Guest')
    UNION
    SELECT id FROM friend_groups WHERE created_by = auth.uid()
  )
);

-- Create a function to check user's role in a group
CREATE OR REPLACE FUNCTION public.get_user_role_in_group(group_id UUID, user_id UUID)
RETURNS TEXT AS $$
DECLARE
    user_role TEXT;
BEGIN
    -- Check if user is the creator of the group
    IF EXISTS (
        SELECT 1 FROM friend_groups
        WHERE id = group_id AND created_by = user_id
    ) THEN
        RETURN 'Admin';
    END IF;
    
    -- Check if user is a member of the group
    SELECT role INTO user_role
    FROM group_members
    WHERE group_id = group_id AND user_id = user_id;
    
    RETURN user_role;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create a function to check if user can perform an action in a group
CREATE OR REPLACE FUNCTION public.can_user_perform_action(
    group_id UUID, 
    user_id UUID,
    required_role TEXT
)
RETURNS BOOLEAN AS $$
DECLARE
    user_role TEXT;
BEGIN
    user_role := public.get_user_role_in_group(group_id, user_id);
    
    IF user_role IS NULL THEN
        RETURN FALSE;
    END IF;
    
    IF required_role = 'Admin' THEN
        RETURN user_role = 'Admin';
    ELSIF required_role = 'Collaborator' THEN
        RETURN user_role IN ('Admin', 'Collaborator');
    ELSIF required_role = 'Guest' THEN
        RETURN user_role IN ('Admin', 'Collaborator', 'Guest');
    END IF;
    
    RETURN FALSE;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
