-- Create user_invitations table for tracking invitations
CREATE TABLE IF NOT EXISTS public.user_invitations (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    group_id UUID REFERENCES friend_groups(id) ON DELETE CASCADE NOT NULL,
    invited_email TEXT NOT NULL,
    invited_by UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
    role TEXT CHECK (role IN ('Admin', 'Collaborator', 'Guest')) NOT NULL,
    status TEXT DEFAULT 'pending' CHECK (status IN ('pending', 'accepted', 'declined')),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now()) NOT NULL,
    UNIQUE(group_id, invited_email)
);

-- Enable RLS
ALTER TABLE public.user_invitations ENABLE ROW LEVEL SECURITY;

-- Create policies
CREATE POLICY "Users can view invitations they created"
ON public.user_invitations
FOR SELECT
USING (invited_by = auth.uid());

CREATE POLICY "Users can create invitations for groups they created"
ON public.user_invitations
FOR INSERT
WITH CHECK (
    invited_by = auth.uid() AND
    EXISTS (
        SELECT 1 FROM friend_groups
        WHERE id = group_id AND created_by = auth.uid()
    )
);

CREATE POLICY "Users can update invitations they created"
ON public.user_invitations
FOR UPDATE
USING (invited_by = auth.uid());

CREATE POLICY "Users can delete invitations they created"
ON public.user_invitations
FOR DELETE
USING (invited_by = auth.uid());
