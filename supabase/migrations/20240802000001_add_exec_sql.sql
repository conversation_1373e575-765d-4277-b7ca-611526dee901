-- Create a function to execute SQL statements with admin privileges
-- This function should only be callable by service role or admin users
CREATE OR REPLACE FUNCTION public.exec_sql(sql text)
RETURNS void
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  EXECUTE sql;
END;
$$;

-- Restrict access to this function to only service role
REVOKE ALL ON FUNCTION public.exec_sql FROM PUBLIC;
REVOKE ALL ON FUNCTION public.exec_sql FROM anon;
REVOKE ALL ON FUNCTION public.exec_sql FROM authenticated;

-- Only allow service_role to execute this function
-- This is handled by Supabase automatically for service role requests
