-- Drop the existing function first to avoid return type errors
DROP FUNCTION IF EXISTS public.get_user_details_by_id(UUID);

-- Create a function to safely get user details by ID
-- This function needs to be run in your Supabase SQL editor
CREATE FUNCTION public.get_user_details_by_id(user_id_to_find UUID)
RETURNS JSON
LANGUAGE plpgsql
SECURITY DEFINER -- This runs with the privileges of the function creator
SET search_path = public
AS $$
DECLARE
    result JSON;
BEGIN
    SELECT
        json_build_object(
            'email', u.email::TEXT,
            'display_name', COALESCE(u.raw_user_meta_data->>'full_name', '')::TEXT
        ) INTO result
    FROM
        auth.users u
    WHERE
        u.id = user_id_to_find;

    RETURN result;
END;
$$;

-- Grant execute permission to authenticated users
GRANT EXECUTE ON FUNCTION public.get_user_details_by_id(UUID) TO authenticated;

[auth.email.smtp]
enabled = true
host = "smtp.resend.com"
port = 587
user = "resend"
pass = "env(RESEND_API_KEY)"
admin_email = "<EMAIL>"
sender_name = "Soccer Stats"
