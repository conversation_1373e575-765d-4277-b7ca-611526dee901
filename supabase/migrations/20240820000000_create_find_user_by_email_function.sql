-- Create a function to safely look up a user ID by email
-- This function needs to be run in your Supabase SQL editor
CREATE OR REPLACE FUNCTION public.find_user_id_by_email(email_to_find TEXT)
RETURNS UUID
LANGUAGE plpgsql
SECURITY DEFINER -- This runs with the privileges of the function creator
SET search_path = public
AS $$
DECLARE
    user_id UUID;
BEGIN
    -- Look up the user ID from auth.users
    SELECT id INTO user_id
    FROM auth.users
    WHERE email = email_to_find;
    
    RETURN user_id;
END;
$$;

-- Grant execute permission to authenticated users
GRANT EXECUTE ON FUNCTION public.find_user_id_by_email(TEXT) TO authenticated;
