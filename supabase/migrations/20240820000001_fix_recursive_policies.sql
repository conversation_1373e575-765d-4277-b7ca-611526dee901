-- Migration to fix recursive policy issues in friend_groups and group_members tables

-- Drop all potentially problematic policies
DROP POLICY IF EXISTS "Group Creator Full Access" ON friend_groups;
DROP POLICY IF EXISTS "Group Admin Full Access" ON friend_groups;
DROP POLICY IF EXISTS "Group Members Can View Groups" ON friend_groups;
DROP POLICY IF EXISTS "Members View Groups" ON friend_groups;

DROP POLICY IF EXISTS "View Own Memberships" ON group_members;
DROP POLICY IF EXISTS "Admins Manage Memberships" ON group_members;

-- Create simplified policies for friend_groups

-- 1. Allow creators to manage their groups
CREATE POLICY "Creators Manage Groups"
ON friend_groups
FOR ALL
USING (created_by = auth.uid());

-- 2. Allow members to view groups they belong to
CREATE POLICY "Members View Groups"
ON friend_groups
FOR SELECT
USING (
  id IN (
    SELECT group_id FROM group_members
    WHERE user_id = auth.uid()
  )
);

-- Create simplified policies for group_members

-- 1. Allow users to view their own memberships
CREATE POLICY "View Own Memberships"
ON group_members
FOR SELECT
USING (user_id = auth.uid());

-- 2. Allow group creators to manage all memberships in their groups
CREATE POLICY "Creators Manage All Memberships"
ON group_members
FOR ALL
USING (
  group_id IN (
    SELECT id FROM friend_groups
    WHERE created_by = auth.uid()
  )
);

-- 3. Allow admins to manage memberships in groups where they are admins
CREATE POLICY "Admins Manage Group Memberships"
ON group_members
FOR ALL
USING (
  group_id IN (
    SELECT group_id FROM group_members
    WHERE user_id = auth.uid()
    AND role = 'Admin'
  )
);
