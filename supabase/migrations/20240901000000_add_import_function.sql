-- Create a function to handle group creation with data import in a single transaction
CREATE OR REPLACE FUNCTION create_group_with_import(
  group_name TEXT,
  user_id UUID,
  import_data JSONB
)
RETURNS SETOF friend_groups AS $$
DECLARE
  new_group_id UUID;
  player_data JSONB;
  match_data JSONB;
  player_record RECORD;
  match_record RECORD;
  player_ids INTEGER[];
  created_players INTEGER[];
  player_id INTEGER;
  teama_ids INTEGER[];
  teamb_ids INTEGER[];
  updated_goalscorers JSONB;
  goalscorer JSONB;
  player_index INTEGER;
  real_player_id INTEGER;
  i INTEGER;
  goalscorers_array JSONB[];
BEGIN
  -- Start transaction
  BEGIN
    -- 1. Create the group
    INSERT INTO friend_groups (name, created_by)
    VALUES (group_name, user_id)
    RETURNING id INTO new_group_id;

    -- 2. Add the creator as an Admin
    INSERT INTO group_members (group_id, user_id, role)
    VALUES (new_group_id, user_id, 'Admin');

    -- 3. Import players and create a mapping between player names and IDs
    -- This ensures that player references in matches are correctly maintained
    -- regardless of the order in which players are stored

    -- Create a temporary table to store player names and their IDs
    CREATE TEMP TABLE temp_player_mapping (
      player_index INTEGER,
      player_name TEXT,
      player_id INTEGER
    );

    -- Use a counter to track the original index of each player
    i := 0;

    -- First pass: insert players and record their IDs
    FOR player_record IN
      SELECT * FROM jsonb_to_recordset(import_data->'players')
      AS x(name TEXT, skills INTEGER, effort INTEGER, stamina INTEGER)
    LOOP
      -- Insert the player
      INSERT INTO players (name, skills, effort, stamina, group_id, metadata)
      VALUES (
        player_record.name,
        player_record.skills,
        player_record.effort,
        player_record.stamina,
        new_group_id,
        jsonb_build_object('original_index', i, 'import_name', player_record.name)
      )
      RETURNING id INTO player_id;

      -- Store the mapping between index, name and ID
      INSERT INTO temp_player_mapping (player_index, player_name, player_id)
      VALUES (i, player_record.name, player_id);

      -- Increment the counter for the next player
      i := i + 1;
    END LOOP;

    -- For backward compatibility, also create the array of player IDs
    SELECT array_agg(player_id ORDER BY player_index) INTO created_players
    FROM temp_player_mapping;

    -- 4. Import matches with player ID mapping
    FOR match_record IN
      SELECT * FROM jsonb_to_recordset(import_data->'matches')
      AS x(
        match_date TIMESTAMPTZ,
        teama INTEGER[],
        teamb INTEGER[],
        scorea INTEGER,
        scoreb INTEGER,
        winner TEXT,
        goalscorers JSONB,
        youtubelink TEXT
      )
    LOOP
      -- Map team A player indices to actual player IDs using the mapping table
      teama_ids := ARRAY[]::INTEGER[];
      IF match_record.teama IS NOT NULL THEN
        FOREACH i IN ARRAY match_record.teama
        LOOP
          -- Look up the player ID from the mapping table
          SELECT player_id INTO real_player_id
          FROM temp_player_mapping
          WHERE player_index = i;

          -- If found, add to the team array
          IF real_player_id IS NOT NULL THEN
            teama_ids := array_append(teama_ids, real_player_id);
          END IF;
        END LOOP;
      END IF;

      -- Map team B player indices to actual player IDs using the mapping table
      teamb_ids := ARRAY[]::INTEGER[];
      IF match_record.teamb IS NOT NULL THEN
        FOREACH i IN ARRAY match_record.teamb
        LOOP
          -- Look up the player ID from the mapping table
          SELECT player_id INTO real_player_id
          FROM temp_player_mapping
          WHERE player_index = i;

          -- If found, add to the team array
          IF real_player_id IS NOT NULL THEN
            teamb_ids := array_append(teamb_ids, real_player_id);
          END IF;
        END LOOP;
      END IF;

      -- Map goalscorers player indices to actual player IDs using the mapping table
      updated_goalscorers := '[]'::JSONB;
      IF match_record.goalscorers IS NOT NULL AND jsonb_array_length(match_record.goalscorers) > 0 THEN
        FOR i IN 0..jsonb_array_length(match_record.goalscorers)-1
        LOOP
          goalscorer := match_record.goalscorers->i;
          player_index := (goalscorer->>'playerId')::INTEGER;

          -- Look up the player ID from the mapping table
          SELECT player_id INTO real_player_id
          FROM temp_player_mapping
          WHERE player_index = (goalscorer->>'playerId')::INTEGER;

          -- If found, create updated goalscorer with real player ID
          IF real_player_id IS NOT NULL THEN
            updated_goalscorers := updated_goalscorers || jsonb_build_object(
              'team', goalscorer->>'team',
              'playerId', real_player_id
            );
          END IF;
        END LOOP;
      END IF;

      -- Insert the match with mapped player IDs
      INSERT INTO matches (
        match_date,
        teama,
        teamb,
        scorea,
        scoreb,
        winner,
        goalscorers,
        youtubelink,
        group_id
      )
      VALUES (
        match_record.match_date,
        teama_ids,
        teamb_ids,
        match_record.scorea,
        match_record.scoreb,
        match_record.winner,
        updated_goalscorers,
        match_record.youtubelink,
        new_group_id
      );
    END LOOP;

    -- Clean up the temporary table
    DROP TABLE temp_player_mapping;

    -- Return the created group
    RETURN QUERY SELECT * FROM friend_groups WHERE id = new_group_id;
  EXCEPTION
    WHEN OTHERS THEN
      -- Roll back the transaction on error
      RAISE;
  END;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Add appropriate permissions
GRANT EXECUTE ON FUNCTION create_group_with_import TO authenticated;
