-- Migration to fix recursive policy issues in friend_groups and group_members tables

-- First, drop all potentially problematic policies
DROP POLICY IF EXISTS "Group Creator Full Access" ON friend_groups;
DROP POLICY IF EXISTS "Group Admin Full Access" ON friend_groups;
DROP POLICY IF EXISTS "Group Members Can View Groups" ON friend_groups;
DROP POLICY IF EXISTS "Members View Groups" ON friend_groups;
DROP POLICY IF EXISTS "Creators Manage Groups" ON friend_groups;

DROP POLICY IF EXISTS "View Own Memberships" ON group_members;
DROP POLICY IF EXISTS "Admins Manage Memberships" ON group_members;
DROP POLICY IF EXISTS "Creators Manage All Memberships" ON group_members;
DROP POLICY IF EXISTS "Admins Manage Group Memberships" ON group_members;

-- Create non-recursive policies for friend_groups

-- 1. Allow users to view groups they created (no recursion)
CREATE POLICY "Creators View Own Groups"
ON friend_groups
FOR SELECT
USING (created_by = auth.uid());

-- 2. Allow users to manage groups they created (no recursion)
CREATE POLICY "Creators Manage Own Groups"
ON friend_groups
FOR ALL
USING (created_by = auth.uid());

-- 3. Allow users to view groups they are members of (simplified query)
CREATE POLICY "Members View Groups"
ON friend_groups
FOR SELECT
USING (
  EXISTS (
    SELECT 1 FROM group_members
    WHERE group_members.group_id = friend_groups.id
    AND group_members.user_id = auth.uid()
  )
);

-- Create non-recursive policies for group_members

-- 1. Allow users to view their own memberships (no recursion)
CREATE POLICY "Users View Own Memberships"
ON group_members
FOR SELECT
USING (user_id = auth.uid());

-- 2. Allow group creators to manage all memberships in their groups (no recursion)
CREATE POLICY "Creators Manage Group Memberships"
ON group_members
FOR ALL
USING (
  EXISTS (
    SELECT 1 FROM friend_groups
    WHERE friend_groups.id = group_members.group_id
    AND friend_groups.created_by = auth.uid()
  )
);

-- 3. Allow admins to view memberships in groups where they are admins (simplified)
CREATE POLICY "Admins View Group Memberships"
ON group_members
FOR SELECT
USING (
  EXISTS (
    SELECT 1 FROM group_members AS gm
    WHERE gm.group_id = group_members.group_id
    AND gm.user_id = auth.uid()
    AND gm.role = 'Admin'
  )
);

-- 4. Allow admins to insert/update/delete memberships (separate policies to avoid recursion)
CREATE POLICY "Admins Insert Group Memberships"
ON group_members
FOR INSERT
WITH CHECK (
  EXISTS (
    SELECT 1 FROM group_members AS gm
    WHERE gm.group_id = group_members.group_id
    AND gm.user_id = auth.uid()
    AND gm.role = 'Admin'
  )
);

CREATE POLICY "Admins Update Group Memberships"
ON group_members
FOR UPDATE
USING (
  EXISTS (
    SELECT 1 FROM group_members AS gm
    WHERE gm.group_id = group_members.group_id
    AND gm.user_id = auth.uid()
    AND gm.role = 'Admin'
  )
);

CREATE POLICY "Admins Delete Group Memberships"
ON group_members
FOR DELETE
USING (
  EXISTS (
    SELECT 1 FROM group_members AS gm
    WHERE gm.group_id = group_members.group_id
    AND gm.user_id = auth.uid()
    AND gm.role = 'Admin'
  )
);

-- Update policies for other tables to use simplified queries

-- Players table
DROP POLICY IF EXISTS "Users can view players in their groups" ON public.players;
CREATE POLICY "Users can view players in their groups" ON public.players
FOR SELECT USING (
  -- Direct creator check
  EXISTS (
    SELECT 1 FROM friend_groups
    WHERE friend_groups.id = players.group_id
    AND friend_groups.created_by = auth.uid()
  )
  OR
  -- Member check
  EXISTS (
    SELECT 1 FROM group_members
    WHERE group_members.group_id = players.group_id
    AND group_members.user_id = auth.uid()
  )
);

DROP POLICY IF EXISTS "Users can modify players in their groups" ON public.players;
CREATE POLICY "Users can modify players in their groups" ON public.players
FOR ALL USING (
  -- Direct creator check
  EXISTS (
    SELECT 1 FROM friend_groups
    WHERE friend_groups.id = players.group_id
    AND friend_groups.created_by = auth.uid()
  )
  OR
  -- Admin/Collaborator check
  EXISTS (
    SELECT 1 FROM group_members
    WHERE group_members.group_id = players.group_id
    AND group_members.user_id = auth.uid()
    AND group_members.role IN ('Admin', 'Collaborator')
  )
);

-- Matches table
DROP POLICY IF EXISTS "Users can view matches in their groups" ON public.matches;
CREATE POLICY "Users can view matches in their groups" ON public.matches
FOR SELECT USING (
  -- Direct creator check
  EXISTS (
    SELECT 1 FROM friend_groups
    WHERE friend_groups.id = matches.group_id
    AND friend_groups.created_by = auth.uid()
  )
  OR
  -- Member check
  EXISTS (
    SELECT 1 FROM group_members
    WHERE group_members.group_id = matches.group_id
    AND group_members.user_id = auth.uid()
  )
);

DROP POLICY IF EXISTS "Users can modify matches in their groups" ON public.matches;
CREATE POLICY "Users can modify matches in their groups" ON public.matches
FOR ALL USING (
  -- Direct creator check
  EXISTS (
    SELECT 1 FROM friend_groups
    WHERE friend_groups.id = matches.group_id
    AND friend_groups.created_by = auth.uid()
  )
  OR
  -- Admin/Collaborator check
  EXISTS (
    SELECT 1 FROM group_members
    WHERE group_members.group_id = matches.group_id
    AND group_members.user_id = auth.uid()
    AND group_members.role IN ('Admin', 'Collaborator')
  )
);

-- Chemistry table
DROP POLICY IF EXISTS "Users can view chemistry in their groups" ON public.chemistry;
CREATE POLICY "Users can view chemistry in their groups" ON public.chemistry
FOR SELECT USING (
  -- Direct creator check
  EXISTS (
    SELECT 1 FROM friend_groups
    WHERE friend_groups.id = chemistry.group_id
    AND friend_groups.created_by = auth.uid()
  )
  OR
  -- Member check
  EXISTS (
    SELECT 1 FROM group_members
    WHERE group_members.group_id = chemistry.group_id
    AND group_members.user_id = auth.uid()
  )
);

DROP POLICY IF EXISTS "Users can modify chemistry in their groups" ON public.chemistry;
CREATE POLICY "Users can modify chemistry in their groups" ON public.chemistry
FOR ALL USING (
  -- Direct creator check
  EXISTS (
    SELECT 1 FROM friend_groups
    WHERE friend_groups.id = chemistry.group_id
    AND friend_groups.created_by = auth.uid()
  )
  OR
  -- Admin/Collaborator check
  EXISTS (
    SELECT 1 FROM group_members
    WHERE group_members.group_id = chemistry.group_id
    AND group_members.user_id = auth.uid()
    AND group_members.role IN ('Admin', 'Collaborator')
  )
);

-- Match comments table
DROP POLICY IF EXISTS "Users can view comments in their groups" ON public.match_comments;
CREATE POLICY "Users can view comments in their groups" ON public.match_comments
FOR SELECT USING (
  -- Direct creator check
  EXISTS (
    SELECT 1 FROM friend_groups
    WHERE friend_groups.id = match_comments.group_id
    AND friend_groups.created_by = auth.uid()
  )
  OR
  -- Member check
  EXISTS (
    SELECT 1 FROM group_members
    WHERE group_members.group_id = match_comments.group_id
    AND group_members.user_id = auth.uid()
  )
);

DROP POLICY IF EXISTS "Users can insert comments in their groups" ON public.match_comments;
CREATE POLICY "Users can insert comments in their groups" ON public.match_comments
FOR INSERT WITH CHECK (
  -- Direct creator check
  EXISTS (
    SELECT 1 FROM friend_groups
    WHERE friend_groups.id = match_comments.group_id
    AND friend_groups.created_by = auth.uid()
  )
  OR
  -- Member check (all roles can comment)
  EXISTS (
    SELECT 1 FROM group_members
    WHERE group_members.group_id = match_comments.group_id
    AND group_members.user_id = auth.uid()
  )
);
