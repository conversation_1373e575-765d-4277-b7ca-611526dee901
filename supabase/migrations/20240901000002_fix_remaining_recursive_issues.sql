-- Migration to fix any remaining recursive policy issues

-- First, check for any remaining problematic policies on friend_groups
DROP POLICY IF EXISTS "friend_groups_policy" ON friend_groups;

-- Check for any remaining problematic policies on group_members
DROP POLICY IF EXISTS "group_members_policy" ON group_members;

-- Fix potential issues with the Admins policies on group_members
-- The current policies might still have a circular dependency

-- Drop the potentially problematic admin policies
DROP POLICY IF EXISTS "Admins View Group Memberships" ON group_members;
DROP POLICY IF EXISTS "Admins Insert Group Memberships" ON group_members;
DROP POLICY IF EXISTS "Admins Update Group Memberships" ON group_members;
DROP POLICY IF EXISTS "Admins Delete Group Memberships" ON group_members;

-- Create new admin policies with a different approach that avoids recursion
-- Instead of checking if the user is an admin in the same group,
-- we'll use a subquery that gets the user's admin groups first

-- Create a function to get groups where a user is an admin
CREATE OR REPLACE FUNCTION get_admin_group_ids(user_uuid UUID)
RETURNS SETOF UUID AS $$
BEGIN
  RETURN QUERY
  SELECT group_id FROM group_members
  WHERE user_id = user_uuid
  AND role = 'Admin';
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create new admin policies using the function
CREATE POLICY "Admins View Group Memberships V2"
ON group_members
FOR SELECT
USING (
  group_id IN (SELECT get_admin_group_ids(auth.uid()))
);

CREATE POLICY "Admins Insert Group Memberships V2"
ON group_members
FOR INSERT
WITH CHECK (
  group_id IN (SELECT get_admin_group_ids(auth.uid()))
);

CREATE POLICY "Admins Update Group Memberships V2"
ON group_members
FOR UPDATE
USING (
  group_id IN (SELECT get_admin_group_ids(auth.uid()))
);

CREATE POLICY "Admins Delete Group Memberships V2"
ON group_members
FOR DELETE
USING (
  group_id IN (SELECT get_admin_group_ids(auth.uid()))
);

-- Update the policies for other tables to use the function as well

-- Players table
DROP POLICY IF EXISTS "Users can modify players in their groups" ON public.players;
CREATE POLICY "Users can modify players in their groups" ON public.players
FOR ALL USING (
  -- Direct creator check
  EXISTS (
    SELECT 1 FROM friend_groups
    WHERE friend_groups.id = players.group_id
    AND friend_groups.created_by = auth.uid()
  )
  OR
  -- Admin/Collaborator check using the function for admins
  (
    players.group_id IN (SELECT get_admin_group_ids(auth.uid()))
    OR
    EXISTS (
      SELECT 1 FROM group_members
      WHERE group_members.group_id = players.group_id
      AND group_members.user_id = auth.uid()
      AND group_members.role = 'Collaborator'
    )
  )
);

-- Matches table
DROP POLICY IF EXISTS "Users can modify matches in their groups" ON public.matches;
CREATE POLICY "Users can modify matches in their groups" ON public.matches
FOR ALL USING (
  -- Direct creator check
  EXISTS (
    SELECT 1 FROM friend_groups
    WHERE friend_groups.id = matches.group_id
    AND friend_groups.created_by = auth.uid()
  )
  OR
  -- Admin/Collaborator check using the function for admins
  (
    matches.group_id IN (SELECT get_admin_group_ids(auth.uid()))
    OR
    EXISTS (
      SELECT 1 FROM group_members
      WHERE group_members.group_id = matches.group_id
      AND group_members.user_id = auth.uid()
      AND group_members.role = 'Collaborator'
    )
  )
);

-- Chemistry table
DROP POLICY IF EXISTS "Users can modify chemistry in their groups" ON public.chemistry;
CREATE POLICY "Users can modify chemistry in their groups" ON public.chemistry
FOR ALL USING (
  -- Direct creator check
  EXISTS (
    SELECT 1 FROM friend_groups
    WHERE friend_groups.id = chemistry.group_id
    AND friend_groups.created_by = auth.uid()
  )
  OR
  -- Admin/Collaborator check using the function for admins
  (
    chemistry.group_id IN (SELECT get_admin_group_ids(auth.uid()))
    OR
    EXISTS (
      SELECT 1 FROM group_members
      WHERE group_members.group_id = chemistry.group_id
      AND group_members.user_id = auth.uid()
      AND group_members.role = 'Collaborator'
    )
  )
);
