-- Complete rebuild of all RLS policies from scratch
-- This script drops all existing policies, disables <PERSON><PERSON> temporarily,
-- then creates new simplified policies and re-enables RLS

-- Step 1: Drop ALL existing policies on relevant tables
DROP POLICY IF EXISTS "Group Creator Full Access" ON friend_groups;
DROP POLICY IF EXISTS "Group Admin Full Access" ON friend_groups;
DROP POLICY IF EXISTS "Group Members Can View Groups" ON friend_groups;
DROP POLICY IF EXISTS "Members View Groups" ON friend_groups;
DROP POLICY IF EXISTS "Creators Manage Groups" ON friend_groups;
DROP POLICY IF EXISTS "Creators View Own Groups" ON friend_groups;
DROP POLICY IF EXISTS "Creators Manage Own Groups" ON friend_groups;
DROP POLICY IF EXISTS "friend_groups_policy" ON friend_groups;

DROP POLICY IF EXISTS "View Own Memberships" ON group_members;
DROP POLICY IF EXISTS "Admins Manage Memberships" ON group_members;
DROP POLICY IF EXISTS "Creators Manage All Memberships" ON group_members;
DROP POLICY IF EXISTS "Admins Manage Group Memberships" ON group_members;
DROP POLICY IF EXISTS "Users View Own Memberships" ON group_members;
DROP POLICY IF EXISTS "Creators Manage Group Memberships" ON group_members;
DROP POLICY IF EXISTS "Admins View Group Memberships" ON group_members;
DROP POLICY IF EXISTS "Admins Insert Group Memberships" ON group_members;
DROP POLICY IF EXISTS "Admins Update Group Memberships" ON group_members;
DROP POLICY IF EXISTS "Admins Delete Group Memberships" ON group_members;
DROP POLICY IF EXISTS "Admins View Group Memberships V2" ON group_members;
DROP POLICY IF EXISTS "Admins Insert Group Memberships V2" ON group_members;
DROP POLICY IF EXISTS "Admins Update Group Memberships V2" ON group_members;
DROP POLICY IF EXISTS "Admins Delete Group Memberships V2" ON group_members;
DROP POLICY IF EXISTS "group_members_policy" ON group_members;

DROP POLICY IF EXISTS "Users can view players in their groups" ON players;
DROP POLICY IF EXISTS "Users can modify players in their groups" ON players;

DROP POLICY IF EXISTS "Users can view matches in their groups" ON matches;
DROP POLICY IF EXISTS "Users can modify matches in their groups" ON matches;

DROP POLICY IF EXISTS "Users can view chemistry in their groups" ON chemistry;
DROP POLICY IF EXISTS "Users can modify chemistry in their groups" ON chemistry;

DROP POLICY IF EXISTS "Users can view comments in their groups" ON match_comments;
DROP POLICY IF EXISTS "Users can insert comments in their groups" ON match_comments;

-- Step 2: Drop any helper functions that might be causing issues
DROP FUNCTION IF EXISTS get_admin_group_ids;

-- Step 3: Temporarily disable RLS on all tables
ALTER TABLE friend_groups DISABLE ROW LEVEL SECURITY;
ALTER TABLE group_members DISABLE ROW LEVEL SECURITY;
ALTER TABLE players DISABLE ROW LEVEL SECURITY;
ALTER TABLE matches DISABLE ROW LEVEL SECURITY;
ALTER TABLE chemistry DISABLE ROW LEVEL SECURITY;
ALTER TABLE match_comments DISABLE ROW LEVEL SECURITY;

-- Step 4: Create new simplified helper functions
-- This function returns groups where a user is a member (any role)
CREATE OR REPLACE FUNCTION user_member_groups(user_uuid UUID)
RETURNS SETOF UUID AS $$
BEGIN
  RETURN QUERY
  SELECT group_id FROM group_members
  WHERE user_id = user_uuid;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- This function returns groups where a user is an admin
CREATE OR REPLACE FUNCTION user_admin_groups(user_uuid UUID)
RETURNS SETOF UUID AS $$
BEGIN
  RETURN QUERY
  SELECT group_id FROM group_members
  WHERE user_id = user_uuid
  AND role = 'Admin';
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- This function returns groups where a user is an admin or collaborator
CREATE OR REPLACE FUNCTION user_editor_groups(user_uuid UUID)
RETURNS SETOF UUID AS $$
BEGIN
  RETURN QUERY
  SELECT group_id FROM group_members
  WHERE user_id = user_uuid
  AND role IN ('Admin', 'Collaborator');
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- This function returns groups created by a user
CREATE OR REPLACE FUNCTION user_created_groups(user_uuid UUID)
RETURNS SETOF UUID AS $$
BEGIN
  RETURN QUERY
  SELECT id FROM friend_groups
  WHERE created_by = user_uuid;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Step 5: Re-enable RLS on all tables
ALTER TABLE friend_groups ENABLE ROW LEVEL SECURITY;
ALTER TABLE group_members ENABLE ROW LEVEL SECURITY;
ALTER TABLE players ENABLE ROW LEVEL SECURITY;
ALTER TABLE matches ENABLE ROW LEVEL SECURITY;
ALTER TABLE chemistry ENABLE ROW LEVEL SECURITY;
ALTER TABLE match_comments ENABLE ROW LEVEL SECURITY;

-- Step 6: Create new simplified policies for friend_groups
-- Policy for viewing groups you created
CREATE POLICY "View Created Groups"
ON friend_groups
FOR SELECT
USING (created_by = auth.uid());

-- Policy for managing groups you created
CREATE POLICY "Manage Created Groups"
ON friend_groups
FOR ALL
USING (created_by = auth.uid());

-- Policy for viewing groups you're a member of
CREATE POLICY "View Member Groups"
ON friend_groups
FOR SELECT
USING (id IN (SELECT user_member_groups(auth.uid())));

-- Step 7: Create new simplified policies for group_members
-- Policy for viewing your own memberships
CREATE POLICY "View Own Memberships"
ON group_members
FOR SELECT
USING (user_id = auth.uid());

-- Policy for viewing memberships in groups you created
CREATE POLICY "View Memberships In Created Groups"
ON group_members
FOR SELECT
USING (group_id IN (SELECT user_created_groups(auth.uid())));

-- Policy for managing memberships in groups you created
CREATE POLICY "Manage Memberships In Created Groups"
ON group_members
FOR ALL
USING (group_id IN (SELECT user_created_groups(auth.uid())));

-- Policy for viewing memberships in groups where you're an admin
CREATE POLICY "View Memberships As Admin"
ON group_members
FOR SELECT
USING (group_id IN (SELECT user_admin_groups(auth.uid())));

-- Policy for managing memberships in groups where you're an admin
CREATE POLICY "Manage Memberships As Admin"
ON group_members
FOR ALL
USING (group_id IN (SELECT user_admin_groups(auth.uid())));

-- Step 8: Create new simplified policies for players
-- Policy for viewing players in groups you're a member of
CREATE POLICY "View Players In Member Groups"
ON players
FOR SELECT
USING (
  group_id IN (SELECT user_member_groups(auth.uid()))
  OR
  group_id IN (SELECT user_created_groups(auth.uid()))
);

-- Policy for managing players in groups where you're an editor (admin or collaborator)
CREATE POLICY "Manage Players As Editor"
ON players
FOR ALL
USING (
  group_id IN (SELECT user_editor_groups(auth.uid()))
  OR
  group_id IN (SELECT user_created_groups(auth.uid()))
);

-- Step 9: Create new simplified policies for matches
-- Policy for viewing matches in groups you're a member of
CREATE POLICY "View Matches In Member Groups"
ON matches
FOR SELECT
USING (
  group_id IN (SELECT user_member_groups(auth.uid()))
  OR
  group_id IN (SELECT user_created_groups(auth.uid()))
);

-- Policy for managing matches in groups where you're an editor (admin or collaborator)
CREATE POLICY "Manage Matches As Editor"
ON matches
FOR ALL
USING (
  group_id IN (SELECT user_editor_groups(auth.uid()))
  OR
  group_id IN (SELECT user_created_groups(auth.uid()))
);

-- Step 10: Create new simplified policies for chemistry
-- Policy for viewing chemistry in groups you're a member of
CREATE POLICY "View Chemistry In Member Groups"
ON chemistry
FOR SELECT
USING (
  group_id IN (SELECT user_member_groups(auth.uid()))
  OR
  group_id IN (SELECT user_created_groups(auth.uid()))
);

-- Policy for managing chemistry in groups where you're an editor (admin or collaborator)
CREATE POLICY "Manage Chemistry As Editor"
ON chemistry
FOR ALL
USING (
  group_id IN (SELECT user_editor_groups(auth.uid()))
  OR
  group_id IN (SELECT user_created_groups(auth.uid()))
);

-- Step 11: Create new simplified policies for match_comments
-- Policy for viewing comments in groups you're a member of
CREATE POLICY "View Comments In Member Groups"
ON match_comments
FOR SELECT
USING (
  group_id IN (SELECT user_member_groups(auth.uid()))
  OR
  group_id IN (SELECT user_created_groups(auth.uid()))
);

-- Policy for adding comments in groups you're a member of (any role)
CREATE POLICY "Add Comments In Member Groups"
ON match_comments
FOR INSERT
WITH CHECK (
  group_id IN (SELECT user_member_groups(auth.uid()))
  OR
  group_id IN (SELECT user_created_groups(auth.uid()))
);

-- Policy for managing your own comments
CREATE POLICY "Manage Own Comments"
ON match_comments
FOR ALL
USING (user_id = auth.uid());

-- Step 12: Add policies for user_invitations if needed
DROP POLICY IF EXISTS "Users can view invitations they created" ON user_invitations;
DROP POLICY IF EXISTS "Users can create invitations for groups they created" ON user_invitations;
DROP POLICY IF EXISTS "Users can update invitations they created" ON user_invitations;
DROP POLICY IF EXISTS "Users can delete invitations they created" ON user_invitations;

CREATE POLICY "View Own Invitations"
ON user_invitations
FOR SELECT
USING (invited_by = auth.uid());

CREATE POLICY "Create Invitations For Created Groups"
ON user_invitations
FOR INSERT
WITH CHECK (
  invited_by = auth.uid() 
  AND
  group_id IN (SELECT user_created_groups(auth.uid()))
);

CREATE POLICY "Create Invitations As Admin"
ON user_invitations
FOR INSERT
WITH CHECK (
  invited_by = auth.uid() 
  AND
  group_id IN (SELECT user_admin_groups(auth.uid()))
);

CREATE POLICY "Manage Own Invitations"
ON user_invitations
FOR UPDATE
USING (invited_by = auth.uid());

CREATE POLICY "Delete Own Invitations"
ON user_invitations
FOR DELETE
USING (invited_by = auth.uid());

-- Step 13: Add policies for user_preferences
DROP POLICY IF EXISTS "Users can view their own preferences" ON user_preferences;
DROP POLICY IF EXISTS "Users can insert their own preferences" ON user_preferences;
DROP POLICY IF EXISTS "Users can update their own preferences" ON user_preferences;
DROP POLICY IF EXISTS "Users can delete their own preferences" ON user_preferences;

CREATE POLICY "View Own Preferences"
ON user_preferences
FOR SELECT
USING (user_id = auth.uid());

CREATE POLICY "Insert Own Preferences"
ON user_preferences
FOR INSERT
WITH CHECK (user_id = auth.uid());

CREATE POLICY "Update Own Preferences"
ON user_preferences
FOR UPDATE
USING (user_id = auth.uid());

CREATE POLICY "Delete Own Preferences"
ON user_preferences
FOR DELETE
USING (user_id = auth.uid());
