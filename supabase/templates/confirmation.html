<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <title>Fulbito Stats - Confirm Your Email | Confirma tu Correo</title>
  <style>
    body {
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif;
      line-height: 1.6;
      color: #333;
      margin: 0;
      padding: 0;
    }
    .container {
      max-width: 600px;
      margin: 0 auto;
      padding: 20px;
    }
    .header {
      background-color: #35db71;
      color: white;
      padding: 20px;
      text-align: center;
      border-radius: 5px 5px 0 0;
    }
    .content {
      background-color: #fff;
      padding: 20px;
      border: 1px solid #ddd;
      border-top: none;
      border-radius: 0 0 5px 5px;
    }
    .button {
      display: inline-block;
      background-color: #35db71;
      color: white;
      padding: 10px 20px;
      text-decoration: none;
      border-radius: 4px;
      margin: 20px 0;
    }
    .footer {
      text-align: center;
      margin-top: 20px;
      font-size: 12px;
      color: #666;
    }
  </style>
</head>
<body>
  <div class="container">
    <div class="header">
      <h1>Fulbito Stats</h1>
    </div>
    <div class="content">
      <h2>Confirm Your Email Address | Confirma tu Dirección de Correo</h2>

      <!-- English Version -->
      <div>
        <p>Hello,</p>
        <p>Thank you for signing up for Fulbito Stats! Please confirm your email address by clicking the button below:</p>
        <p><a href="{{ .SiteURL }}/auth/confirm?token={{ .Token }}&type=signup&redirect_to=/login" class="button">Confirm Email Address</a></p>
        <p>If you didn't create an account with us, you can safely ignore this email.</p>
        <p>Thank you,<br>Fulbito Stats Team</p>
      </div>

      <hr style="margin: 30px 0; border-top: 1px solid #ddd;">

      <!-- Spanish Version -->
      <div>
        <p>Hola,</p>
        <p>¡Gracias por registrarte en Fulbito Stats! Por favor, confirma tu dirección de correo electrónico haciendo clic en el botón de abajo:</p>
        <p><a href="{{ .SiteURL }}/auth/confirm?token={{ .Token }}&type=signup&redirect_to=/login" class="button">Confirmar Correo Electrónico</a></p>
        <p>Si no creaste una cuenta con nosotros, puedes ignorar este correo electrónico.</p>
        <p>Gracias,<br>Equipo de Fulbito Stats</p>
      </div>
    </div>
    <div class="footer">
      <p>If you did not request this confirmation, please ignore this email. | Si no solicitaste esta confirmación, por favor ignora este correo.</p>
      <p>&copy; 2024 Fulbito Stats. All rights reserved. | Todos los derechos reservados.</p>
    </div>
  </div>
</body>
</html>
