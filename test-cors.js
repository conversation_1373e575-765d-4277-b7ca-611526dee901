// Simple script to test CORS with Supabase
const supabaseUrl = process.env.VITE_SUPABASE_URL || 'https://jbljfsvvskbbyxftqlkg.supabase.co';
const supabaseAnonKey = process.env.VITE_SUPABASE_ANON_KEY;

console.log('Testing CORS with Supabase...');
console.log(`URL: ${supabaseUrl}`);

fetch(`${supabaseUrl}/auth/v1/token?grant_type=password`, {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'apikey': supabaseAnonKey,
  },
  body: JSON.stringify({
    email: '<EMAIL>',
    password: 'password',
  }),
})
.then(response => {
  console.log('Response status:', response.status);
  return response.json().catch(() => null);
})
.then(data => {
  console.log('Response data:', data);
})
.catch(error => {
  console.error('Error:', error);
});
