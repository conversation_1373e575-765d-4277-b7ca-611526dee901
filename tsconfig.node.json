{"compilerOptions": {"composite": true, "target": "ES2022", "lib": ["ES2023"], "module": "ESNext", "skipLibCheck": true, "moduleResolution": "node", "allowSyntheticDefaultImports": true, "resolveJsonModule": true, "isolatedModules": true, "moduleDetection": "force", "strict": true, "noUnusedLocals": false, "noUnusedParameters": false, "noFallthroughCasesInSwitch": true}, "include": ["vite.config.ts"]}