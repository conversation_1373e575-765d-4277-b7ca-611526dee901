import json
import sys
from datetime import datetime

def validate_player(player, index):
    errors = []
    
    # Check required fields
    for field in ['name', 'skills', 'effort', 'stamina']:
        if field not in player:
            errors.append(f"Player {index}: Missing required field '{field}'")
    
    # Validate name
    if 'name' in player:
        if not isinstance(player['name'], str):
            errors.append(f"Player {index}: 'name' must be a string")
        elif len(player['name']) < 2 or len(player['name']) > 50:
            errors.append(f"Player {index}: 'name' must be between 2 and 50 characters")
    
    # Validate numeric fields
    for field in ['skills', 'effort', 'stamina']:
        if field in player:
            if not isinstance(player[field], (int, float)):
                errors.append(f"Player {index}: '{field}' must be a number")
            elif player[field] < 0 or player[field] > 100:
                errors.append(f"Player {index}: '{field}' must be between 0 and 100")
    
    return errors

def validate_match(match, index, player_count):
    errors = []
    
    # Check required fields
    for field in ['match_date', 'teama', 'teamb']:
        if field not in match:
            errors.append(f"Match {index}: Missing required field '{field}'")
    
    # Validate match_date
    if 'match_date' in match:
        if not isinstance(match['match_date'], str):
            errors.append(f"Match {index}: 'match_date' must be a string")
        else:
            try:
                datetime.fromisoformat(match['match_date'].replace('Z', '+00:00'))
            except ValueError:
                errors.append(f"Match {index}: 'match_date' must be a valid ISO 8601 date string")
    
    # Validate teams
    for team_field in ['teama', 'teamb']:
        if team_field in match:
            if not isinstance(match[team_field], list):
                errors.append(f"Match {index}: '{team_field}' must be an array")
            elif len(match[team_field]) < 1 or len(match[team_field]) > 11:
                errors.append(f"Match {index}: '{team_field}' must have between 1 and 11 players")
            else:
                for player_idx in match[team_field]:
                    if not isinstance(player_idx, int):
                        errors.append(f"Match {index}: Player indices in '{team_field}' must be integers")
                    elif player_idx < 0 or player_idx >= player_count:
                        errors.append(f"Match {index}: Player index {player_idx} in '{team_field}' is out of range (0-{player_count-1})")
    
    # Validate scores
    for score_field in ['scorea', 'scoreb']:
        if score_field in match and match[score_field] is not None:
            if not isinstance(match[score_field], int):
                errors.append(f"Match {index}: '{score_field}' must be an integer or null")
            elif match[score_field] < 0 or match[score_field] > 99:
                errors.append(f"Match {index}: '{score_field}' must be between 0 and 99")
    
    # Validate winner
    if 'winner' in match and match['winner'] is not None:
        if match['winner'] not in ['A', 'B', 'Draw']:
            errors.append(f"Match {index}: 'winner' must be 'A', 'B', 'Draw', or null")
    
    # Validate goalscorers
    if 'goalscorers' in match:
        if not isinstance(match['goalscorers'], list):
            errors.append(f"Match {index}: 'goalscorers' must be an array")
        else:
            for i, scorer in enumerate(match['goalscorers']):
                if not isinstance(scorer, dict):
                    errors.append(f"Match {index}, Goalscorer {i}: Must be an object")
                else:
                    if 'team' not in scorer:
                        errors.append(f"Match {index}, Goalscorer {i}: Missing 'team'")
                    elif scorer['team'] not in ['A', 'B']:
                        errors.append(f"Match {index}, Goalscorer {i}: 'team' must be 'A' or 'B'")
                    
                    if 'playerId' not in scorer:
                        errors.append(f"Match {index}, Goalscorer {i}: Missing 'playerId'")
                    elif not isinstance(scorer['playerId'], int):
                        errors.append(f"Match {index}, Goalscorer {i}: 'playerId' must be an integer")
                    elif scorer['playerId'] < 0 or scorer['playerId'] >= player_count:
                        errors.append(f"Match {index}, Goalscorer {i}: 'playerId' {scorer['playerId']} is out of range (0-{player_count-1})")
    
    # Validate youtubelink
    if 'youtubelink' in match:
        if match['youtubelink'] is not None and match['youtubelink'] != "" and not isinstance(match['youtubelink'], str):
            errors.append(f"Match {index}: 'youtubelink' must be a string, null, or empty string")
    
    return errors

def validate_import_data(data):
    errors = []
    
    # Check required sections
    for section in ['players', 'matches']:
        if section not in data:
            errors.append(f"Missing required section '{section}'")
            return errors  # Can't continue validation without this section
    
    # Validate players
    if not isinstance(data['players'], list):
        errors.append("'players' must be an array")
    else:
        for i, player in enumerate(data['players']):
            errors.extend(validate_player(player, i))
    
    # Validate matches
    if not isinstance(data['matches'], list):
        errors.append("'matches' must be an array")
    else:
        player_count = len(data['players'])
        for i, match in enumerate(data['matches']):
            errors.extend(validate_match(match, i, player_count))
    
    return errors

def main():
    if len(sys.argv) != 2:
        print("Usage: python validate_json.py <json_file>")
        sys.exit(1)
    
    json_file = sys.argv[1]
    
    try:
        with open(json_file, 'r', encoding='utf-8') as f:
            data = json.load(f)
    except Exception as e:
        print(f"Error reading JSON file: {e}")
        sys.exit(1)
    
    errors = validate_import_data(data)
    
    if errors:
        print(f"Found {len(errors)} validation errors:")
        for error in errors:
            print(f"- {error}")
        sys.exit(1)
    else:
        print("Validation successful!")
        print(f"Players: {len(data['players'])}")
        print(f"Matches: {len(data['matches'])}")
        sys.exit(0)

if __name__ == "__main__":
    main()
